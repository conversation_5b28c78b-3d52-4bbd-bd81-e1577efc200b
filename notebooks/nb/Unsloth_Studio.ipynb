{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "Nz4odU5XYDDw"}, "outputs": [], "source": ["# @title ↙️ Press ▶ to start 🦥 Unsloth Studio Chat for Llama-3.1 8b\n", "\n", "# Unsloth Studio\n", "# Copyright (C) 2024-present the Unsloth AI team. All rights reserved.\n", "\n", "# This program is free software: you can redistribute it and/or modify\n", "# it under the terms of the GNU Affero General Public License as published\n", "# by the Free Software Foundation, either version 3 of the License, or\n", "# (at your option) any later version.\n", "\n", "# This program is distributed in the hope that it will be useful,\n", "# but WITHOUT ANY WARRANTY; without even the implied warranty of\n", "# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n", "# GNU Affero General Public License for more details.\n", "\n", "# You should have received a copy of the GNU Affero General Public License\n", "# along with this program.  If not, see <https://www.gnu.org/licenses/>.\n", "!git clone https://github.com/unslothai/studio > /dev/null 2>&1\n", "with open(\"studio/unsloth_studio/chat.py\", \"r\") as chat_module:\n", "    code = chat_module.read()\n", "exec(code)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 0}