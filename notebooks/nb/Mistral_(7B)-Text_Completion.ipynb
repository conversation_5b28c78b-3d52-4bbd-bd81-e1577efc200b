{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth\nelse:\n    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1,<4.0.0\" \"huggingface_hub>=0.34.0\" hf_transfer\n    !pip install --no-deps unsloth"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Text Completion / Raw Text Training\n", "This is a community notebook collaboration with [Mithex].\n", "\n", "We train on `Tiny Stories` (link [here](https://huggingface.co/datasets/roneneldan/TinyStories)) which is a collection of small stories. For example:\n", "```\n", "Once upon a time, there was a little car named <PERSON><PERSON>. <PERSON><PERSON> loved to go fast and play in the sun.\n", "<PERSON><PERSON> was a healthy car because he always had good fuel....\n", "```\n", "Instead of `Alpaca`'s Question Answer format, one only needs 1 column - the `\"text\"` column. This means you can finetune on any dataset and let your model act as a text completion model, like for novel writing.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%env UNSLOTH_RETURN_LOGITS=1 # Run this to disable CCE since it is not supported for CPT"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 367, "referenced_widgets": ["23649ef1df704457919de08ecda081ba", "d95b3e48f249411e894317fdfcb0bcd1", "147548f4823c475b9c2a82253872f4cb", "b3e7c261642342f18977fafeca64813a", "d9d8e6d24894416ea481442dc33eec80", "2a18a32dda174d1a85ed7a2f94ff0be3", "14e68c95472d489f8fe9f0c9e38d5457", "d88233a81e7040eca0004111026d289b", "842ebeab10d4481cb8dd38b5eca033f2", "a2a71fa3e7e84b7d8df5d700f0810259", "14e2670d4c684fe2ad9d640249cec8fd", "5b58ca1e80e445ccafb8149cd239be09", "c268be6fb1de415ca9e6c55411ef85f7", "06033b10b015449a958138ba21d591f3", "420b0f94c0cb422dbb22b90ba7cebe7a", "8bfc0c5e41744c68bb41a171c662a667", "3f73fe9b55864ae5b7622dda8653572a", "2b9bbf400d4440468ef51a7af36f273e", "29db539cae9b4dd4a51c855784562eed", "96d6ffe4553b4e8aaac5d8a575ac1da9", "c9577eafe3a0491ba5cbd72c464f6874", "20e4cbfb9ee64eda80bd5283c68277ea", "c347faf6eb114732b5dc009be3636ebc", "cf0c656eebbe4b9c88b25ce0449c16c3", "e0adda358fee41a2a25a76b963aff3c8", "b41c8750cece4911addefdc1940f693b", "810d0d3ffc164d6a88834224bc2f4a3c", "9a23522084a8488c8010f341944bf736", "ab17d129478f47d8959d72708786aa92", "fa684d9d809f4d708fea1de165c2d3c9", "52c09ed521ba49949bbc786974a2560b", "b6700fcb58894ac39d06415589b35779", "6d0fc50735744f639492dfa52df812e1", "233d6fcb495d40288530d63bf35f6cfc", "1afe84fe63ee40a68db92ae38143f509", "b3f431ac6c82432f9fa5e691fb5c7915", "34113a79134e4389899c74a8016eb9f9", "7c99023a81de4fcebd718f787f920d95", "4691ac2c19814ddca82b557bf1abf49c", "4a8eea3fe65d41069793e29df02aeb61", "799b6b7a228f49cbad3d47bb26f16d46", "39bf8ed45e7a4234a209cb1f15fa5165", "5289267a77944768a3feae8c0a60edc6", "4e4e0d27644c4a3e9642ec4a4e48f9da", "a592d0c788454eea947b033197751c50", "ee71e78be5d9406ea6b686d17ebb6d7d", "33eaf6a087a34e19bc1657dde249c786", "e8a7b3d1c6b34b92a5aa99bddacbba35", "55195af15d6243bb9659e3aecc345fe5", "68467f5d0bac4d488e333969dae954f8", "2cee2854a26e46e090825953f1e7dd69", "3142b77c414f4c0b96e769e7099c0ff0", "90be2ee8e1934cc99d973e025c0d5a7c", "3893ed578d5b4a90b26bc2abad30719a", "747078e706ba4028ae480dd512367887", "0fbdc37811d44365a331922ab1146da3", "fe09ec6a787546e1afd1699180b5e30b", "095d836f57bd47a9b6accd25abacb9ec", "15cb1089b0a74f5ba2c50e674f6e88b0", "2363232fdc674b8bb46d1c6df6bc710b", "bdd07cd097bb4c67bd581ec046cc8bb7", "3c313a02db614be8aabbe5ee619f6f57", "4a3c4a73eaea44ba9639013abafe8f65", "ee76fb24b834429dbeb3320caf9aa755", "d873326dcc854da4866b8aa31e30a6d0", "925a44a71ef340ce97452c40ca3860c8", "387ec2c16d074e2c86f0b95f553e0584", "d73a947a9e084457a509046a976ad907", "e9061bc2704a4771bfe4d1e3661c1d37", "9304bc57715b480ca80849cfdc455d9e", "115eccc3a42746af9c5f46d3a7216ee1", "7abf7e0b03f34ca1bf3df1cc900c806d", "ad0595973e7b450a8d71838fbc89c8e5", "7067c8908b5b438eb5d2f0f217b2d8b0", "807b2ec5e3d6483387124d3291618e21", "0c596e37e0e549ab9102d2321bc94632", "60523115a0934c42b6d3eaa7b8dbfa5c"]}, "id": "QmUBVEnvCDJv", "outputId": "fc0acf60-7b53-4630-f4b3-89e169e2e99e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23649ef1df704457919de08ecda081ba", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.15k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Mistral patching release 2024.5\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.3.0+cu121. CUDA = 7.5. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. Xformers = 0.0.26.post1. FA = False.\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b58ca1e80e445ccafb8149cd239be09", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/4.14G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c347faf6eb114732b5dc009be3636ebc", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/111 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "233d6fcb495d40288530d63bf35f6cfc", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/137k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a592d0c788454eea947b033197751c50", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/587k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0fbdc37811d44365a331922ab1146da3", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/560 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "387ec2c16d074e2c86f0b95f553e0584", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/1.96M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-v0.3-bnb-4bit\",      # New Mistral v3 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/llama-3-8b-bnb-4bit\",           # Llama-3 15 trillion tokens model 2x faster!\n", "    \"unsloth/llama-3-8b-Instruct-bnb-4bit\",\n", "    \"unsloth/llama-3-70b-bnb-4bit\",\n", "    \"unsloth/Phi-3-mini-4k-instruct\",        # Phi-3 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\",             # Gemma 2.2x faster!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/mistral-7b-v0.3\", # \"unsloth/mistral-7b\" for 16bit loading\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!\n", "\n", "We also add `embed_tokens` and `lm_head` to allow the model to learn out of distribution data."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "dbb7269d-e25b-420f-9239-37a63f77b984"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Offloading input_embeddings to disk to save VRAM\n", "Unsloth: Offloading output_embeddings to disk to save VRAM\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.5 patched 32 layers with 32 QKV layers, 32 O layers and 32 MLP layers.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Casting embed_tokens to float32\n", "Unsloth: Casting lm_head to float32\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 128, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",\n", "\n", "                      \"embed_tokens\", \"lm_head\",], # Add for continual pretraining\n", "    lora_alpha = 32,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = True,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the Tiny Stories dataset from https://huggingface.co/datasets/roneneldan/TinyStories. We only sample the first 5000 rows to speed training up. We must add `EOS_TOKEN` or `tokenizer.eos_token` or else the model's generation will go on forever.\n", "\n", "If you want to use the `llama-3` template for ShareGPT datasets, try our conversational [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_v0.3_(7B)-Conversational.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 384, "referenced_widgets": ["b5f8212008ec453fb7289e701debcfc3", "2ea873838f6541e9afd9f19d21da8315", "a34628ecc40745f2948c1c46d732c286", "6d2a2611703c4fd7b3db412e2625be48", "5725e9e7a3ef49799c7ad40804a44545", "c31afd889e2c46cd84ef35b07059845e", "8c6dee64c33b44bda12874f6a54c8d9e", "b58aeeca06fa4fb38009ebcf58a26386", "387fa6b0943848f1a8b948559822e205", "df7d00a7f4bf4963a5fd23b7be473960", "2695578369f14c98a3204d473d4fac3b", "9fe034194139457e941f699a57f103af", "6dadfcf52eb74a559431010a6e77d583", "a1cc093e80c647f5a3bd0c723edbe571", "2ae3de4fa73c4b559536bc8ff5ece100", "8f99454387b84a53b3605c04666d071b", "37064a269e744da5875e035a69690885", "982c7a4d956b4b5b9e8ce527e1d9db0c", "caf1b1b2eeec43bea992b8a55a9340ee", "4456475bc1b648e9819534fa9ab8cced", "dba649311a634dce989d634119541df4", "6cf85160c2ad4573837fdff644e11208", "d1848a12767c49b784dacbcd37b1199a", "3d7dac001a3540eeaf8f3cdaf8baea8f", "5391065bacb04614a6226ad3c799df3c", "4993b3837ebe4abd820e94a559fbed4e", "ca2d8e83378f4cda91ab846e10a36fb1", "74231474464249dbbf0ebd0e3d2705ad", "99785a56de774625be94cb7ddc5909b3", "7e5afd994d484b51a4b1ef1523d5969f", "147915d703424306b73b919c26677db8", "ccf22c2bfd0c4e05a6c61ee2a1f073cf", "e715b74c921f4b6b9b1781dc0dacb65a", "6d9202d4ac964bf782e460c182360f36", "6ba7db52a1504ff5b7fad4b9aa37ca47", "2391ad77914e445e8b1b600e3fa96c7c", "685183eeb79542eca5f070a3c2f67122", "cdf10bc17c9b4a48b3531bdf4807ba18", "2f1217b5e45644109b4e0270f2dedd45", "16a14ddb52a345e3befde572795d6a9c", "62a312500e9842a6ae116a7d88a44bfc", "022c7a03f4714ed2ba133d3cd79e9866", "dd48f879b9fd4738a21d99708f5ce40d", "f2a07cbf33044dddb71963636961c443", "44c6504054c54f098486e3435b798f0a", "eb32e24d5ca04fd4837a8d7a39da56bd", "ee38243222f541799f44e1b1c5a47504", "ff27478e1bca41148c5e0fec7fa42843", "96afce833a6a4eba88fe03fa4cbab813", "7c33fe8933e94987af5f45cf4deb976e", "0aa02bcf14524cec95e0f9553015d4e1", "7d6164dec3704a93abd75a1952f343b6", "f49bf15971aa40ea9936c3ccf021157b", "e818ceb7b2a94dd1a8c915c1497e7270", "56bed294aa6d4ac3ba5d7bdf440c26ac", "0aa2ff7cc1824725b985c6fc35e01511", "163cc2596b154a84bc9b82dedb963e18", "d5f8e906b97b4ed493c3a9b69284a020", "54b4c1fbbe844e4898c06617eef31748", "d7b4be775bf842c188d94385b29b9263", "d0f6847ee49244b5b9eeda26754d630d", "793aae2dc0d54ac4b80df65329a650ea", "e7d5fee28a62446d9091b12730ec2801", "24a643daaea341888490005bf97400f8", "2888f04a22084170bafa7fdeed97d52b", "b08f6a63776d4a89ba2ef045ab4902f4", "23897dd1809f4fea9656bdaba9675d21", "a00a2e203bad4e9b941a400e9f88e006", "5d43db671ec34d9bb0743e6859e21cfc", "dbdf9d595d114c94b93dfaad7975f07b", "eedfbef053eb4fdf8ffeabd5b5303c09", "891e2e52835243b2acfd640b23c0071d", "aecf5315643e4518be9cf7b748bd2973", "9c7fd659ddec4c5facba45d7dc8a70a8", "efc96d91544c4408b3935daeb589fe43", "9f76613f1188459889cc43ae9f5f3fa4", "ee33bf4147144744b62c6170732d3523", "461b4893fd4c44fbb1466143343c4b74", "52920e1aaa524949ab4f3e79caba30e5", "3002cae9544549d39f2c3d34cbc667ad", "013632c5019f405dbacfdeefe494dc22", "5b698a8efd7f418d8964d4ed18df2ce8", "e7aeb988266948428b2084fc95248620", "870ea57e268d44d7bbf8f88101ba7d3a", "943d7db8b97e4d5c94fe5373db1e0dfe", "6b7204b65e4e4705b23edd33e6061009", "903b3af5c66e45c5bf7daf6a46134b89", "0f000c0a882d4f079201dcc78a8a899b"]}, "id": "yXt8Na97yRe7", "outputId": "34ef03f0-6018-4c3b-9458-c574de4a216c"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5f8212008ec453fb7289e701debcfc3", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/1.02k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Repo card metadata block was not found. Setting CardData to empty.\n", "WARNING:huggingface_hub.repocard:Repo card metadata block was not found. Setting CardData to empty.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9fe034194139457e941f699a57f103af", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/249M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d1848a12767c49b784dacbcd37b1199a", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/248M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6d9202d4ac964bf782e460c182360f36", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/246M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "44c6504054c54f098486e3435b798f0a", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/248M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0aa2ff7cc1824725b985c6fc35e01511", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/9.99M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23897dd1809f4fea9656bdaba9675d21", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/2119719 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "461b4893fd4c44fbb1466143343c4b74", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating validation split:   0%|          | 0/21990 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"roneneldan/TinyStories\", split = \"train[:2500]\")\n", "EOS_TOKEN = tokenizer.eos_token\n", "def formatting_prompts_func(examples):\n", "    return { \"text\" : [example + EOS_TOKEN for example in examples[\"text\"]] }\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "8ENCwwLaWjud"}, "source": ["Print out 5 stories from `Tiny Stories`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EMv7nqxz6fta", "outputId": "b2ba42c6-8db4-4a39-e263-0ab6e6c92a31"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=========================\n", "One day, a little girl named <PERSON> found a needle in her room. She knew it was difficult to play with it because it was sharp. <PERSON> wanted to share the needle with her mom, so she could sew a button on her shirt.\n", "\n", "<PERSON> went to her mom and said, \"Mom, I found this needle. Can you share it with me and sew my shirt?\" Her mom smiled and said, \"Yes, <PERSON>, we can share the needle and fix your shirt.\"\n", "\n", "Together, they shared the needle and sewed the button on <PERSON>'s shirt. It was not difficult for them because they were sharing and helping each other. After they finished, <PERSON> thanked her mom for sharing the needle and fixing her shirt. They both felt happy because they had shared and worked together.\n", "=========================\n", "Once upon a time, there was a little car named <PERSON><PERSON>. <PERSON><PERSON> loved to go fast and play in the sun. <PERSON><PERSON> was a healthy car because he always had good fuel. Good fuel made <PERSON><PERSON> happy and strong.\n", "\n", "One day, <PERSON><PERSON> was driving in the park when he saw a big tree. The tree had many leaves that were falling. <PERSON><PERSON> liked how the leaves fall and wanted to play with them. <PERSON><PERSON> drove under the tree and watched the leaves fall on him. He laughed and beeped his horn.\n", "\n", "<PERSON><PERSON> played with the falling leaves all day. When it was time to go home, <PERSON><PERSON> knew he needed more fuel. He went to the fuel place and got more healthy fuel. Now, <PERSON><PERSON> was ready to go fast and play again the next day. And <PERSON><PERSON> lived happily ever after.\n", "=========================\n", "One day, a little fish named <PERSON> was swimming near the shore. He saw a big crab and wanted to be friends. \"Hi, I am <PERSON>. Do you want to play?\" asked the little fish. The crab looked at <PERSON> and said, \"No, I don't want to play. I am cold and I don't feel fine.\"\n", "\n", "<PERSON> felt sad but wanted to help the crab feel better. He swam away and thought of a plan. He remembered that the sun could make things warm. So, <PERSON> swam to the top of the water and called to the sun, \"Please, sun, help my new friend feel fine and not freeze!\"\n", "\n", "The sun heard <PERSON>'s call and shone its warm light on the shore. The crab started to feel better and not so cold. He saw <PERSON> and said, \"Thank you, little fish, for making me feel fine. I don't feel like I will freeze now. Let's play together!\" And so, <PERSON> and the crab played and became good friends.\n", "=========================\n", "Once upon a time, in a land full of trees, there was a little cherry tree. The cherry tree was very sad because it did not have any friends. All the other trees were big and strong, but the cherry tree was small and weak. The cherry tree was envious of the big trees.\n", "\n", "One day, the cherry tree felt a tickle in its branches. It was a little spring wind. The wind told the cherry tree not to be sad. The wind said, \"You are special because you have sweet cherries that everyone loves.\" The cherry tree started to feel a little better.\n", "\n", "As time went on, the cherry tree grew more and more cherries. All the animals in the land came to eat the cherries and play under the cherry tree. The cherry tree was happy because it had many friends now. The cherry tree learned that being different can be a good thing. And they all lived happily ever after.\n", "=========================\n", "Once upon a time, there was a little girl named <PERSON>. <PERSON> liked to pretend she was a popular princess. She lived in a big castle with her best friends, a cat and a dog.\n", "\n", "One day, while playing in the castle, <PERSON> found a big cobweb. The cobweb was in the way of her fun game. She wanted to get rid of it, but she was scared of the spider that lived there.\n", "\n", "<PERSON> asked her friends, the cat and the dog, to help her. They all worked together to clean the cobweb. The spider was sad, but it found a new home outside. <PERSON>, the cat, and the dog were happy they could play without the cobweb in the way. And they all lived happily ever after.\n"]}], "source": ["for row in dataset[:5][\"text\"]:\n", "    print(\"=========================\")\n", "    print(row)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Continued Pretraining\n", "Now let's use Unsloth's `UnslothTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 20 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`.\n", "\n", "Also set `embedding_learning_rate` to be a learning rate at least 2x or 10x smaller than `learning_rate` to make continual pretraining work!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["23f7fc20f86f49aa9776202b5661e309", "abe972c95fe945a8bb054ece4745bcc6", "e7b5629c0559481d81d978cd38c4d96e", "41f4262cfb794a87ae66afe28ef2288d", "5d114ee11c234481b023d256c29f2b48", "8ec74735134648aea38b5254cf9d3a73", "5d9f69b0c01747189df2a998bdba2336", "fa86792b494b45eda841dcd30d08cae0", "c47b46aa519f41a086231f67888e8212", "ebab2bac40d7464c850dd16353b7b001", "d244d3ea508f497c96f1e9239e269fe1"]}, "id": "95_Nn-89DhsL", "outputId": "60416521-7c90-4ff9-f598-960559678687"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/multiprocess/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23f7fc20f86f49aa9776202b5661e309", "version_major": 2, "version_minor": 0}, "text/plain": ["Map (num_proc=8):   0%|          | 0/2500 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from trl import SFTTrainer\n", "from transformers import TrainingArguments\n", "from unsloth import UnslothTrainer, UnslothTrainingArguments\n", "\n", "trainer = <PERSON><PERSON><PERSON>hTrainer(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 8,\n", "\n", "    args = UnslothTrainingArguments(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 8,\n", "\n", "        warmup_ratio = 0.1,\n", "        num_train_epochs = 1,\n", "\n", "        learning_rate = 5e-5,\n", "        embedding_learning_rate = 5e-6,\n", "\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.00,\n", "        lr_scheduler_type = \"cosine\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "b27999be-070a-4fce-fa68-6fad7d98fb0e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "6.367 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "ec11f4a5-58ce-4374-fc35-599c4ffe2677"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 2,500 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 8\n", "\\        /    Total batch size = 16 | Total steps = 156\n", " \"-____-\"     Number of trainable parameters = 603,979,776\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Setting lr = 5.00e-06 instead of 5.00e-05 for embed_tokens.\n", "Unsloth: Setting lr = 5.00e-06 instead of 5.00e-05 for lm_head.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='156' max='156' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [156/156 45:59, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.436500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.470800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.464500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.310200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.416000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.368000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.421400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.174200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.173500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.287900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.109300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.197100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.268700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.134900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.386000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.153700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.125500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.152500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.179200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>1.207800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>1.095800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.213400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>1.107600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>1.163800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.311300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.214000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.183200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.281600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.081900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.081300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>1.112900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>1.147900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>1.181000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>1.210600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>1.380400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>1.201400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>1.266000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>1.130800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>1.121300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>1.200900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>1.151000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>1.073600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>1.166200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>1.146400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>1.192300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>1.278000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>1.238400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.160900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>1.102400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.078700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>1.140500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>1.114900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>1.117000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>1.163400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>1.281300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.164900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>1.058300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>1.037000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>1.137100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>1.114700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>61</td>\n", "      <td>1.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>62</td>\n", "      <td>1.139200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>63</td>\n", "      <td>1.150200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>64</td>\n", "      <td>1.180000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>65</td>\n", "      <td>1.201400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>66</td>\n", "      <td>1.012100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>67</td>\n", "      <td>0.992500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>68</td>\n", "      <td>1.196200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>69</td>\n", "      <td>1.166600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>70</td>\n", "      <td>1.165200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>71</td>\n", "      <td>0.988600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>72</td>\n", "      <td>1.026200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>73</td>\n", "      <td>1.112700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>74</td>\n", "      <td>1.158800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>1.183400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>76</td>\n", "      <td>1.024100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>77</td>\n", "      <td>1.086200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>78</td>\n", "      <td>1.180900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>79</td>\n", "      <td>1.129600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>80</td>\n", "      <td>1.208800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>81</td>\n", "      <td>1.117000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>82</td>\n", "      <td>1.157500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>83</td>\n", "      <td>1.053700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>84</td>\n", "      <td>1.145400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>85</td>\n", "      <td>1.072900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>86</td>\n", "      <td>1.176500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>87</td>\n", "      <td>1.054300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>88</td>\n", "      <td>1.017700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>89</td>\n", "      <td>1.169200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>90</td>\n", "      <td>1.282500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>91</td>\n", "      <td>1.287400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>92</td>\n", "      <td>1.230200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>93</td>\n", "      <td>1.153800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>94</td>\n", "      <td>1.219700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>95</td>\n", "      <td>1.069300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>96</td>\n", "      <td>1.075200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>97</td>\n", "      <td>1.157100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>98</td>\n", "      <td>1.073300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>99</td>\n", "      <td>1.213700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>1.205200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>101</td>\n", "      <td>1.156800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>102</td>\n", "      <td>1.036000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>103</td>\n", "      <td>1.214400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>104</td>\n", "      <td>1.002900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>105</td>\n", "      <td>1.207700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>106</td>\n", "      <td>1.185800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>107</td>\n", "      <td>1.034100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>108</td>\n", "      <td>1.149200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>109</td>\n", "      <td>1.182200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>110</td>\n", "      <td>1.197500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>111</td>\n", "      <td>1.173900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>112</td>\n", "      <td>1.089200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>113</td>\n", "      <td>1.136000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>114</td>\n", "      <td>1.112800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>115</td>\n", "      <td>1.111700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>116</td>\n", "      <td>1.158000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>117</td>\n", "      <td>1.157000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>118</td>\n", "      <td>1.031500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>119</td>\n", "      <td>0.984400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>120</td>\n", "      <td>1.158000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>121</td>\n", "      <td>1.202100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>122</td>\n", "      <td>1.084400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>123</td>\n", "      <td>1.093800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>124</td>\n", "      <td>1.053500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>125</td>\n", "      <td>1.137300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>126</td>\n", "      <td>1.085000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>127</td>\n", "      <td>1.070000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>128</td>\n", "      <td>1.134800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>129</td>\n", "      <td>0.979200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>130</td>\n", "      <td>1.144800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>131</td>\n", "      <td>1.078300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>132</td>\n", "      <td>1.094200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>133</td>\n", "      <td>0.982400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>134</td>\n", "      <td>1.097700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>135</td>\n", "      <td>1.082200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>136</td>\n", "      <td>1.034000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>137</td>\n", "      <td>1.140300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>138</td>\n", "      <td>1.015100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>139</td>\n", "      <td>1.231500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>140</td>\n", "      <td>1.020900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>141</td>\n", "      <td>1.083500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>142</td>\n", "      <td>1.060300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>143</td>\n", "      <td>1.072800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>144</td>\n", "      <td>1.241200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>145</td>\n", "      <td>1.153900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>146</td>\n", "      <td>1.237800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>147</td>\n", "      <td>1.202900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>148</td>\n", "      <td>1.047100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>149</td>\n", "      <td>1.083000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>150</td>\n", "      <td>1.140200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>151</td>\n", "      <td>1.208900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>152</td>\n", "      <td>1.142300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>153</td>\n", "      <td>1.083900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>154</td>\n", "      <td>1.108300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>155</td>\n", "      <td>1.209200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>156</td>\n", "      <td>1.016500</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "eed84fa5-b782-4a0c-ab72-2a480cad0f93"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2780.9282 seconds used for training.\n", "46.35 minutes used for training.\n", "Peak reserved memory = 11.432 GB.\n", "Peak reserved memory for training = 5.065 GB.\n", "Peak reserved memory % of max memory = 77.516 %.\n", "Peak reserved memory for training % of max memory = 34.344 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model!\n", "\n", "We first will try to see if the model follows the style and understands to write a story that is within the distribution of \"Tiny Stories\". Ie a story fit for a bed time story most likely.\n", "\n", "We select \"Once upon a time, in a galaxy, far far away,\" since it normally is associated with Star Wars."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RHm11vaQRt1U", "outputId": "b7c3956b-2926-4892-c8f0-f1ecb4015440"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:2 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<s>Once upon a time, in a galaxy, far faraway, there was a little girl named <PERSON>. She loved to \n", "play with her toys and explore the universe. One day, she found a big, shiny rock. She picked it up and \n", "put it in her pocket.\n", "\n", "<PERSON> went to play with her friends, but she forgot about the rock. When she \n", "came back home, she realized that she had lost the rock. She was very sad and started to cry.\n", "\n", "Her mom \n", "saw her crying and asked her what was wrong. <PERSON> told her about the rock and how she lost it. Her mom \n", "said, \"Don't worry, we can find it again.\" They went back to the place where <PERSON> found the rock and \n", "searched for it. After a while, they found the rock and <PERSON> was very happy. She learned that it's \n", "important to take care of her things and not to lose them. The end.</s>"]}], "source": ["from transformers import TextIteratorStreamer\n", "from threading import Thread\n", "text_streamer = TextIteratorStreamer(tokenizer)\n", "import textwrap\n", "max_print_width = 100\n", "\n", "# Before running inference, call `FastLanguageModel.for_inference` first\n", "\n", "FastLanguageModel.for_inference(model)\n", "\n", "inputs = tokenizer(\n", "[\n", "    \"Once upon a time, in a galaxy, far far away,\"\n", "]*1, return_tensors = \"pt\").to(\"cuda\")\n", "\n", "generation_kwargs = dict(\n", "    inputs,\n", "    streamer = text_streamer,\n", "    max_new_tokens = 256,\n", "    use_cache = True,\n", ")\n", "thread = Thread(target = model.generate, kwargs = generation_kwargs)\n", "thread.start()\n", "\n", "length = 0\n", "for j, new_text in enumerate(text_streamer):\n", "    if j == 0:\n", "        wrapped_text = textwrap.wrap(new_text, width = max_print_width)\n", "        length = len(wrapped_text[-1])\n", "        wrapped_text = \"\\n\".join(wrapped_text)\n", "        print(wrapped_text, end = \"\")\n", "    else:\n", "        length += len(new_text)\n", "        if length >= max_print_width:\n", "            length = 0\n", "            print()\n", "        print(new_text, end = \"\")\n", "    pass\n", "pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"013632c5019f405dbacfdeefe494dc22": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_903b3af5c66e45c5bf7daf6a46134b89", "placeholder": "​", "style": "IPY_MODEL_0f000c0a882d4f079201dcc78a8a899b", "value": " 21990/21990 [00:00&lt;00:00, 242316.02 examples/s]"}}, "022c7a03f4714ed2ba133d3cd79e9866": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "06033b10b015449a958138ba21d591f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_29db539cae9b4dd4a51c855784562eed", "max": 4138270821, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_96d6ffe4553b4e8aaac5d8a575ac1da9", "value": 4138270821}}, "095d836f57bd47a9b6accd25abacb9ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4a3c4a73eaea44ba9639013abafe8f65", "max": 560, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ee76fb24b834429dbeb3320caf9aa755", "value": 560}}, "0aa02bcf14524cec95e0f9553015d4e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0aa2ff7cc1824725b985c6fc35e01511": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_163cc2596b154a84bc9b82dedb963e18", "IPY_MODEL_d5f8e906b97b4ed493c3a9b69284a020", "IPY_MODEL_54b4c1fbbe844e4898c06617eef31748"], "layout": "IPY_MODEL_d7b4be775bf842c188d94385b29b9263"}}, "0c596e37e0e549ab9102d2321bc94632": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f000c0a882d4f079201dcc78a8a899b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0fbdc37811d44365a331922ab1146da3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fe09ec6a787546e1afd1699180b5e30b", "IPY_MODEL_095d836f57bd47a9b6accd25abacb9ec", "IPY_MODEL_15cb1089b0a74f5ba2c50e674f6e88b0"], "layout": "IPY_MODEL_2363232fdc674b8bb46d1c6df6bc710b"}}, "115eccc3a42746af9c5f46d3a7216ee1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "147548f4823c475b9c2a82253872f4cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d88233a81e7040eca0004111026d289b", "max": 1148, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_842ebeab10d4481cb8dd38b5eca033f2", "value": 1148}}, "147915d703424306b73b919c26677db8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "14e2670d4c684fe2ad9d640249cec8fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "14e68c95472d489f8fe9f0c9e38d5457": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "15cb1089b0a74f5ba2c50e674f6e88b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d873326dcc854da4866b8aa31e30a6d0", "placeholder": "​", "style": "IPY_MODEL_925a44a71ef340ce97452c40ca3860c8", "value": " 560/560 [00:00&lt;00:00, 43.7kB/s]"}}, "163cc2596b154a84bc9b82dedb963e18": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d0f6847ee49244b5b9eeda26754d630d", "placeholder": "​", "style": "IPY_MODEL_793aae2dc0d54ac4b80df65329a650ea", "value": "Downloading data: 100%"}}, "16a14ddb52a345e3befde572795d6a9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1afe84fe63ee40a68db92ae38143f509": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4691ac2c19814ddca82b557bf1abf49c", "placeholder": "​", "style": "IPY_MODEL_4a8eea3fe65d41069793e29df02aeb61", "value": "tokenizer_config.json: 100%"}}, "20e4cbfb9ee64eda80bd5283c68277ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "233d6fcb495d40288530d63bf35f6cfc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1afe84fe63ee40a68db92ae38143f509", "IPY_MODEL_b3f431ac6c82432f9fa5e691fb5c7915", "IPY_MODEL_34113a79134e4389899c74a8016eb9f9"], "layout": "IPY_MODEL_7c99023a81de4fcebd718f787f920d95"}}, "2363232fdc674b8bb46d1c6df6bc710b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "23649ef1df704457919de08ecda081ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d95b3e48f249411e894317fdfcb0bcd1", "IPY_MODEL_147548f4823c475b9c2a82253872f4cb", "IPY_MODEL_b3e7c261642342f18977fafeca64813a"], "layout": "IPY_MODEL_d9d8e6d24894416ea481442dc33eec80"}}, "23897dd1809f4fea9656bdaba9675d21": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a00a2e203bad4e9b941a400e9f88e006", "IPY_MODEL_5d43db671ec34d9bb0743e6859e21cfc", "IPY_MODEL_dbdf9d595d114c94b93dfaad7975f07b"], "layout": "IPY_MODEL_eedfbef053eb4fdf8ffeabd5b5303c09"}}, "2391ad77914e445e8b1b600e3fa96c7c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_62a312500e9842a6ae116a7d88a44bfc", "max": 245894874, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_022c7a03f4714ed2ba133d3cd79e9866", "value": 245894874}}, "23f7fc20f86f49aa9776202b5661e309": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_abe972c95fe945a8bb054ece4745bcc6", "IPY_MODEL_e7b5629c0559481d81d978cd38c4d96e", "IPY_MODEL_41f4262cfb794a87ae66afe28ef2288d"], "layout": "IPY_MODEL_5d114ee11c234481b023d256c29f2b48"}}, "24a643daaea341888490005bf97400f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2695578369f14c98a3204d473d4fac3b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2888f04a22084170bafa7fdeed97d52b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "29db539cae9b4dd4a51c855784562eed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2a18a32dda174d1a85ed7a2f94ff0be3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ae3de4fa73c4b559536bc8ff5ece100": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dba649311a634dce989d634119541df4", "placeholder": "​", "style": "IPY_MODEL_6cf85160c2ad4573837fdff644e11208", "value": " 249M/249M [00:05&lt;00:00, 77.2MB/s]"}}, "2b9bbf400d4440468ef51a7af36f273e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2cee2854a26e46e090825953f1e7dd69": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2ea873838f6541e9afd9f19d21da8315": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c31afd889e2c46cd84ef35b07059845e", "placeholder": "​", "style": "IPY_MODEL_8c6dee64c33b44bda12874f6a54c8d9e", "value": "Downloading readme: 100%"}}, "2f1217b5e45644109b4e0270f2dedd45": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3002cae9544549d39f2c3d34cbc667ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_943d7db8b97e4d5c94fe5373db1e0dfe", "max": 21990, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6b7204b65e4e4705b23edd33e6061009", "value": 21990}}, "3142b77c414f4c0b96e769e7099c0ff0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33eaf6a087a34e19bc1657dde249c786": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3142b77c414f4c0b96e769e7099c0ff0", "max": 587404, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_90be2ee8e1934cc99d973e025c0d5a7c", "value": 587404}}, "34113a79134e4389899c74a8016eb9f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5289267a77944768a3feae8c0a60edc6", "placeholder": "​", "style": "IPY_MODEL_4e4e0d27644c4a3e9642ec4a4e48f9da", "value": " 137k/137k [00:00&lt;00:00, 670kB/s]"}}, "37064a269e744da5875e035a69690885": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "387ec2c16d074e2c86f0b95f553e0584": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d73a947a9e084457a509046a976ad907", "IPY_MODEL_e9061bc2704a4771bfe4d1e3661c1d37", "IPY_MODEL_9304bc57715b480ca80849cfdc455d9e"], "layout": "IPY_MODEL_115eccc3a42746af9c5f46d3a7216ee1"}}, "387fa6b0943848f1a8b948559822e205": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3893ed578d5b4a90b26bc2abad30719a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "39bf8ed45e7a4234a209cb1f15fa5165": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3c313a02db614be8aabbe5ee619f6f57": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d7dac001a3540eeaf8f3cdaf8baea8f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_74231474464249dbbf0ebd0e3d2705ad", "placeholder": "​", "style": "IPY_MODEL_99785a56de774625be94cb7ddc5909b3", "value": "Downloading data: 100%"}}, "3f73fe9b55864ae5b7622dda8653572a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "41f4262cfb794a87ae66afe28ef2288d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ebab2bac40d7464c850dd16353b7b001", "placeholder": "​", "style": "IPY_MODEL_d244d3ea508f497c96f1e9239e269fe1", "value": " 2500/2500 [00:02&lt;00:00, 1808.64 examples/s]"}}, "420b0f94c0cb422dbb22b90ba7cebe7a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c9577eafe3a0491ba5cbd72c464f6874", "placeholder": "​", "style": "IPY_MODEL_20e4cbfb9ee64eda80bd5283c68277ea", "value": " 4.14G/4.14G [00:55&lt;00:00, 76.0MB/s]"}}, "4456475bc1b648e9819534fa9ab8cced": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "44c6504054c54f098486e3435b798f0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_eb32e24d5ca04fd4837a8d7a39da56bd", "IPY_MODEL_ee38243222f541799f44e1b1c5a47504", "IPY_MODEL_ff27478e1bca41148c5e0fec7fa42843"], "layout": "IPY_MODEL_96afce833a6a4eba88fe03fa4cbab813"}}, "461b4893fd4c44fbb1466143343c4b74": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_52920e1aaa524949ab4f3e79caba30e5", "IPY_MODEL_3002cae9544549d39f2c3d34cbc667ad", "IPY_MODEL_013632c5019f405dbacfdeefe494dc22"], "layout": "IPY_MODEL_5b698a8efd7f418d8964d4ed18df2ce8"}}, "4691ac2c19814ddca82b557bf1abf49c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4993b3837ebe4abd820e94a559fbed4e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ccf22c2bfd0c4e05a6c61ee2a1f073cf", "placeholder": "​", "style": "IPY_MODEL_e715b74c921f4b6b9b1781dc0dacb65a", "value": " 248M/248M [00:04&lt;00:00, 79.5MB/s]"}}, "4a3c4a73eaea44ba9639013abafe8f65": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a8eea3fe65d41069793e29df02aeb61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e4e0d27644c4a3e9642ec4a4e48f9da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5289267a77944768a3feae8c0a60edc6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "52920e1aaa524949ab4f3e79caba30e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e7aeb988266948428b2084fc95248620", "placeholder": "​", "style": "IPY_MODEL_870ea57e268d44d7bbf8f88101ba7d3a", "value": "Generating validation split: 100%"}}, "52c09ed521ba49949bbc786974a2560b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5391065bacb04614a6226ad3c799df3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7e5afd994d484b51a4b1ef1523d5969f", "max": 248171980, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_147915d703424306b73b919c26677db8", "value": 248171980}}, "54b4c1fbbe844e4898c06617eef31748": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2888f04a22084170bafa7fdeed97d52b", "placeholder": "​", "style": "IPY_MODEL_b08f6a63776d4a89ba2ef045ab4902f4", "value": " 9.99M/9.99M [00:01&lt;00:00, 7.71MB/s]"}}, "55195af15d6243bb9659e3aecc345fe5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "56bed294aa6d4ac3ba5d7bdf440c26ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5725e9e7a3ef49799c7ad40804a44545": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b58ca1e80e445ccafb8149cd239be09": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c268be6fb1de415ca9e6c55411ef85f7", "IPY_MODEL_06033b10b015449a958138ba21d591f3", "IPY_MODEL_420b0f94c0cb422dbb22b90ba7cebe7a"], "layout": "IPY_MODEL_8bfc0c5e41744c68bb41a171c662a667"}}, "5b698a8efd7f418d8964d4ed18df2ce8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d114ee11c234481b023d256c29f2b48": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d43db671ec34d9bb0743e6859e21cfc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9c7fd659ddec4c5facba45d7dc8a70a8", "max": 2119719, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_efc96d91544c4408b3935daeb589fe43", "value": 2119719}}, "5d9f69b0c01747189df2a998bdba2336": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "60523115a0934c42b6d3eaa7b8dbfa5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "62a312500e9842a6ae116a7d88a44bfc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68467f5d0bac4d488e333969dae954f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "685183eeb79542eca5f070a3c2f67122": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dd48f879b9fd4738a21d99708f5ce40d", "placeholder": "​", "style": "IPY_MODEL_f2a07cbf33044dddb71963636961c443", "value": " 246M/246M [00:04&lt;00:00, 79.1MB/s]"}}, "6b7204b65e4e4705b23edd33e6061009": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6ba7db52a1504ff5b7fad4b9aa37ca47": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f1217b5e45644109b4e0270f2dedd45", "placeholder": "​", "style": "IPY_MODEL_16a14ddb52a345e3befde572795d6a9c", "value": "Downloading data: 100%"}}, "6cf85160c2ad4573837fdff644e11208": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6d0fc50735744f639492dfa52df812e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6d2a2611703c4fd7b3db412e2625be48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df7d00a7f4bf4963a5fd23b7be473960", "placeholder": "​", "style": "IPY_MODEL_2695578369f14c98a3204d473d4fac3b", "value": " 1.02k/1.02k [00:00&lt;00:00, 71.7kB/s]"}}, "6d9202d4ac964bf782e460c182360f36": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6ba7db52a1504ff5b7fad4b9aa37ca47", "IPY_MODEL_2391ad77914e445e8b1b600e3fa96c7c", "IPY_MODEL_685183eeb79542eca5f070a3c2f67122"], "layout": "IPY_MODEL_cdf10bc17c9b4a48b3531bdf4807ba18"}}, "6dadfcf52eb74a559431010a6e77d583": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_37064a269e744da5875e035a69690885", "placeholder": "​", "style": "IPY_MODEL_982c7a4d956b4b5b9e8ce527e1d9db0c", "value": "Downloading data: 100%"}}, "7067c8908b5b438eb5d2f0f217b2d8b0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "74231474464249dbbf0ebd0e3d2705ad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "747078e706ba4028ae480dd512367887": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "793aae2dc0d54ac4b80df65329a650ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "799b6b7a228f49cbad3d47bb26f16d46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7abf7e0b03f34ca1bf3df1cc900c806d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c33fe8933e94987af5f45cf4deb976e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c99023a81de4fcebd718f787f920d95": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d6164dec3704a93abd75a1952f343b6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e5afd994d484b51a4b1ef1523d5969f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "807b2ec5e3d6483387124d3291618e21": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "810d0d3ffc164d6a88834224bc2f4a3c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "842ebeab10d4481cb8dd38b5eca033f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "870ea57e268d44d7bbf8f88101ba7d3a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "891e2e52835243b2acfd640b23c0071d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8bfc0c5e41744c68bb41a171c662a667": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c6dee64c33b44bda12874f6a54c8d9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8ec74735134648aea38b5254cf9d3a73": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f99454387b84a53b3605c04666d071b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "903b3af5c66e45c5bf7daf6a46134b89": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "90be2ee8e1934cc99d973e025c0d5a7c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "925a44a71ef340ce97452c40ca3860c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9304bc57715b480ca80849cfdc455d9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c596e37e0e549ab9102d2321bc94632", "placeholder": "​", "style": "IPY_MODEL_60523115a0934c42b6d3eaa7b8dbfa5c", "value": " 1.96M/1.96M [00:00&lt;00:00, 2.05MB/s]"}}, "943d7db8b97e4d5c94fe5373db1e0dfe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "96afce833a6a4eba88fe03fa4cbab813": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "96d6ffe4553b4e8aaac5d8a575ac1da9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "982c7a4d956b4b5b9e8ce527e1d9db0c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "99785a56de774625be94cb7ddc5909b3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a23522084a8488c8010f341944bf736": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c7fd659ddec4c5facba45d7dc8a70a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f76613f1188459889cc43ae9f5f3fa4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9fe034194139457e941f699a57f103af": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6dadfcf52eb74a559431010a6e77d583", "IPY_MODEL_a1cc093e80c647f5a3bd0c723edbe571", "IPY_MODEL_2ae3de4fa73c4b559536bc8ff5ece100"], "layout": "IPY_MODEL_8f99454387b84a53b3605c04666d071b"}}, "a00a2e203bad4e9b941a400e9f88e006": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_891e2e52835243b2acfd640b23c0071d", "placeholder": "​", "style": "IPY_MODEL_aecf5315643e4518be9cf7b748bd2973", "value": "Generating train split: 100%"}}, "a1cc093e80c647f5a3bd0c723edbe571": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_caf1b1b2eeec43bea992b8a55a9340ee", "max": 248731111, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4456475bc1b648e9819534fa9ab8cced", "value": 248731111}}, "a2a71fa3e7e84b7d8df5d700f0810259": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a34628ecc40745f2948c1c46d732c286": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b58aeeca06fa4fb38009ebcf58a26386", "max": 1020, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_387fa6b0943848f1a8b948559822e205", "value": 1020}}, "a592d0c788454eea947b033197751c50": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ee71e78be5d9406ea6b686d17ebb6d7d", "IPY_MODEL_33eaf6a087a34e19bc1657dde249c786", "IPY_MODEL_e8a7b3d1c6b34b92a5aa99bddacbba35"], "layout": "IPY_MODEL_55195af15d6243bb9659e3aecc345fe5"}}, "ab17d129478f47d8959d72708786aa92": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "abe972c95fe945a8bb054ece4745bcc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ec74735134648aea38b5254cf9d3a73", "placeholder": "​", "style": "IPY_MODEL_5d9f69b0c01747189df2a998bdba2336", "value": "Map (num_proc=8): 100%"}}, "ad0595973e7b450a8d71838fbc89c8e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aecf5315643e4518be9cf7b748bd2973": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b08f6a63776d4a89ba2ef045ab4902f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b3e7c261642342f18977fafeca64813a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a2a71fa3e7e84b7d8df5d700f0810259", "placeholder": "​", "style": "IPY_MODEL_14e2670d4c684fe2ad9d640249cec8fd", "value": " 1.15k/1.15k [00:00&lt;00:00, 79.8kB/s]"}}, "b3f431ac6c82432f9fa5e691fb5c7915": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_799b6b7a228f49cbad3d47bb26f16d46", "max": 136734, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_39bf8ed45e7a4234a209cb1f15fa5165", "value": 136734}}, "b41c8750cece4911addefdc1940f693b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b6700fcb58894ac39d06415589b35779", "placeholder": "​", "style": "IPY_MODEL_6d0fc50735744f639492dfa52df812e1", "value": " 111/111 [00:00&lt;00:00, 8.93kB/s]"}}, "b58aeeca06fa4fb38009ebcf58a26386": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5f8212008ec453fb7289e701debcfc3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2ea873838f6541e9afd9f19d21da8315", "IPY_MODEL_a34628ecc40745f2948c1c46d732c286", "IPY_MODEL_6d2a2611703c4fd7b3db412e2625be48"], "layout": "IPY_MODEL_5725e9e7a3ef49799c7ad40804a44545"}}, "b6700fcb58894ac39d06415589b35779": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bdd07cd097bb4c67bd581ec046cc8bb7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c268be6fb1de415ca9e6c55411ef85f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f73fe9b55864ae5b7622dda8653572a", "placeholder": "​", "style": "IPY_MODEL_2b9bbf400d4440468ef51a7af36f273e", "value": "model.safetensors: 100%"}}, "c31afd889e2c46cd84ef35b07059845e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c347faf6eb114732b5dc009be3636ebc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cf0c656eebbe4b9c88b25ce0449c16c3", "IPY_MODEL_e0adda358fee41a2a25a76b963aff3c8", "IPY_MODEL_b41c8750cece4911addefdc1940f693b"], "layout": "IPY_MODEL_810d0d3ffc164d6a88834224bc2f4a3c"}}, "c47b46aa519f41a086231f67888e8212": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c9577eafe3a0491ba5cbd72c464f6874": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca2d8e83378f4cda91ab846e10a36fb1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caf1b1b2eeec43bea992b8a55a9340ee": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ccf22c2bfd0c4e05a6c61ee2a1f073cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cdf10bc17c9b4a48b3531bdf4807ba18": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf0c656eebbe4b9c88b25ce0449c16c3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9a23522084a8488c8010f341944bf736", "placeholder": "​", "style": "IPY_MODEL_ab17d129478f47d8959d72708786aa92", "value": "generation_config.json: 100%"}}, "d0f6847ee49244b5b9eeda26754d630d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d1848a12767c49b784dacbcd37b1199a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3d7dac001a3540eeaf8f3cdaf8baea8f", "IPY_MODEL_5391065bacb04614a6226ad3c799df3c", "IPY_MODEL_4993b3837ebe4abd820e94a559fbed4e"], "layout": "IPY_MODEL_ca2d8e83378f4cda91ab846e10a36fb1"}}, "d244d3ea508f497c96f1e9239e269fe1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d5f8e906b97b4ed493c3a9b69284a020": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e7d5fee28a62446d9091b12730ec2801", "max": 9989127, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_24a643daaea341888490005bf97400f8", "value": 9989127}}, "d73a947a9e084457a509046a976ad907": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7abf7e0b03f34ca1bf3df1cc900c806d", "placeholder": "​", "style": "IPY_MODEL_ad0595973e7b450a8d71838fbc89c8e5", "value": "tokenizer.json: 100%"}}, "d7b4be775bf842c188d94385b29b9263": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d873326dcc854da4866b8aa31e30a6d0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d88233a81e7040eca0004111026d289b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d95b3e48f249411e894317fdfcb0bcd1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2a18a32dda174d1a85ed7a2f94ff0be3", "placeholder": "​", "style": "IPY_MODEL_14e68c95472d489f8fe9f0c9e38d5457", "value": "config.json: 100%"}}, "d9d8e6d24894416ea481442dc33eec80": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dba649311a634dce989d634119541df4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dbdf9d595d114c94b93dfaad7975f07b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9f76613f1188459889cc43ae9f5f3fa4", "placeholder": "​", "style": "IPY_MODEL_ee33bf4147144744b62c6170732d3523", "value": " 2119719/2119719 [00:06&lt;00:00, 295237.77 examples/s]"}}, "dd48f879b9fd4738a21d99708f5ce40d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df7d00a7f4bf4963a5fd23b7be473960": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0adda358fee41a2a25a76b963aff3c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fa684d9d809f4d708fea1de165c2d3c9", "max": 111, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_52c09ed521ba49949bbc786974a2560b", "value": 111}}, "e715b74c921f4b6b9b1781dc0dacb65a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e7aeb988266948428b2084fc95248620": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7b5629c0559481d81d978cd38c4d96e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fa86792b494b45eda841dcd30d08cae0", "max": 2500, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c47b46aa519f41a086231f67888e8212", "value": 2500}}, "e7d5fee28a62446d9091b12730ec2801": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e818ceb7b2a94dd1a8c915c1497e7270": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e8a7b3d1c6b34b92a5aa99bddacbba35": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3893ed578d5b4a90b26bc2abad30719a", "placeholder": "​", "style": "IPY_MODEL_747078e706ba4028ae480dd512367887", "value": " 587k/587k [00:00&lt;00:00, 41.6MB/s]"}}, "e9061bc2704a4771bfe4d1e3661c1d37": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7067c8908b5b438eb5d2f0f217b2d8b0", "max": 1961691, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_807b2ec5e3d6483387124d3291618e21", "value": 1961691}}, "eb32e24d5ca04fd4837a8d7a39da56bd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7c33fe8933e94987af5f45cf4deb976e", "placeholder": "​", "style": "IPY_MODEL_0aa02bcf14524cec95e0f9553015d4e1", "value": "Downloading data: 100%"}}, "ebab2bac40d7464c850dd16353b7b001": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee33bf4147144744b62c6170732d3523": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee38243222f541799f44e1b1c5a47504": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7d6164dec3704a93abd75a1952f343b6", "max": 247988350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f49bf15971aa40ea9936c3ccf021157b", "value": 247988350}}, "ee71e78be5d9406ea6b686d17ebb6d7d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_68467f5d0bac4d488e333969dae954f8", "placeholder": "​", "style": "IPY_MODEL_2cee2854a26e46e090825953f1e7dd69", "value": "tokenizer.model: 100%"}}, "ee76fb24b834429dbeb3320caf9aa755": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "eedfbef053eb4fdf8ffeabd5b5303c09": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "efc96d91544c4408b3935daeb589fe43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f2a07cbf33044dddb71963636961c443": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f49bf15971aa40ea9936c3ccf021157b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fa684d9d809f4d708fea1de165c2d3c9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa86792b494b45eda841dcd30d08cae0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe09ec6a787546e1afd1699180b5e30b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bdd07cd097bb4c67bd581ec046cc8bb7", "placeholder": "​", "style": "IPY_MODEL_3c313a02db614be8aabbe5ee619f6f57", "value": "special_tokens_map.json: 100%"}}, "ff27478e1bca41148c5e0fec7fa42843": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e818ceb7b2a94dd1a8c915c1497e7270", "placeholder": "​", "style": "IPY_MODEL_56bed294aa6d4ac3ba5d7bdf440c26ac", "value": " 248M/248M [00:04&lt;00:00, 79.4MB/s]"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}