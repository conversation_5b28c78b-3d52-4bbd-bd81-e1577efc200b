# AOT ID: ['23_forward']
from ctypes import c_void_p, c_long, c_int
import torch
import math
import random
import os
import tempfile
from math import inf, nan
from cmath import nanj
from torch._inductor.hooks import run_intermediate_hooks
from torch._inductor.utils import maybe_profile
from torch._inductor.codegen.memory_planning import _align as align
from torch import device, empty_strided
from torch._inductor.async_compile import AsyncCompile
from torch._inductor.select_algorithm import extern_kernels
from torch._inductor.codegen.multi_kernel import MultiKernelCall
import triton
import triton.language as tl
from torch._inductor.runtime.triton_heuristics import start_graph, end_graph
from torch._C import _cuda_getCurrentRawStream as get_raw_stream
from torch._C import _cuda_getCurrentRawStream as get_raw_stream

aten = torch.ops.aten
inductor_ops = torch.ops.inductor
_quantized = torch.ops._quantized
assert_size_stride = torch._C._dynamo.guards.assert_size_stride
empty_strided_cpu = torch._C._dynamo.guards._empty_strided_cpu
empty_strided_cuda = torch._C._dynamo.guards._empty_strided_cuda
empty_strided_xpu = torch._C._dynamo.guards._empty_strided_xpu
reinterpret_tensor = torch._C._dynamo.guards._reinterpret_tensor
alloc_from_pool = torch.ops.inductor._alloc_from_pool
async_compile = AsyncCompile()
empty_strided_p2p = torch._C._distributed_c10d._SymmetricMemory.empty_strided_p2p


# kernel path: /tmp/torchinductor_lile/cd/ccdolg3y7yqbqtdpv5dttpbt3p7u5wemuajcgb4lee4ggke7zfyu.py
# Topologically Sorted Source Nodes: [float_1, , cross_entropy_loss], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
# Source node to ATen node mapping:
#    => prepare_softmax_online_default_6
#   cross_entropy_loss => log
#   float_1 => convert_element_type_2
# Graph fragment:
#   %convert_element_type_2 : [num_users=2] = call_function[target=torch.ops.prims.convert_element_type.default](args = (%mm, torch.float32), kwargs = {})
#   %prepare_softmax_online_default_6 : [num_users=2] = call_function[target=torch.ops.prims.prepare_softmax_online.default](args = (%convert_element_type_2, 1), kwargs = {})
#   %log : [num_users=2] = call_function[target=torch.ops.aten.log.default](args = (%getitem_76,), kwargs = {})
triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0 = async_compile.triton('triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 512, 'r0_': 262144},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_out_ptr0': '*fp32', 'in_ptr0': '*bf16', 'out_ptr0': '*fp32', 'xnumel': 'i32', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0', 'mutated_arg_names': ['in_out_ptr0'], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 1, 'num_reduction': 2, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0(in_out_ptr0, in_ptr0, out_ptr0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    r0_numel = 201088
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = xindex < xnumel
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    x0 = xindex
    _tmp3_max = tl.full([XBLOCK, R0_BLOCK], float('-inf'), tl.float32)
    _tmp3_sum = tl.zeros([XBLOCK, R0_BLOCK], tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_1 = r0_index
        tmp0 = tl.load(in_ptr0 + (r0_1 + 201088*x0), xmask & r0_mask, eviction_policy='evict_first', other=0.0).to(tl.float32)
        tmp1 = tmp0.to(tl.float32)
        tmp2 = tl.broadcast_to(tmp1, [XBLOCK, R0_BLOCK])

        _tmp3_max_next, _tmp3_sum_next = triton_helpers.online_softmax_combine(
            _tmp3_max, _tmp3_sum, tmp2, False
        )

        _tmp3_max = tl.where(r0_mask & xmask, _tmp3_max_next, _tmp3_max)
        _tmp3_sum = tl.where(r0_mask & xmask, _tmp3_sum_next, _tmp3_sum)

    tmp5, tmp6 = triton_helpers.online_softmax_reduce(
        _tmp3_max, _tmp3_sum, 1, False)
    tmp5 = tmp5[:, None]
    tmp6 = tmp6[:, None]
    tmp3 = tmp5
    tmp4 = tmp6
    tl.store(out_ptr0 + (x0), tmp3, xmask)
    tmp7 = tl_math.log(tmp4)
    tl.debug_barrier()
    tl.store(in_out_ptr0 + (x0), tmp7, xmask)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/b3/cb3k53aznudzvwursnxeevvkrgwwdsq2rcal5ty3svgibyxxaogp.py
# Topologically Sorted Source Nodes: [cross_entropy_loss], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2, ne_5, neg, sum_3, where_1
# Graph fragment:
#   %ne_5 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_14, -100), kwargs = {})
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %neg : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze,), kwargs = {})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %where_1 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_5, %neg, %full_default_2), kwargs = {})
#   %sum_3 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_1,), kwargs = {})
#   %ne_56 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_13, -100), kwargs = {})
#   %where_26 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_56, %unsqueeze_13, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_1 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_1', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_1', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_1(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + ((r0_0 % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = (r0_0 % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + ((r0_0 % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/rj/crjffdqdueaph3il7mqhsr2b2q6uaudb54zmr7r2rcmumf5nqhju.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_1], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_1 => ne_11, neg_1, sum_6, where_3
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %ne_11 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_22, -100), kwargs = {})
#   %neg_1 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_1,), kwargs = {})
#   %where_3 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_11, %neg_1, %full_default_2), kwargs = {})
#   %sum_6 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_3,), kwargs = {})
#   %ne_54 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_12, -100), kwargs = {})
#   %where_24 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_54, %unsqueeze_12, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_2 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_2', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_2', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_2(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (((r0_0 + ((6 + 2*ks0) // 7)) % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + ((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + (((r0_0 + ((6 + 2*ks0) // 7)) % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/6t/c6tutqjuqbadskojizb45gjgfcn75sjwz4yyuy5j5ex3sis7i3jk.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_2], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_2 => ne_17, neg_2, sum_9, where_5
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %ne_17 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_30, -100), kwargs = {})
#   %neg_2 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_2,), kwargs = {})
#   %where_5 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_17, %neg_2, %full_default_2), kwargs = {})
#   %sum_9 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_5,), kwargs = {})
#   %ne_52 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_11, -100), kwargs = {})
#   %where_22 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_52, %unsqueeze_11, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_3 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_3', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_3', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_3(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (((r0_0 + 2*((6 + 2*ks0) // 7)) % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + 2*((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + (((r0_0 + 2*((6 + 2*ks0) // 7)) % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/dh/cdh5qxknllxpx43nbrmpisl34yljf4pqhsfcg4d6xlmjkiawsvmw.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_3], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_3 => ne_23, neg_3, sum_12, where_7
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %ne_23 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_38, -100), kwargs = {})
#   %neg_3 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_3,), kwargs = {})
#   %where_7 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_23, %neg_3, %full_default_2), kwargs = {})
#   %sum_12 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_7,), kwargs = {})
#   %ne_50 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_10, -100), kwargs = {})
#   %where_20 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_50, %unsqueeze_10, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_4 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_4', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_4', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_4(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (((r0_0 + 3*((6 + 2*ks0) // 7)) % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + 3*((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + (((r0_0 + 3*((6 + 2*ks0) // 7)) % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/ff/cffzdzfqjpgvuehweg7l2tekfruulu3hj6ad5u5tzvvo4najlyb3.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_4], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_4 => ne_29, neg_4, sum_15, where_9
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %ne_29 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_46, -100), kwargs = {})
#   %neg_4 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_4,), kwargs = {})
#   %where_9 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_29, %neg_4, %full_default_2), kwargs = {})
#   %sum_15 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_9,), kwargs = {})
#   %ne_48 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_9, -100), kwargs = {})
#   %where_18 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_48, %unsqueeze_9, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_5 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_5', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_5', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_5(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (((r0_0 + 4*((6 + 2*ks0) // 7)) % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + 4*((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + (((r0_0 + 4*((6 + 2*ks0) // 7)) % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/ua/cuas6bx4xjaeh342oawex2pla4p3pnuuu7n53qxvqxrjeshkig7k.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_5], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_5 => ne_35, neg_5, sum_18, where_11
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %ne_35 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_54, -100), kwargs = {})
#   %neg_5 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_5,), kwargs = {})
#   %where_11 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_35, %neg_5, %full_default_2), kwargs = {})
#   %sum_18 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_11,), kwargs = {})
#   %ne_46 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_8, -100), kwargs = {})
#   %where_16 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_46, %unsqueeze_8, %full_default_1), kwargs = {})
triton_red_fused_nll_loss_backward_nll_loss_forward_6 = async_compile.triton('triton_red_fused_nll_loss_backward_nll_loss_forward_6', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'out_ptr0': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused_nll_loss_backward_nll_loss_forward_6', 'mutated_arg_names': [], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 4, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused_nll_loss_backward_nll_loss_forward_6(in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, out_ptr0, out_ptr1, out_ptr2, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (((r0_0 + 5*((6 + 2*ks0) // 7)) % (2*ks0))), r0_mask, eviction_policy='evict_last', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + 5*((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + (((r0_0 + 5*((6 + 2*ks0) // 7)) % (2*ks0))), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_last', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tl.store(out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp27, None)
''', device_str='cuda')


# kernel path: /tmp/torchinductor_lile/do/cdol5ljbhjug2g4gonxt6wj5h4nuxgmvcpwzwlqejstkq3edvr7t.py
# Topologically Sorted Source Nodes: [cross_entropy_loss, loss, loss_1, loss_2, loss_3, loss_4, loss_5, cross_entropy_loss_6, loss_6, tensor, loss_7], Original ATen: [aten.nll_loss_forward, aten.add, aten._to_copy, aten.div, aten.nll_loss_backward]
# Source node to ATen node mapping:
#   cross_entropy_loss => full_default_1, full_default_2
#   cross_entropy_loss_6 => ne_41, neg_6, sum_21, where_13
#   loss => add_78
#   loss_1 => add_91
#   loss_2 => add_104
#   loss_3 => add_117
#   loss_4 => add_130
#   loss_5 => add_143
#   loss_6 => add_156
#   loss_7 => div
#   tensor => convert_element_type_28
# Graph fragment:
#   %full_default_1 : [num_users=14] = call_function[target=torch.ops.aten.full.default](args = ([], 0), kwargs = {dtype: torch.int64, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %full_default_2 : [num_users=7] = call_function[target=torch.ops.aten.full.default](args = ([], 0.0), kwargs = {dtype: torch.float32, layout: torch.strided, device: cuda:0, pin_memory: False})
#   %add_78 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%sum_3, 0.0), kwargs = {})
#   %add_91 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_78, %sum_6), kwargs = {})
#   %add_104 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_91, %sum_9), kwargs = {})
#   %add_117 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_104, %sum_12), kwargs = {})
#   %add_130 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_117, %sum_15), kwargs = {})
#   %add_143 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_130, %sum_18), kwargs = {})
#   %ne_41 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%getitem_62, -100), kwargs = {})
#   %neg_6 : [num_users=1] = call_function[target=torch.ops.aten.neg.default](args = (%squeeze_6,), kwargs = {})
#   %where_13 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_41, %neg_6, %full_default_2), kwargs = {})
#   %sum_21 : [num_users=1] = call_function[target=torch.ops.aten.sum.default](args = (%where_13,), kwargs = {})
#   %add_156 : [num_users=1] = call_function[target=torch.ops.aten.add.Tensor](args = (%add_143, %sum_21), kwargs = {})
#   %convert_element_type_28 : [num_users=2] = call_function[target=torch.ops.prims.convert_element_type.default](args = (%primals_6, torch.float32), kwargs = {})
#   %div : [num_users=1] = call_function[target=torch.ops.aten.div.Tensor](args = (%add_156, %convert_element_type_28), kwargs = {})
#   %ne_44 : [num_users=2] = call_function[target=torch.ops.aten.ne.Scalar](args = (%unsqueeze_7, -100), kwargs = {})
#   %where_14 : [num_users=1] = call_function[target=torch.ops.aten.where.self](args = (%ne_44, %unsqueeze_7, %full_default_1), kwargs = {})
triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7 = async_compile.triton('triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7', '''
import triton
import triton.language as tl

from torch._inductor.runtime import triton_helpers, triton_heuristics
from torch._inductor.runtime.triton_helpers import libdevice, math as tl_math
from torch._inductor.runtime.hints import AutotuneHint, ReductionHint, TileHint, DeviceProperties
triton_helpers.set_driver_to_gpu()

@triton_heuristics.reduction(
    size_hints={'x': 1, 'r0_': 512},
    reduction_hint=ReductionHint.INNER,
    filename=__file__,
    triton_meta={'signature': {'in_out_ptr0': '*fp32', 'in_ptr0': '*i64', 'in_ptr1': '*i64', 'in_ptr2': '*bf16', 'in_ptr3': '*fp32', 'in_ptr4': '*fp32', 'in_ptr5': '*i64', 'in_ptr6': '*fp32', 'in_ptr7': '*fp32', 'in_ptr8': '*fp32', 'in_ptr9': '*fp32', 'in_ptr10': '*fp32', 'out_ptr1': '*i1', 'out_ptr2': '*i64', 'out_ptr3': '*fp32', 'ks0': 'i32', 'xnumel': 'constexpr', 'r0_numel': 'i32', 'XBLOCK': 'constexpr', 'R0_BLOCK': 'constexpr'}, 'device': DeviceProperties(type='cuda', index=0, multi_processor_count=132, cc=90, major=9, regs_per_multiprocessor=65536, max_threads_per_multi_processor=2048, warp_size=32), 'constants': {'xnumel': 1}, 'configs': [{(0,): [['tt.divisibility', 16]], (1,): [['tt.divisibility', 16]], (2,): [['tt.divisibility', 16]], (3,): [['tt.divisibility', 16]], (4,): [['tt.divisibility', 16]], (5,): [['tt.divisibility', 16]], (6,): [['tt.divisibility', 16]], (7,): [['tt.divisibility', 16]], (8,): [['tt.divisibility', 16]], (9,): [['tt.divisibility', 16]], (10,): [['tt.divisibility', 16]], (11,): [['tt.divisibility', 16]], (12,): [['tt.divisibility', 16]], (13,): [['tt.divisibility', 16]], (14,): [['tt.divisibility', 16]]}]},
    inductor_meta={'grid_type': 'Grid1D', 'autotune_hints': set(), 'kernel_name': 'triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7', 'mutated_arg_names': ['in_out_ptr0'], 'optimize_mem': False, 'no_x_dim': False, 'num_load': 11, 'num_reduction': 1, 'backend_hash': '6BE828ED5586B6FAE8FF446A7636CA33B7485F906B06FE7C269A644474AB4AF1', 'are_deterministic_algorithms_enabled': False, 'assert_indirect_indexing': True, 'autotune_local_cache': True, 'autotune_pointwise': True, 'autotune_remote_cache': None, 'force_disable_caches': False, 'dynamic_scale_rblock': True, 'max_autotune': False, 'max_autotune_pointwise': False, 'min_split_scan_rblock': 256, 'spill_threshold': 16, 'store_cubin': False}
)
@triton.jit
def triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7(in_out_ptr0, in_ptr0, in_ptr1, in_ptr2, in_ptr3, in_ptr4, in_ptr5, in_ptr6, in_ptr7, in_ptr8, in_ptr9, in_ptr10, out_ptr1, out_ptr2, out_ptr3, ks0, xnumel, r0_numel, XBLOCK : tl.constexpr, R0_BLOCK : tl.constexpr):
    xnumel = 1
    rnumel = r0_numel
    RBLOCK: tl.constexpr = R0_BLOCK
    xoffset = tl.program_id(0) * XBLOCK
    xindex = xoffset + tl.arange(0, XBLOCK)[:, None]
    xmask = tl.full([XBLOCK, R0_BLOCK], True, tl.int1)
    r0_base = tl.arange(0, R0_BLOCK)[None, :]
    rbase = r0_base
    _tmp27 = tl.full([XBLOCK, R0_BLOCK], 0, tl.float32)
    for r0_offset in range(0, r0_numel, R0_BLOCK):
        r0_index = r0_offset + r0_base
        r0_mask = r0_index < r0_numel
        roffset = r0_offset
        rindex = r0_index
        r0_0 = r0_index
        tmp5 = tl.load(in_ptr1 + (r0_0 + 6*((6 + 2*ks0) // 7)), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp19 = tl.load(in_ptr3 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp21 = tl.load(in_ptr4 + (r0_0), r0_mask, eviction_policy='evict_first', other=0.0)
        tmp0 = ((r0_0 + 6*((6 + 2*ks0) // 7)) % ks0)
        tmp1 = (-1) + ks0
        tmp2 = tmp0 == tmp1
        tmp3 = tmp0 < tmp1
        tmp4 = tl.load(in_ptr0 + (tl.broadcast_to(1 + r0_0 + 6*((6 + 2*ks0) // 7), [XBLOCK, R0_BLOCK])), r0_mask & tmp3, eviction_policy='evict_first', other=0.0)
        tmp6 = tl.where(tmp3, tmp4, tmp5)
        tmp7 = tl.full([1, 1], -100, tl.int64)
        tmp8 = tl.where(tmp2, tmp7, tmp6)
        tmp9 = tmp8 != tmp7
        tmp10 = tl.full([1, 1], 0, tl.int64)
        tmp11 = tl.where(tmp9, tmp8, tmp10)
        tmp12 = tl.full([XBLOCK, R0_BLOCK], 201088, tl.int32)
        tmp13 = tmp11 + tmp12
        tmp14 = tmp11 < 0
        tmp15 = tl.where(tmp14, tmp13, tmp11)
        tl.device_assert(((0 <= tmp15) & (tmp15 < 201088)) | ~(r0_mask), "index out of bounds: 0 <= tmp15 < 201088")
        tmp17 = tl.load(in_ptr2 + (tmp15 + 201088*r0_0), r0_mask, eviction_policy='evict_last', other=0.0).to(tl.float32)
        tmp18 = tmp17.to(tl.float32)
        tmp20 = tmp18 - tmp19
        tmp22 = tmp20 - tmp21
        tmp23 = -tmp22
        tmp24 = 0.0
        tmp25 = tl.where(tmp9, tmp23, tmp24)
        tmp26 = tl.broadcast_to(tmp25, [XBLOCK, R0_BLOCK])
        tmp28 = _tmp27 + tmp26
        _tmp27 = tl.where(r0_mask, tmp28, _tmp27)
        tl.store(out_ptr1 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp9, r0_mask)
        tl.store(out_ptr2 + (tl.broadcast_to(r0_0, [XBLOCK, R0_BLOCK])), tmp11, r0_mask)
    tmp27 = tl.sum(_tmp27, 1)[:, None]
    tmp29 = tl.load(in_ptr5 + (0))
    tmp30 = tl.broadcast_to(tmp29, [XBLOCK, 1])
    tmp32 = tl.load(in_out_ptr0 + (0))
    tmp33 = tl.broadcast_to(tmp32, [XBLOCK, 1])
    tmp36 = tl.load(in_ptr6 + (0))
    tmp37 = tl.broadcast_to(tmp36, [XBLOCK, 1])
    tmp39 = tl.load(in_ptr7 + (0))
    tmp40 = tl.broadcast_to(tmp39, [XBLOCK, 1])
    tmp42 = tl.load(in_ptr8 + (0))
    tmp43 = tl.broadcast_to(tmp42, [XBLOCK, 1])
    tmp45 = tl.load(in_ptr9 + (0))
    tmp46 = tl.broadcast_to(tmp45, [XBLOCK, 1])
    tmp48 = tl.load(in_ptr10 + (0))
    tmp49 = tl.broadcast_to(tmp48, [XBLOCK, 1])
    tmp31 = tmp30.to(tl.float32)
    tmp34 = 0.0
    tmp35 = tmp33 + tmp34
    tmp38 = tmp35 + tmp37
    tmp41 = tmp38 + tmp40
    tmp44 = tmp41 + tmp43
    tmp47 = tmp44 + tmp46
    tmp50 = tmp47 + tmp49
    tmp51 = tmp50 + tmp27
    tmp52 = (tmp51 / tmp31)
    tl.store(out_ptr3 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp31, None)
    tl.debug_barrier()
    tl.store(in_out_ptr0 + (tl.full([XBLOCK, 1], 0, tl.int32)), tmp52, None)
''', device_str='cuda')


async_compile.wait(globals())
del async_compile

def call(args):
    primals_1, primals_2, primals_3, primals_4, primals_5, primals_6 = args
    args.clear()
    s0 = primals_2
    s3 = primals_4
    assert_size_stride(primals_1, (201088, 2880), (2880, 1))
    assert_size_stride(primals_3, (2, s0), (s0, 1))
    assert_size_stride(primals_5, (2, s3, 2880), (2880*s3, 2880, 1))
    assert_size_stride(primals_6, (), ())
    with torch.cuda._DeviceGuard(0):
        torch.cuda.set_device(0)
        buf0 = empty_strided_cuda((2, s0), (s0, 1), torch.int64)
        buf1 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 0), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf1)
        buf2 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf3 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf4 = reinterpret_tensor(buf3, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf3  # reuse
        # Topologically Sorted Source Nodes: [float_1, , cross_entropy_loss], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf4, buf1, buf2, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf5 = empty_strided_cuda((), (), torch.float32)
        buf49 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf50 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_1_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_1.run(primals_3, buf0, buf1, buf2, buf4, buf5, buf49, buf50, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_1_r0_numel, stream=stream0)
        buf6 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_1], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 2880*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf6)
        buf7 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf8 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf9 = reinterpret_tensor(buf8, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf8  # reuse
        # Topologically Sorted Source Nodes: [float_2, , cross_entropy_loss_1], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf9, buf6, buf7, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf10 = empty_strided_cuda((), (), torch.float32)
        buf47 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf48 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_1], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_2_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_2.run(primals_3, buf0, buf6, buf7, buf9, buf10, buf47, buf48, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_2_r0_numel, stream=stream0)
        buf11 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_2], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 5760*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf11)
        buf12 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf13 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf14 = reinterpret_tensor(buf13, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf13  # reuse
        # Topologically Sorted Source Nodes: [float_3, , cross_entropy_loss_2], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf14, buf11, buf12, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf15 = empty_strided_cuda((), (), torch.float32)
        buf45 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf46 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_2], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_3_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_3.run(primals_3, buf0, buf11, buf12, buf14, buf15, buf45, buf46, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_3_r0_numel, stream=stream0)
        buf16 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_3], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 8640*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf16)
        buf17 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf18 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf19 = reinterpret_tensor(buf18, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf18  # reuse
        # Topologically Sorted Source Nodes: [float_4, , cross_entropy_loss_3], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf19, buf16, buf17, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf20 = empty_strided_cuda((), (), torch.float32)
        buf43 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf44 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_3], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_4_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_4.run(primals_3, buf0, buf16, buf17, buf19, buf20, buf43, buf44, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_4_r0_numel, stream=stream0)
        buf21 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_4], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 11520*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf21)
        buf22 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf23 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf24 = reinterpret_tensor(buf23, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf23  # reuse
        # Topologically Sorted Source Nodes: [float_5, , cross_entropy_loss_4], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf24, buf21, buf22, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf25 = empty_strided_cuda((), (), torch.float32)
        buf41 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf42 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_4], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_5_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_5.run(primals_3, buf0, buf21, buf22, buf24, buf25, buf41, buf42, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_5_r0_numel, stream=stream0)
        buf26 = empty_strided_cuda(((6 + 2*s3) // 7, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_5], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((6 + 2*s3) // 7, 2880), (2880, 1), 14400*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf26)
        buf27 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, 1), torch.float32)
        buf28 = empty_strided_cuda(((6 + 2*s3) // 7, 1), (1, (6 + 2*s3) // 7), torch.float32)
        buf29 = reinterpret_tensor(buf28, ((6 + 2*s3) // 7, 1), (1, 1), 0); del buf28  # reuse
        # Topologically Sorted Source Nodes: [float_6, , cross_entropy_loss_5], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (6 + 2*s3) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf29, buf26, buf27, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf30 = empty_strided_cuda((), (), torch.float32)
        buf39 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.bool)
        buf40 = empty_strided_cuda(((6 + 2*s0) // 7, 1), (1, 1), torch.int64)
        # Topologically Sorted Source Nodes: [cross_entropy_loss, cross_entropy_loss_5], Original ATen: [aten.nll_loss_forward, aten.nll_loss_backward]
        triton_red_fused_nll_loss_backward_nll_loss_forward_6_r0_numel = (6 + 2*s0) // 7
        stream0 = get_raw_stream(0)
        triton_red_fused_nll_loss_backward_nll_loss_forward_6.run(primals_3, buf0, buf26, buf27, buf29, buf30, buf39, buf40, s0, 1, triton_red_fused_nll_loss_backward_nll_loss_forward_6_r0_numel, stream=stream0)
        buf31 = empty_strided_cuda(((-6)*((6 + 2*s3) // 7) + 2*s3, 201088), (201088, 1), torch.bfloat16)
        # Topologically Sorted Source Nodes: [_shift_logits_6], Original ATen: [aten.mm]
        extern_kernels.mm(reinterpret_tensor(primals_5, ((-6)*((6 + 2*s3) // 7) + 2*s3, 2880), (2880, 1), 17280*((6 + 2*s3) // 7)), reinterpret_tensor(primals_1, (2880, 201088), (1, 2880), 0), out=buf31)
        del primals_5
        buf32 = empty_strided_cuda(((-6)*((6 + 2*s3) // 7) + 2*s3, 1), (1, 1), torch.float32)
        buf33 = empty_strided_cuda(((-6)*((6 + 2*s3) // 7) + 2*s3, 1), (1, (-6)*((6 + 2*s3) // 7) + 2*s3), torch.float32)
        buf34 = reinterpret_tensor(buf33, ((-6)*((6 + 2*s3) // 7) + 2*s3, 1), (1, 1), 0); del buf33  # reuse
        # Topologically Sorted Source Nodes: [float_7, , cross_entropy_loss_6], Original ATen: [aten._to_copy, prims.prepare_softmax_online, aten._log_softmax]
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel = (-6)*((6 + 2*s3) // 7) + 2*s3
        stream0 = get_raw_stream(0)
        triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0.run(buf34, buf31, buf32, triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0_xnumel, 201088, stream=stream0)
        buf37 = empty_strided_cuda(((-6)*((6 + 2*s0) // 7) + 2*s0, 1), (1, 1), torch.bool)
        buf38 = empty_strided_cuda(((-6)*((6 + 2*s0) // 7) + 2*s0, 1), (1, 1), torch.int64)
        buf36 = empty_strided_cuda((), (), torch.float32)
        buf51 = buf5; del buf5  # reuse
        # Topologically Sorted Source Nodes: [cross_entropy_loss, loss, loss_1, loss_2, loss_3, loss_4, loss_5, cross_entropy_loss_6, loss_6, tensor, loss_7], Original ATen: [aten.nll_loss_forward, aten.add, aten._to_copy, aten.div, aten.nll_loss_backward]
        triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7_r0_numel = (-6)*((6 + 2*s0) // 7) + 2*s0
        stream0 = get_raw_stream(0)
        triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7.run(buf51, primals_3, buf0, buf31, buf32, buf34, primals_6, buf10, buf15, buf20, buf25, buf30, buf37, buf38, buf36, s0, 1, triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7_r0_numel, stream=stream0)
        del buf0
        del buf10
        del buf15
        del buf20
        del buf25
        del buf30
        del primals_3
        del primals_6
    return (buf51, buf1, buf2, buf4, buf6, buf7, buf9, buf11, buf12, buf14, buf16, buf17, buf19, buf21, buf22, buf24, buf26, buf27, buf29, buf31, buf32, buf34, buf36, buf37, buf38, primals_1, buf39, buf40, buf41, buf42, buf43, buf44, buf45, buf46, buf47, buf48, buf49, buf50, s3, s0, (6 + 2*s3) // 7, (-6)*((6 + 2*s3) // 7) + 2*s3, )


def benchmark_compiled_module(times=10, repeat=10):
    from torch._dynamo.testing import rand_strided
    from torch._inductor.utils import print_performance
    primals_1 = rand_strided((201088, 2880), (2880, 1), device='cuda:0', dtype=torch.bfloat16)
    primals_2 = 1359
    primals_3 = rand_strided((2, 1359), (1359, 1), device='cuda:0', dtype=torch.int64)
    primals_4 = 1359
    primals_5 = rand_strided((2, 1359, 2880), (3913920, 2880, 1), device='cuda:0', dtype=torch.bfloat16)
    primals_6 = rand_strided((), (), device='cuda:0', dtype=torch.int64)
    fn = lambda: call([primals_1, primals_2, primals_3, primals_4, primals_5, primals_6])
    return print_performance(fn, times=times, repeat=repeat)


if __name__ == "__main__":
    from torch._inductor.wrapper_benchmark import compiled_module_main
    compiled_module_main('None', benchmark_compiled_module)
