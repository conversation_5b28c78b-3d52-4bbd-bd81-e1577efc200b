
import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,roundup_power2_divisions:[32:256,64:128,256:64,>:32]'
os.environ['TRITON_DISABLE_LINE_INFO'] = '1'
os.environ['TRITON_FRONT_END_DEBUGGING'] = '0'
os.environ['TORCHINDUCTOR_FX_GRAPH_CACHE'] = '1'
os.environ['TORCHINDUCTOR_AUTOTUNE_REMOTE_CACHE'] = '1'
os.environ['TORCHINDUCTOR_CACHE_DIR'] = '/tmp/torchinductor_lile'
os.environ['TRITON_CACHE_DIR'] = '/tmp/torchinductor_lile/triton/0'

import torch
from torch import tensor, device
import torch.fx as fx
from torch._dynamo.testing import rand_strided
from math import inf
import torch._inductor.inductor_prims

import torch._dynamo.config
import torch._inductor.config
import torch._functorch.config
import torch.fx.experimental._config
torch._dynamo.config.verbose = False
torch._dynamo.config.recompile_limit = 16
torch._dynamo.config.accumulated_recompile_limit = 1024
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.capture_scalar_outputs = True
torch._dynamo.config.capture_dynamic_output_shape_ops = True
torch._dynamo.config.allow_unspec_int_on_nn_module = True
torch._dynamo.config.optimize_ddp = True
torch._dynamo.config.do_not_emit_runtime_asserts = True
torch._dynamo.config.numpy_default_float = 'float32'
torch._dynamo.config.inline_inbuilt_nn_modules = True
torch._dynamo.config.compiled_autograd = False
torch._inductor.config.debug = False
torch._inductor.config.disable_progress = True
torch._inductor.config.verbose_progress = False
torch._inductor.config.dce = True
torch._inductor.config.memory_planning = True
torch._inductor.config.memory_pool = 'none'
torch._inductor.config.epilogue_fusion = True
torch._inductor.config.efficient_conv_bn_eval_fx_passes = True
torch._inductor.config.group_fusion = False
torch._inductor.config.dynamic_scale_rblock = True
torch._inductor.config.max_autotune = False
torch._inductor.config.max_autotune_pointwise = False
torch._inductor.config.max_autotune_gemm = False
torch._inductor.config.max_autotune_gemm_backends = 'ATEN,TRITON,CPP'
torch._inductor.config.autotune_fallback_to_aten = True
torch._inductor.config.autotune_multi_device = True
torch._inductor.config.coordinate_descent_tuning = False
torch._inductor.config.aggressive_fusion = False
torch._inductor.config.combo_kernels = False
torch._inductor.config.benchmark_combo_kernel = False
torch._inductor.config.combo_kernel_foreach_dynamic_shapes = False
torch._inductor.config.emulate_precision_casts = False
torch._inductor.config.compile_threads = 24
torch._inductor.config.shape_padding = True
torch._inductor.config.freezing = False
torch._inductor.config.triton.cudagraphs = False
torch._inductor.config.triton.autotune_at_compile_time = None
torch._inductor.config.triton.cooperative_reductions = False
torch._inductor.config.triton.multi_kernel = 0
torch._inductor.config.triton.store_cubin = False
torch._inductor.config.triton.use_block_ptr = False
torch._inductor.config.triton.enable_persistent_tma_matmul = False
torch._inductor.config.cuda.compile_opt_level = '-O1'
torch._inductor.config.cuda.enable_cuda_lto = True
torch._inductor.config.cuda.use_fast_math = True
torch._inductor.config.trace.enabled = False
torch._inductor.config.trace.save_real_tensors = False
torch._inductor.config.trace.graph_diagram = False
torch._inductor.config.test_configs.runtime_triton_dtype_assert = False
torch._functorch.config.functionalize_rng_ops = False
torch._functorch.config.fake_tensor_allow_unsafe_data_ptr_access = True
torch._functorch.config.unlift_effect_tokens = True



isolate_fails_code_str = None




# torch version: 2.7.1+cu126
# torch cuda version: 12.6
# torch git version: e2d141dbde55c2a4370fac5165b0561b6af4798b


# CUDA Info: 
# nvcc: NVIDIA (R) Cuda compiler driver 
# Copyright (c) 2005-2024 NVIDIA Corporation 
# Built on Wed_Apr_17_19:19:55_PDT_2024 
# Cuda compilation tools, release 12.5, V12.5.40 
# Build cuda_12.5.r12.5/compiler.34177558_0 

# GPU Hardware Info: 
# NVIDIA H800 : 1 


from torch.nn import *
class Repro(torch.nn.Module):
    def __init__(self) -> None:
        super().__init__()

    
    
    def forward(self, primals_1, primals_2, primals_3, primals_4, primals_5, primals_6):
        empty = torch.ops.aten.empty.memory_format([2, primals_2], dtype = torch.int64, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        permute = torch.ops.aten.permute.default(empty, [0, 1]);  empty = None
        slice_1 = torch.ops.aten.slice.Tensor(primals_3, 1, 1, 9223372036854775807);  primals_3 = None
        slice_scatter = torch.ops.aten.slice_scatter.default(permute, slice_1, 1, 0, -1);  slice_1 = None
        full_default = torch.ops.aten.full.default([], -100, dtype = torch.int64, layout = torch.strided, device = device(type='cpu'), pin_memory = False)
        select_1 = torch.ops.aten.select.int(slice_scatter, 1, -1)
        copy_1 = torch.ops.aten.copy.default(select_1, full_default);  select_1 = full_default = None
        select_scatter = torch.ops.aten.select_scatter.default(slice_scatter, copy_1, 1, -1);  slice_scatter = copy_1 = None
        sym_numel_default = torch.ops.aten.sym_numel.default(permute);  permute = None
        view_1 = torch.ops.aten.view.default(primals_5, [-1, 2880]);  primals_5 = None
        sym_size_int_4 = torch.ops.aten.sym_size.int(view_1, 0)
        add_29 = sym_size_int_4 + 7;  sym_size_int_4 = None
        sub_8 = add_29 - 1;  add_29 = None
        floordiv = sub_8 // 7;  sub_8 = None
        split = torch.ops.aten.split.Tensor(view_1, floordiv);  view_1 = None
        getitem = split[0]
        getitem_1 = split[1]
        getitem_2 = split[2]
        getitem_3 = split[3]
        getitem_4 = split[4]
        getitem_5 = split[5]
        getitem_6 = split[6];  split = None
        sym_size_int_5 = torch.ops.aten.sym_size.int(getitem_6, 0)
        add_51 = sym_numel_default + 7;  sym_numel_default = None
        sub_16 = add_51 - 1;  add_51 = None
        floordiv_1 = sub_16 // 7;  sub_16 = None
        permute_1 = torch.ops.aten.permute.default(primals_1, [1, 0]);  primals_1 = None
        mm = torch.ops.aten.mm.default(getitem, permute_1);  getitem = None
        convert_element_type_2 = torch.ops.prims.convert_element_type.default(mm, torch.float32)
        amax = torch.ops.aten.amax.default(convert_element_type_2, [1], True)
        sub_26 = torch.ops.aten.sub.Tensor(convert_element_type_2, amax);  convert_element_type_2 = None
        exp = torch.ops.aten.exp.default(sub_26)
        sum_1 = torch.ops.aten.sum.dim_IntList(exp, [1], True);  exp = None
        log = torch.ops.aten.log.default(sum_1);  sum_1 = None
        sub_27 = torch.ops.aten.sub.Tensor(sub_26, log);  sub_26 = None
        view_2 = torch.ops.aten.view.default(select_scatter, [-1]);  select_scatter = None
        split_2 = torch.ops.aten.split.Tensor(view_2, floordiv_1);  view_2 = floordiv_1 = None
        getitem_14 = split_2[0]
        ne_5 = torch.ops.aten.ne.Scalar(getitem_14, -100)
        full_default_1 = torch.ops.aten.full.default([], 0, dtype = torch.int64, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        where = torch.ops.aten.where.self(ne_5, getitem_14, full_default_1)
        unsqueeze = torch.ops.aten.unsqueeze.default(where, 1);  where = None
        gather = torch.ops.aten.gather.default(sub_27, 1, unsqueeze);  sub_27 = unsqueeze = None
        squeeze = torch.ops.aten.squeeze.dim(gather, 1);  gather = None
        neg = torch.ops.aten.neg.default(squeeze);  squeeze = None
        full_default_2 = torch.ops.aten.full.default([], 0.0, dtype = torch.float32, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        where_1 = torch.ops.aten.where.self(ne_5, neg, full_default_2);  ne_5 = neg = None
        sum_3 = torch.ops.aten.sum.default(where_1);  where_1 = None
        add_78 = torch.ops.aten.add.Tensor(sum_3, 0.0);  sum_3 = None
        mm_1 = torch.ops.aten.mm.default(getitem_1, permute_1);  getitem_1 = None
        convert_element_type_6 = torch.ops.prims.convert_element_type.default(mm_1, torch.float32)
        amax_1 = torch.ops.aten.amax.default(convert_element_type_6, [1], True)
        sub_32 = torch.ops.aten.sub.Tensor(convert_element_type_6, amax_1);  convert_element_type_6 = None
        exp_1 = torch.ops.aten.exp.default(sub_32)
        sum_4 = torch.ops.aten.sum.dim_IntList(exp_1, [1], True);  exp_1 = None
        log_1 = torch.ops.aten.log.default(sum_4);  sum_4 = None
        sub_33 = torch.ops.aten.sub.Tensor(sub_32, log_1);  sub_32 = None
        getitem_22 = split_2[1]
        ne_11 = torch.ops.aten.ne.Scalar(getitem_22, -100)
        where_2 = torch.ops.aten.where.self(ne_11, getitem_22, full_default_1)
        unsqueeze_1 = torch.ops.aten.unsqueeze.default(where_2, 1);  where_2 = None
        gather_1 = torch.ops.aten.gather.default(sub_33, 1, unsqueeze_1);  sub_33 = unsqueeze_1 = None
        squeeze_1 = torch.ops.aten.squeeze.dim(gather_1, 1);  gather_1 = None
        neg_1 = torch.ops.aten.neg.default(squeeze_1);  squeeze_1 = None
        where_3 = torch.ops.aten.where.self(ne_11, neg_1, full_default_2);  ne_11 = neg_1 = None
        sum_6 = torch.ops.aten.sum.default(where_3);  where_3 = None
        add_91 = torch.ops.aten.add.Tensor(add_78, sum_6);  add_78 = sum_6 = None
        mm_2 = torch.ops.aten.mm.default(getitem_2, permute_1);  getitem_2 = None
        convert_element_type_10 = torch.ops.prims.convert_element_type.default(mm_2, torch.float32)
        amax_2 = torch.ops.aten.amax.default(convert_element_type_10, [1], True)
        sub_38 = torch.ops.aten.sub.Tensor(convert_element_type_10, amax_2);  convert_element_type_10 = None
        exp_2 = torch.ops.aten.exp.default(sub_38)
        sum_7 = torch.ops.aten.sum.dim_IntList(exp_2, [1], True);  exp_2 = None
        log_2 = torch.ops.aten.log.default(sum_7);  sum_7 = None
        sub_39 = torch.ops.aten.sub.Tensor(sub_38, log_2);  sub_38 = None
        getitem_30 = split_2[2]
        ne_17 = torch.ops.aten.ne.Scalar(getitem_30, -100)
        where_4 = torch.ops.aten.where.self(ne_17, getitem_30, full_default_1)
        unsqueeze_2 = torch.ops.aten.unsqueeze.default(where_4, 1);  where_4 = None
        gather_2 = torch.ops.aten.gather.default(sub_39, 1, unsqueeze_2);  sub_39 = unsqueeze_2 = None
        squeeze_2 = torch.ops.aten.squeeze.dim(gather_2, 1);  gather_2 = None
        neg_2 = torch.ops.aten.neg.default(squeeze_2);  squeeze_2 = None
        where_5 = torch.ops.aten.where.self(ne_17, neg_2, full_default_2);  ne_17 = neg_2 = None
        sum_9 = torch.ops.aten.sum.default(where_5);  where_5 = None
        add_104 = torch.ops.aten.add.Tensor(add_91, sum_9);  add_91 = sum_9 = None
        mm_3 = torch.ops.aten.mm.default(getitem_3, permute_1);  getitem_3 = None
        convert_element_type_14 = torch.ops.prims.convert_element_type.default(mm_3, torch.float32)
        amax_3 = torch.ops.aten.amax.default(convert_element_type_14, [1], True)
        sub_44 = torch.ops.aten.sub.Tensor(convert_element_type_14, amax_3);  convert_element_type_14 = None
        exp_3 = torch.ops.aten.exp.default(sub_44)
        sum_10 = torch.ops.aten.sum.dim_IntList(exp_3, [1], True);  exp_3 = None
        log_3 = torch.ops.aten.log.default(sum_10);  sum_10 = None
        sub_45 = torch.ops.aten.sub.Tensor(sub_44, log_3);  sub_44 = None
        getitem_38 = split_2[3]
        ne_23 = torch.ops.aten.ne.Scalar(getitem_38, -100)
        where_6 = torch.ops.aten.where.self(ne_23, getitem_38, full_default_1)
        unsqueeze_3 = torch.ops.aten.unsqueeze.default(where_6, 1);  where_6 = None
        gather_3 = torch.ops.aten.gather.default(sub_45, 1, unsqueeze_3);  sub_45 = unsqueeze_3 = None
        squeeze_3 = torch.ops.aten.squeeze.dim(gather_3, 1);  gather_3 = None
        neg_3 = torch.ops.aten.neg.default(squeeze_3);  squeeze_3 = None
        where_7 = torch.ops.aten.where.self(ne_23, neg_3, full_default_2);  ne_23 = neg_3 = None
        sum_12 = torch.ops.aten.sum.default(where_7);  where_7 = None
        add_117 = torch.ops.aten.add.Tensor(add_104, sum_12);  add_104 = sum_12 = None
        mm_4 = torch.ops.aten.mm.default(getitem_4, permute_1);  getitem_4 = None
        convert_element_type_18 = torch.ops.prims.convert_element_type.default(mm_4, torch.float32)
        amax_4 = torch.ops.aten.amax.default(convert_element_type_18, [1], True)
        sub_50 = torch.ops.aten.sub.Tensor(convert_element_type_18, amax_4);  convert_element_type_18 = None
        exp_4 = torch.ops.aten.exp.default(sub_50)
        sum_13 = torch.ops.aten.sum.dim_IntList(exp_4, [1], True);  exp_4 = None
        log_4 = torch.ops.aten.log.default(sum_13);  sum_13 = None
        sub_51 = torch.ops.aten.sub.Tensor(sub_50, log_4);  sub_50 = None
        getitem_46 = split_2[4]
        ne_29 = torch.ops.aten.ne.Scalar(getitem_46, -100)
        where_8 = torch.ops.aten.where.self(ne_29, getitem_46, full_default_1)
        unsqueeze_4 = torch.ops.aten.unsqueeze.default(where_8, 1);  where_8 = None
        gather_4 = torch.ops.aten.gather.default(sub_51, 1, unsqueeze_4);  sub_51 = unsqueeze_4 = None
        squeeze_4 = torch.ops.aten.squeeze.dim(gather_4, 1);  gather_4 = None
        neg_4 = torch.ops.aten.neg.default(squeeze_4);  squeeze_4 = None
        where_9 = torch.ops.aten.where.self(ne_29, neg_4, full_default_2);  ne_29 = neg_4 = None
        sum_15 = torch.ops.aten.sum.default(where_9);  where_9 = None
        add_130 = torch.ops.aten.add.Tensor(add_117, sum_15);  add_117 = sum_15 = None
        mm_5 = torch.ops.aten.mm.default(getitem_5, permute_1);  getitem_5 = None
        convert_element_type_22 = torch.ops.prims.convert_element_type.default(mm_5, torch.float32)
        amax_5 = torch.ops.aten.amax.default(convert_element_type_22, [1], True)
        sub_56 = torch.ops.aten.sub.Tensor(convert_element_type_22, amax_5);  convert_element_type_22 = None
        exp_5 = torch.ops.aten.exp.default(sub_56)
        sum_16 = torch.ops.aten.sum.dim_IntList(exp_5, [1], True);  exp_5 = None
        log_5 = torch.ops.aten.log.default(sum_16);  sum_16 = None
        sub_57 = torch.ops.aten.sub.Tensor(sub_56, log_5);  sub_56 = None
        getitem_54 = split_2[5]
        ne_35 = torch.ops.aten.ne.Scalar(getitem_54, -100)
        where_10 = torch.ops.aten.where.self(ne_35, getitem_54, full_default_1)
        unsqueeze_5 = torch.ops.aten.unsqueeze.default(where_10, 1);  where_10 = None
        gather_5 = torch.ops.aten.gather.default(sub_57, 1, unsqueeze_5);  sub_57 = unsqueeze_5 = None
        squeeze_5 = torch.ops.aten.squeeze.dim(gather_5, 1);  gather_5 = None
        neg_5 = torch.ops.aten.neg.default(squeeze_5);  squeeze_5 = None
        where_11 = torch.ops.aten.where.self(ne_35, neg_5, full_default_2);  ne_35 = neg_5 = None
        sum_18 = torch.ops.aten.sum.default(where_11);  where_11 = None
        add_143 = torch.ops.aten.add.Tensor(add_130, sum_18);  add_130 = sum_18 = None
        mm_6 = torch.ops.aten.mm.default(getitem_6, permute_1);  getitem_6 = None
        convert_element_type_26 = torch.ops.prims.convert_element_type.default(mm_6, torch.float32)
        amax_6 = torch.ops.aten.amax.default(convert_element_type_26, [1], True)
        sub_62 = torch.ops.aten.sub.Tensor(convert_element_type_26, amax_6);  convert_element_type_26 = None
        exp_6 = torch.ops.aten.exp.default(sub_62)
        sum_19 = torch.ops.aten.sum.dim_IntList(exp_6, [1], True);  exp_6 = None
        log_6 = torch.ops.aten.log.default(sum_19);  sum_19 = None
        sub_63 = torch.ops.aten.sub.Tensor(sub_62, log_6);  sub_62 = None
        getitem_62 = split_2[6];  split_2 = None
        ne_41 = torch.ops.aten.ne.Scalar(getitem_62, -100)
        where_12 = torch.ops.aten.where.self(ne_41, getitem_62, full_default_1)
        unsqueeze_6 = torch.ops.aten.unsqueeze.default(where_12, 1);  where_12 = None
        gather_6 = torch.ops.aten.gather.default(sub_63, 1, unsqueeze_6);  sub_63 = unsqueeze_6 = None
        squeeze_6 = torch.ops.aten.squeeze.dim(gather_6, 1);  gather_6 = None
        neg_6 = torch.ops.aten.neg.default(squeeze_6);  squeeze_6 = None
        where_13 = torch.ops.aten.where.self(ne_41, neg_6, full_default_2);  ne_41 = neg_6 = full_default_2 = None
        sum_21 = torch.ops.aten.sum.default(where_13);  where_13 = None
        add_156 = torch.ops.aten.add.Tensor(add_143, sum_21);  add_143 = sum_21 = None
        convert_element_type_28 = torch.ops.prims.convert_element_type.default(primals_6, torch.float32);  primals_6 = None
        div = torch.ops.aten.div.Tensor(add_156, convert_element_type_28);  add_156 = None
        unsqueeze_7 = torch.ops.aten.unsqueeze.default(getitem_62, 1);  getitem_62 = None
        ne_44 = torch.ops.aten.ne.Scalar(unsqueeze_7, -100)
        where_14 = torch.ops.aten.where.self(ne_44, unsqueeze_7, full_default_1);  unsqueeze_7 = None
        permute_8 = torch.ops.aten.permute.default(permute_1, [1, 0]);  permute_1 = None
        unsqueeze_8 = torch.ops.aten.unsqueeze.default(getitem_54, 1);  getitem_54 = None
        ne_46 = torch.ops.aten.ne.Scalar(unsqueeze_8, -100)
        where_16 = torch.ops.aten.where.self(ne_46, unsqueeze_8, full_default_1);  unsqueeze_8 = None
        unsqueeze_9 = torch.ops.aten.unsqueeze.default(getitem_46, 1);  getitem_46 = None
        ne_48 = torch.ops.aten.ne.Scalar(unsqueeze_9, -100)
        where_18 = torch.ops.aten.where.self(ne_48, unsqueeze_9, full_default_1);  unsqueeze_9 = None
        unsqueeze_10 = torch.ops.aten.unsqueeze.default(getitem_38, 1);  getitem_38 = None
        ne_50 = torch.ops.aten.ne.Scalar(unsqueeze_10, -100)
        where_20 = torch.ops.aten.where.self(ne_50, unsqueeze_10, full_default_1);  unsqueeze_10 = None
        unsqueeze_11 = torch.ops.aten.unsqueeze.default(getitem_30, 1);  getitem_30 = None
        ne_52 = torch.ops.aten.ne.Scalar(unsqueeze_11, -100)
        where_22 = torch.ops.aten.where.self(ne_52, unsqueeze_11, full_default_1);  unsqueeze_11 = None
        unsqueeze_12 = torch.ops.aten.unsqueeze.default(getitem_22, 1);  getitem_22 = None
        ne_54 = torch.ops.aten.ne.Scalar(unsqueeze_12, -100)
        where_24 = torch.ops.aten.where.self(ne_54, unsqueeze_12, full_default_1);  unsqueeze_12 = None
        unsqueeze_13 = torch.ops.aten.unsqueeze.default(getitem_14, 1);  getitem_14 = None
        ne_56 = torch.ops.aten.ne.Scalar(unsqueeze_13, -100)
        where_26 = torch.ops.aten.where.self(ne_56, unsqueeze_13, full_default_1);  unsqueeze_13 = full_default_1 = None
        return (div, mm, amax, log, mm_1, amax_1, log_1, mm_2, amax_2, log_2, mm_3, amax_3, log_3, mm_4, amax_4, log_4, mm_5, amax_5, log_5, mm_6, amax_6, log_6, convert_element_type_28, ne_44, where_14, permute_8, ne_46, where_16, ne_48, where_18, ne_50, where_20, ne_52, where_22, ne_54, where_24, ne_56, where_26, primals_4, primals_2, floordiv, sym_size_int_5)
        
def load_args(reader):
    buf0 = reader.storage(None, 1158266880, device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf0, (201088, 2880), dtype=torch.bfloat16, is_leaf=True)  # primals_1
    reader.symint(1359)  # primals_2
    buf1 = reader.storage(None, 16*s0, device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf1, (2, s0), dtype=torch.int64, is_leaf=True)  # primals_3
    reader.symint(1359)  # primals_4
    buf2 = reader.storage(None, 5760*s2*(s1 - 1) + 5760*s2, device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf2, (2, s3, 2880), dtype=torch.bfloat16, is_leaf=True)  # primals_5
    buf3 = reader.storage(None, 8, device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf3, (), dtype=torch.int64, is_leaf=True)  # primals_6
load_args._version = 0
mod = Repro()
if __name__ == '__main__':
    from torch._dynamo.repro.after_aot import run_repro
    with torch.no_grad():
        run_repro(mod, load_args, accuracy=False, command='run', save_dir=None, tracing_mode='symbolic', check_str=None)
        # To run it separately, do 
        # mod, args = run_repro(mod, load_args, accuracy=False, command='get_args', save_dir=None, tracing_mode='symbolic', check_str=None)
        # mod(*args)