class GraphModule(torch.nn.Module):
    def forward(self, primals_1: "bf16[201088, 2880]", primals_2: "Sym(s0)", primals_3: "i64[2, s0]", primals_4: "Sym(s3)", primals_5: "bf16[2, s3, 2880]", primals_6: "i64[]"):
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:517 in _unsloth_compiled_fused_ce_loss_function, code: shift_labels = torch.empty_like(output_labels, device = device)
        empty: "i64[2, s0]" = torch.ops.aten.empty.memory_format([2, primals_2], dtype = torch.int64, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        permute: "i64[2, s0]" = torch.ops.aten.permute.default(empty, [0, 1]);  empty = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:518 in _unsloth_compiled_fused_ce_loss_function, code: shift_labels[..., :-1] = output_labels[..., 1:]
        slice_1: "i64[2, s0 - 1]" = torch.ops.aten.slice.Tensor(primals_3, 1, 1, 9223372036854775807);  primals_3 = None
        slice_scatter: "i64[2, s0]" = torch.ops.aten.slice_scatter.default(permute, slice_1, 1, 0, -1);  slice_1 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:523 in _unsloth_compiled_fused_ce_loss_function, code: shift_labels[..., -1] = -100
        full_default: "i64[]" = torch.ops.aten.full.default([], -100, dtype = torch.int64, layout = torch.strided, device = device(type='cpu'), pin_memory = False)
        select_1: "i64[2]" = torch.ops.aten.select.int(slice_scatter, 1, -1)
        copy_1: "i64[2]" = torch.ops.aten.copy.default(select_1, full_default);  select_1 = full_default = None
        select_scatter: "i64[2, s0]" = torch.ops.aten.select_scatter.default(slice_scatter, copy_1, 1, -1);  slice_scatter = copy_1 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:524 in _unsloth_compiled_fused_ce_loss_function, code: shift_labels = shift_labels.view(-1)
        sym_numel_default: "Sym(2*s0)" = torch.ops.aten.sym_numel.default(permute);  permute = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:528 in _unsloth_compiled_fused_ce_loss_function, code: hidden_states = hidden_states.view(-1, hd)
        view_1: "bf16[2*s3, 2880]" = torch.ops.aten.view.default(primals_5, [-1, 2880]);  primals_5 = None
        sym_size_int_4: "Sym(2*s3)" = torch.ops.aten.sym_size.int(view_1, 0)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:530 in _unsloth_compiled_fused_ce_loss_function, code: __shift_states = torch.chunk(hidden_states, n_chunks, dim = 0)
        add_29: "Sym(2*s3 + 7)" = sym_size_int_4 + 7;  sym_size_int_4 = None
        sub_8: "Sym(2*s3 + 6)" = add_29 - 1;  add_29 = None
        floordiv: "Sym(((2*s3 + 6)//7))" = sub_8 // 7;  sub_8 = None
        split = torch.ops.aten.split.Tensor(view_1, floordiv);  view_1 = None
        getitem: "bf16[((2*s3 + 6)//7), 2880]" = split[0]
        getitem_1: "bf16[((2*s3 + 6)//7), 2880]" = split[1]
        getitem_2: "bf16[((2*s3 + 6)//7), 2880]" = split[2]
        getitem_3: "bf16[((2*s3 + 6)//7), 2880]" = split[3]
        getitem_4: "bf16[((2*s3 + 6)//7), 2880]" = split[4]
        getitem_5: "bf16[((2*s3 + 6)//7), 2880]" = split[5]
        getitem_6: "bf16[2*s3 - 6*(((2*s3 + 6)//7)), 2880]" = split[6];  split = None
        sym_size_int_5: "Sym(2*s3 - 6*(((2*s3 + 6)//7)))" = torch.ops.aten.sym_size.int(getitem_6, 0)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:531 in _unsloth_compiled_fused_ce_loss_function, code: __shift_labels = torch.chunk(shift_labels,  n_chunks, dim = 0)
        add_51: "Sym(2*s0 + 7)" = sym_numel_default + 7;  sym_numel_default = None
        sub_16: "Sym(2*s0 + 6)" = add_51 - 1;  add_51 = None
        floordiv_1: "Sym(((2*s0 + 6)//7))" = sub_16 // 7;  sub_16 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        permute_1: "bf16[2880, 201088]" = torch.ops.aten.permute.default(primals_1, [1, 0]);  primals_1 = None
        mm: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem, permute_1);  getitem = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_2: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_2, [1], True)
        sub_26: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_2, amax);  convert_element_type_2 = None
        exp: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_26)
        sum_1: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp, [1], True);  exp = None
        log: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_1);  sum_1 = None
        sub_27: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_26, log);  sub_26 = None
        view_2: "i64[2*s0]" = torch.ops.aten.view.default(select_scatter, [-1]);  select_scatter = None
        split_2 = torch.ops.aten.split.Tensor(view_2, floordiv_1);  view_2 = floordiv_1 = None
        getitem_14: "i64[((2*s0 + 6)//7)]" = split_2[0]
        ne_5: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_14, -100)
        full_default_1: "i64[]" = torch.ops.aten.full.default([], 0, dtype = torch.int64, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        where: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_5, getitem_14, full_default_1)
        unsqueeze: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where, 1);  where = None
        gather: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_27, 1, unsqueeze);  sub_27 = unsqueeze = None
        squeeze: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather, 1);  gather = None
        neg: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze);  squeeze = None
        full_default_2: "f32[]" = torch.ops.aten.full.default([], 0.0, dtype = torch.float32, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        where_1: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_5, neg, full_default_2);  ne_5 = neg = None
        sum_3: "f32[]" = torch.ops.aten.sum.default(where_1);  where_1 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_78: "f32[]" = torch.ops.aten.add.Tensor(sum_3, 0.0);  sum_3 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_1: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem_1, permute_1);  getitem_1 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_6: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm_1, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_1: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_6, [1], True)
        sub_32: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_6, amax_1);  convert_element_type_6 = None
        exp_1: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_32)
        sum_4: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp_1, [1], True);  exp_1 = None
        log_1: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_4);  sum_4 = None
        sub_33: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_32, log_1);  sub_32 = None
        getitem_22: "i64[((2*s0 + 6)//7)]" = split_2[1]
        ne_11: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_22, -100)
        where_2: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_11, getitem_22, full_default_1)
        unsqueeze_1: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where_2, 1);  where_2 = None
        gather_1: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_33, 1, unsqueeze_1);  sub_33 = unsqueeze_1 = None
        squeeze_1: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather_1, 1);  gather_1 = None
        neg_1: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze_1);  squeeze_1 = None
        where_3: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_11, neg_1, full_default_2);  ne_11 = neg_1 = None
        sum_6: "f32[]" = torch.ops.aten.sum.default(where_3);  where_3 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_91: "f32[]" = torch.ops.aten.add.Tensor(add_78, sum_6);  add_78 = sum_6 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_2: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem_2, permute_1);  getitem_2 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_10: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm_2, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_2: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_10, [1], True)
        sub_38: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_10, amax_2);  convert_element_type_10 = None
        exp_2: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_38)
        sum_7: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp_2, [1], True);  exp_2 = None
        log_2: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_7);  sum_7 = None
        sub_39: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_38, log_2);  sub_38 = None
        getitem_30: "i64[((2*s0 + 6)//7)]" = split_2[2]
        ne_17: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_30, -100)
        where_4: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_17, getitem_30, full_default_1)
        unsqueeze_2: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where_4, 1);  where_4 = None
        gather_2: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_39, 1, unsqueeze_2);  sub_39 = unsqueeze_2 = None
        squeeze_2: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather_2, 1);  gather_2 = None
        neg_2: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze_2);  squeeze_2 = None
        where_5: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_17, neg_2, full_default_2);  ne_17 = neg_2 = None
        sum_9: "f32[]" = torch.ops.aten.sum.default(where_5);  where_5 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_104: "f32[]" = torch.ops.aten.add.Tensor(add_91, sum_9);  add_91 = sum_9 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_3: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem_3, permute_1);  getitem_3 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_14: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm_3, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_3: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_14, [1], True)
        sub_44: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_14, amax_3);  convert_element_type_14 = None
        exp_3: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_44)
        sum_10: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp_3, [1], True);  exp_3 = None
        log_3: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_10);  sum_10 = None
        sub_45: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_44, log_3);  sub_44 = None
        getitem_38: "i64[((2*s0 + 6)//7)]" = split_2[3]
        ne_23: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_38, -100)
        where_6: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_23, getitem_38, full_default_1)
        unsqueeze_3: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where_6, 1);  where_6 = None
        gather_3: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_45, 1, unsqueeze_3);  sub_45 = unsqueeze_3 = None
        squeeze_3: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather_3, 1);  gather_3 = None
        neg_3: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze_3);  squeeze_3 = None
        where_7: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_23, neg_3, full_default_2);  ne_23 = neg_3 = None
        sum_12: "f32[]" = torch.ops.aten.sum.default(where_7);  where_7 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_117: "f32[]" = torch.ops.aten.add.Tensor(add_104, sum_12);  add_104 = sum_12 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_4: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem_4, permute_1);  getitem_4 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_18: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm_4, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_4: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_18, [1], True)
        sub_50: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_18, amax_4);  convert_element_type_18 = None
        exp_4: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_50)
        sum_13: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp_4, [1], True);  exp_4 = None
        log_4: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_13);  sum_13 = None
        sub_51: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_50, log_4);  sub_50 = None
        getitem_46: "i64[((2*s0 + 6)//7)]" = split_2[4]
        ne_29: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_46, -100)
        where_8: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_29, getitem_46, full_default_1)
        unsqueeze_4: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where_8, 1);  where_8 = None
        gather_4: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_51, 1, unsqueeze_4);  sub_51 = unsqueeze_4 = None
        squeeze_4: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather_4, 1);  gather_4 = None
        neg_4: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze_4);  squeeze_4 = None
        where_9: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_29, neg_4, full_default_2);  ne_29 = neg_4 = None
        sum_15: "f32[]" = torch.ops.aten.sum.default(where_9);  where_9 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_130: "f32[]" = torch.ops.aten.add.Tensor(add_117, sum_15);  add_117 = sum_15 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_5: "bf16[((2*s3 + 6)//7), 201088]" = torch.ops.aten.mm.default(getitem_5, permute_1);  getitem_5 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_22: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.prims.convert_element_type.default(mm_5, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_5: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.amax.default(convert_element_type_22, [1], True)
        sub_56: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_22, amax_5);  convert_element_type_22 = None
        exp_5: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.exp.default(sub_56)
        sum_16: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.sum.dim_IntList(exp_5, [1], True);  exp_5 = None
        log_5: "f32[((2*s3 + 6)//7), 1]" = torch.ops.aten.log.default(sum_16);  sum_16 = None
        sub_57: "f32[((2*s3 + 6)//7), 201088]" = torch.ops.aten.sub.Tensor(sub_56, log_5);  sub_56 = None
        getitem_54: "i64[((2*s0 + 6)//7)]" = split_2[5]
        ne_35: "b8[((2*s0 + 6)//7)]" = torch.ops.aten.ne.Scalar(getitem_54, -100)
        where_10: "i64[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_35, getitem_54, full_default_1)
        unsqueeze_5: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(where_10, 1);  where_10 = None
        gather_5: "f32[((2*s0 + 6)//7), 1]" = torch.ops.aten.gather.default(sub_57, 1, unsqueeze_5);  sub_57 = unsqueeze_5 = None
        squeeze_5: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.squeeze.dim(gather_5, 1);  gather_5 = None
        neg_5: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.neg.default(squeeze_5);  squeeze_5 = None
        where_11: "f32[((2*s0 + 6)//7)]" = torch.ops.aten.where.self(ne_35, neg_5, full_default_2);  ne_35 = neg_5 = None
        sum_18: "f32[]" = torch.ops.aten.sum.default(where_11);  where_11 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_143: "f32[]" = torch.ops.aten.add.Tensor(add_130, sum_18);  add_130 = sum_18 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        mm_6: "bf16[2*s3 - 6*(((2*s3 + 6)//7)), 201088]" = torch.ops.aten.mm.default(getitem_6, permute_1);  getitem_6 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:554 in _unsloth_compiled_fused_ce_loss_function, code: input  = _shift_logits.float().contiguous(),
        convert_element_type_26: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 201088]" = torch.ops.prims.convert_element_type.default(mm_6, torch.float32)
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        amax_6: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 1]" = torch.ops.aten.amax.default(convert_element_type_26, [1], True)
        sub_62: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 201088]" = torch.ops.aten.sub.Tensor(convert_element_type_26, amax_6);  convert_element_type_26 = None
        exp_6: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 201088]" = torch.ops.aten.exp.default(sub_62)
        sum_19: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 1]" = torch.ops.aten.sum.dim_IntList(exp_6, [1], True);  exp_6 = None
        log_6: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 1]" = torch.ops.aten.log.default(sum_19);  sum_19 = None
        sub_63: "f32[2*s3 - 6*(((2*s3 + 6)//7)), 201088]" = torch.ops.aten.sub.Tensor(sub_62, log_6);  sub_62 = None
        getitem_62: "i64[2*s0 - 6*(((2*s0 + 6)//7))]" = split_2[6];  split_2 = None
        ne_41: "b8[2*s0 - 6*(((2*s0 + 6)//7))]" = torch.ops.aten.ne.Scalar(getitem_62, -100)
        where_12: "i64[2*s0 - 6*(((2*s0 + 6)//7))]" = torch.ops.aten.where.self(ne_41, getitem_62, full_default_1)
        unsqueeze_6: "i64[2*s0 - 6*(((2*s0 + 6)//7)), 1]" = torch.ops.aten.unsqueeze.default(where_12, 1);  where_12 = None
        gather_6: "f32[2*s0 - 6*(((2*s0 + 6)//7)), 1]" = torch.ops.aten.gather.default(sub_63, 1, unsqueeze_6);  sub_63 = unsqueeze_6 = None
        squeeze_6: "f32[2*s0 - 6*(((2*s0 + 6)//7))]" = torch.ops.aten.squeeze.dim(gather_6, 1);  gather_6 = None
        neg_6: "f32[2*s0 - 6*(((2*s0 + 6)//7))]" = torch.ops.aten.neg.default(squeeze_6);  squeeze_6 = None
        where_13: "f32[2*s0 - 6*(((2*s0 + 6)//7))]" = torch.ops.aten.where.self(ne_41, neg_6, full_default_2);  ne_41 = neg_6 = full_default_2 = None
        sum_21: "f32[]" = torch.ops.aten.sum.default(where_13);  where_13 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:553 in _unsloth_compiled_fused_ce_loss_function, code: loss += torch.nn.functional.cross_entropy(
        add_156: "f32[]" = torch.ops.aten.add.Tensor(add_143, sum_21);  add_143 = sum_21 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:560 in _unsloth_compiled_fused_ce_loss_function, code: loss = loss / torch.tensor(divisor, dtype = torch.float32)
        convert_element_type_28: "f32[]" = torch.ops.prims.convert_element_type.default(primals_6, torch.float32);  primals_6 = None
        div: "f32[]" = torch.ops.aten.div.Tensor(add_156, convert_element_type_28);  add_156 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        unsqueeze_7: "i64[2*s0 - 6*(((2*s0 + 6)//7)), 1]" = torch.ops.aten.unsqueeze.default(getitem_62, 1);  getitem_62 = None
        ne_44: "b8[2*s0 - 6*(((2*s0 + 6)//7)), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_7, -100)
        where_14: "i64[2*s0 - 6*(((2*s0 + 6)//7)), 1]" = torch.ops.aten.where.self(ne_44, unsqueeze_7, full_default_1);  unsqueeze_7 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/loss_utils.py:537 in _unsloth_compiled_fused_ce_loss_function, code: _shift_logits = torch.nn.functional.linear(
        permute_8: "bf16[201088, 2880]" = torch.ops.aten.permute.default(permute_1, [1, 0]);  permute_1 = None
        
         # File: /mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth_zoo/patch_torch_functions.py:164 in cross_entropy, code: return torch._C._nn.cross_entropy_loss(
        unsqueeze_8: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_54, 1);  getitem_54 = None
        ne_46: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_8, -100)
        where_16: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_46, unsqueeze_8, full_default_1);  unsqueeze_8 = None
        unsqueeze_9: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_46, 1);  getitem_46 = None
        ne_48: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_9, -100)
        where_18: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_48, unsqueeze_9, full_default_1);  unsqueeze_9 = None
        unsqueeze_10: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_38, 1);  getitem_38 = None
        ne_50: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_10, -100)
        where_20: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_50, unsqueeze_10, full_default_1);  unsqueeze_10 = None
        unsqueeze_11: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_30, 1);  getitem_30 = None
        ne_52: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_11, -100)
        where_22: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_52, unsqueeze_11, full_default_1);  unsqueeze_11 = None
        unsqueeze_12: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_22, 1);  getitem_22 = None
        ne_54: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_12, -100)
        where_24: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_54, unsqueeze_12, full_default_1);  unsqueeze_12 = None
        unsqueeze_13: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.unsqueeze.default(getitem_14, 1);  getitem_14 = None
        ne_56: "b8[((2*s0 + 6)//7), 1]" = torch.ops.aten.ne.Scalar(unsqueeze_13, -100)
        where_26: "i64[((2*s0 + 6)//7), 1]" = torch.ops.aten.where.self(ne_56, unsqueeze_13, full_default_1);  unsqueeze_13 = full_default_1 = None
        return (div, mm, amax, log, mm_1, amax_1, log_1, mm_2, amax_2, log_2, mm_3, amax_3, log_3, mm_4, amax_4, log_4, mm_5, amax_5, log_5, mm_6, amax_6, log_6, convert_element_type_28, ne_44, where_14, permute_8, ne_46, where_16, ne_48, where_18, ne_50, where_20, ne_52, where_22, ne_54, where_24, ne_56, where_26, primals_4, primals_2, floordiv, sym_size_int_5)
        