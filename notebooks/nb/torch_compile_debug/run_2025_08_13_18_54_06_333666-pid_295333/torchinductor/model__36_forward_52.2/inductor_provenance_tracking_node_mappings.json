{"preToPost": {"shift_labels": ["empty", "permute"], "getitem": ["slice_1"], "setitem": ["slice_1", "slice_scatter_default"], "setitem_1": ["full_default", "select_1", "copy_1", "select_scatter_default"], "hidden_states": ["view_1"], "chunk": ["add_19", "sub_8", "floordiv", "split", "getitem", "getitem_1", "getitem_2", "getitem_3", "getitem_4", "getitem_5", "getitem_6", "sym_size_int_5"], "chunk_1": ["add_41", "sub_16", "floordiv_1"], "_shift_logits": ["permute_1", "mm"], "float_1": ["convert_element_type_2"], "cross_entropy_loss": ["getitem_75", "getitem_76", "sub_tensor_6", "log", "sub_27", "view_2", "split_2", "getitem_14", "ne_4", "full_default_1", "where", "unsqueeze", "gather", "squeeze", "neg", "full_default_2", "where_1", "sum_3"], "loss": ["add_68"], "_shift_logits_1": ["mm_1"], "float_2": ["convert_element_type_6"], "cross_entropy_loss_1": ["getitem_73", "getitem_74", "sub_tensor_5", "log_1", "sub_33", "getitem_22", "ne_10", "where_2", "unsqueeze_1", "gather_1", "squeeze_1", "neg_1", "where_3", "sum_6"], "loss_1": ["add_81"], "_shift_logits_2": ["mm_2"], "float_3": ["convert_element_type_10"], "cross_entropy_loss_2": ["getitem_71", "getitem_72", "sub_tensor_4", "log_2", "sub_39", "getitem_30", "ne_16", "where_4", "unsqueeze_2", "gather_2", "squeeze_2", "neg_2", "where_5", "sum_9"], "loss_2": ["add_94"], "_shift_logits_3": ["mm_3"], "float_4": ["convert_element_type_14"], "cross_entropy_loss_3": ["getitem_69", "getitem_70", "sub_tensor_3", "log_3", "sub_45", "getitem_38", "ne_22", "where_6", "unsqueeze_3", "gather_3", "squeeze_3", "neg_3", "where_7", "sum_12"], "loss_3": ["add_107"], "_shift_logits_4": ["mm_4"], "float_5": ["convert_element_type_18"], "cross_entropy_loss_4": ["getitem_67", "getitem_68", "sub_tensor_2", "log_4", "sub_51", "getitem_46", "ne_28", "where_8", "unsqueeze_4", "gather_4", "squeeze_4", "neg_4", "where_9", "sum_15"], "loss_4": ["add_120"], "_shift_logits_5": ["mm_5"], "float_6": ["convert_element_type_22"], "cross_entropy_loss_5": ["getitem_65", "getitem_66", "sub_tensor_1", "log_5", "sub_57", "getitem_54", "ne_34", "where_10", "unsqueeze_5", "gather_5", "squeeze_5", "neg_5", "where_11", "sum_18"], "loss_5": ["add_133"], "_shift_logits_6": ["mm_6"], "float_7": ["convert_element_type_26"], "cross_entropy_loss_6": ["getitem_63", "getitem_64", "sub_tensor", "log_6", "sub_63", "getitem_62", "ne_40", "where_12", "unsqueeze_6", "gather_6", "squeeze_6", "neg_6", "where_13", "sum_21"], "loss_6": ["add_146"], "tensor": ["convert_element_type_28"], "loss_7": ["div"]}, "postToPre": {"empty": ["shift_labels"], "permute": ["shift_labels"], "slice_1": ["getitem", "setitem"], "slice_scatter_default": ["setitem"], "full_default": ["setitem_1"], "select_1": ["setitem_1"], "copy_1": ["setitem_1"], "select_scatter_default": ["setitem_1"], "view_1": ["hidden_states"], "add_19": ["chunk"], "sub_8": ["chunk"], "floordiv": ["chunk"], "split": ["chunk"], "getitem": ["chunk"], "getitem_1": ["chunk"], "getitem_2": ["chunk"], "getitem_3": ["chunk"], "getitem_4": ["chunk"], "getitem_5": ["chunk"], "getitem_6": ["chunk"], "sym_size_int_5": ["chunk"], "add_41": ["chunk_1"], "sub_16": ["chunk_1"], "floordiv_1": ["chunk_1"], "permute_1": ["_shift_logits"], "mm": ["_shift_logits"], "convert_element_type_2": ["float_1"], "getitem_75": ["cross_entropy_loss"], "getitem_76": ["cross_entropy_loss"], "sub_tensor_6": ["cross_entropy_loss"], "log": ["cross_entropy_loss"], "sub_27": ["cross_entropy_loss"], "view_2": ["cross_entropy_loss"], "split_2": ["cross_entropy_loss"], "getitem_14": ["cross_entropy_loss"], "ne_4": ["cross_entropy_loss"], "full_default_1": ["cross_entropy_loss"], "where": ["cross_entropy_loss"], "unsqueeze": ["cross_entropy_loss"], "gather": ["cross_entropy_loss"], "squeeze": ["cross_entropy_loss"], "neg": ["cross_entropy_loss"], "full_default_2": ["cross_entropy_loss"], "where_1": ["cross_entropy_loss"], "sum_3": ["cross_entropy_loss"], "add_68": ["loss"], "mm_1": ["_shift_logits_1"], "convert_element_type_6": ["float_2"], "getitem_73": ["cross_entropy_loss_1"], "getitem_74": ["cross_entropy_loss_1"], "sub_tensor_5": ["cross_entropy_loss_1"], "log_1": ["cross_entropy_loss_1"], "sub_33": ["cross_entropy_loss_1"], "getitem_22": ["cross_entropy_loss_1"], "ne_10": ["cross_entropy_loss_1"], "where_2": ["cross_entropy_loss_1"], "unsqueeze_1": ["cross_entropy_loss_1"], "gather_1": ["cross_entropy_loss_1"], "squeeze_1": ["cross_entropy_loss_1"], "neg_1": ["cross_entropy_loss_1"], "where_3": ["cross_entropy_loss_1"], "sum_6": ["cross_entropy_loss_1"], "add_81": ["loss_1"], "mm_2": ["_shift_logits_2"], "convert_element_type_10": ["float_3"], "getitem_71": ["cross_entropy_loss_2"], "getitem_72": ["cross_entropy_loss_2"], "sub_tensor_4": ["cross_entropy_loss_2"], "log_2": ["cross_entropy_loss_2"], "sub_39": ["cross_entropy_loss_2"], "getitem_30": ["cross_entropy_loss_2"], "ne_16": ["cross_entropy_loss_2"], "where_4": ["cross_entropy_loss_2"], "unsqueeze_2": ["cross_entropy_loss_2"], "gather_2": ["cross_entropy_loss_2"], "squeeze_2": ["cross_entropy_loss_2"], "neg_2": ["cross_entropy_loss_2"], "where_5": ["cross_entropy_loss_2"], "sum_9": ["cross_entropy_loss_2"], "add_94": ["loss_2"], "mm_3": ["_shift_logits_3"], "convert_element_type_14": ["float_4"], "getitem_69": ["cross_entropy_loss_3"], "getitem_70": ["cross_entropy_loss_3"], "sub_tensor_3": ["cross_entropy_loss_3"], "log_3": ["cross_entropy_loss_3"], "sub_45": ["cross_entropy_loss_3"], "getitem_38": ["cross_entropy_loss_3"], "ne_22": ["cross_entropy_loss_3"], "where_6": ["cross_entropy_loss_3"], "unsqueeze_3": ["cross_entropy_loss_3"], "gather_3": ["cross_entropy_loss_3"], "squeeze_3": ["cross_entropy_loss_3"], "neg_3": ["cross_entropy_loss_3"], "where_7": ["cross_entropy_loss_3"], "sum_12": ["cross_entropy_loss_3"], "add_107": ["loss_3"], "mm_4": ["_shift_logits_4"], "convert_element_type_18": ["float_5"], "getitem_67": ["cross_entropy_loss_4"], "getitem_68": ["cross_entropy_loss_4"], "sub_tensor_2": ["cross_entropy_loss_4"], "log_4": ["cross_entropy_loss_4"], "sub_51": ["cross_entropy_loss_4"], "getitem_46": ["cross_entropy_loss_4"], "ne_28": ["cross_entropy_loss_4"], "where_8": ["cross_entropy_loss_4"], "unsqueeze_4": ["cross_entropy_loss_4"], "gather_4": ["cross_entropy_loss_4"], "squeeze_4": ["cross_entropy_loss_4"], "neg_4": ["cross_entropy_loss_4"], "where_9": ["cross_entropy_loss_4"], "sum_15": ["cross_entropy_loss_4"], "add_120": ["loss_4"], "mm_5": ["_shift_logits_5"], "convert_element_type_22": ["float_6"], "getitem_65": ["cross_entropy_loss_5"], "getitem_66": ["cross_entropy_loss_5"], "sub_tensor_1": ["cross_entropy_loss_5"], "log_5": ["cross_entropy_loss_5"], "sub_57": ["cross_entropy_loss_5"], "getitem_54": ["cross_entropy_loss_5"], "ne_34": ["cross_entropy_loss_5"], "where_10": ["cross_entropy_loss_5"], "unsqueeze_5": ["cross_entropy_loss_5"], "gather_5": ["cross_entropy_loss_5"], "squeeze_5": ["cross_entropy_loss_5"], "neg_5": ["cross_entropy_loss_5"], "where_11": ["cross_entropy_loss_5"], "sum_18": ["cross_entropy_loss_5"], "add_133": ["loss_5"], "mm_6": ["_shift_logits_6"], "convert_element_type_26": ["float_7"], "getitem_63": ["cross_entropy_loss_6"], "getitem_64": ["cross_entropy_loss_6"], "sub_tensor": ["cross_entropy_loss_6"], "log_6": ["cross_entropy_loss_6"], "sub_63": ["cross_entropy_loss_6"], "getitem_62": ["cross_entropy_loss_6"], "ne_40": ["cross_entropy_loss_6"], "where_12": ["cross_entropy_loss_6"], "unsqueeze_6": ["cross_entropy_loss_6"], "gather_6": ["cross_entropy_loss_6"], "squeeze_6": ["cross_entropy_loss_6"], "neg_6": ["cross_entropy_loss_6"], "where_13": ["cross_entropy_loss_6"], "sum_21": ["cross_entropy_loss_6"], "add_146": ["loss_6"], "convert_element_type_28": ["tensor"], "div": ["loss_7"]}, "cppCodeToPost": {"triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0": ["log_6"], "triton_red_fused_nll_loss_backward_nll_loss_forward_1": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_2": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_3": ["where_22", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_4": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_5": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_6": ["where_22", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7": ["div", "add_156", "add_143", "add_130", "add_117", "add_104", "add_91", "add_78"], "triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0": ["convert_element_type_29", "sub_66", "mul_104", "scatter_upon_const_tensor", "where_15", "div_1", "full_default_2", "mul_105", "exp_7", "sub_63", "sub_62", "convert_element_type_26"], "triton_poi_fused_nll_loss_backward_1": ["scatter_6", "full_1"], "triton_poi_fused_nll_loss_backward_2": ["scatter_6", "full_1"], "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3": ["sum_28", "mul_116", "where_27", "div_1", "full_default_2"], "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4": ["convert_element_type_47", "sub_72", "mul_116", "where_27", "div_1", "full_default_2", "mul_117", "exp_13", "sub_27", "sub_26", "convert_element_type_2"], "triton_red_fused__to_copy_prepare_softmax_online_0": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_per_fused__to_copy_prepare_softmax_online_1": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_red_fused__to_copy_prepare_softmax_online_2": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_per_fused__log_softmax__to_copy_prepare_softmax_online_3": ["log_5"], "triton_red_fused_nll_loss_backward_nll_loss_forward_7": ["where_20", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_8": ["where_18", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_9": ["where_16", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10": ["div", "add_146", "add_133", "add_120", "add_107", "add_94", "add_81", "add_68"]}, "postToCppCode": {"log_6": ["triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0"], "where_26": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_4"], "full_default_1": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_4", "triton_red_fused_nll_loss_backward_nll_loss_forward_5", "triton_red_fused_nll_loss_backward_nll_loss_forward_6", "triton_red_fused_nll_loss_backward_nll_loss_forward_7", "triton_red_fused_nll_loss_backward_nll_loss_forward_8", "triton_red_fused_nll_loss_backward_nll_loss_forward_9"], "where_24": ["triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_5"], "where_22": ["triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "div": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7", "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_156": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_143": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_130": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_117": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_104": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_91": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_78": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "convert_element_type_29": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_66": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "mul_104": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_upon_const_tensor": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "where_15": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "div_1": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "full_default_2": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "mul_105": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "exp_7": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_63": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_62": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "convert_element_type_26": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_6": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "full_1": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "sum_28": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3"], "mul_116": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "where_27": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "convert_element_type_47": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_72": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "mul_117": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "exp_13": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_27": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_26": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "convert_element_type_2": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "prepare_softmax_online_default_1": ["triton_red_fused__to_copy_prepare_softmax_online_0", "triton_per_fused__to_copy_prepare_softmax_online_1", "triton_red_fused__to_copy_prepare_softmax_online_2"], "convert_element_type_22": ["triton_red_fused__to_copy_prepare_softmax_online_0", "triton_per_fused__to_copy_prepare_softmax_online_1", "triton_red_fused__to_copy_prepare_softmax_online_2"], "log_5": ["triton_per_fused__log_softmax__to_copy_prepare_softmax_online_3"], "where_20": ["triton_red_fused_nll_loss_backward_nll_loss_forward_7"], "where_18": ["triton_red_fused_nll_loss_backward_nll_loss_forward_8"], "where_16": ["triton_red_fused_nll_loss_backward_nll_loss_forward_9"], "add_146": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_133": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_120": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_107": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_94": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_81": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_68": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"]}}