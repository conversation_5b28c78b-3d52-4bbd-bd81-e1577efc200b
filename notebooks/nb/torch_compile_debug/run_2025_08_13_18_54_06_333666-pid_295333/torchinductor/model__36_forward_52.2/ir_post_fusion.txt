op0: NopK<PERSON>lSchedulerNode(ComputedBuffer)
op0.writes = [MemoryDep('buf0', d1, {d0: 0, d1: 0})]
op0.unmet_dependencies = []
op0.met_dependencies = []
op0.outputs = [
    buf0: ComputedBuffer
    buf0.layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
    buf0.users = [
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op51'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op52'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op53'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op54'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op55'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op56'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op57'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op58'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op59'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op60'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op61'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op62'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op63'), can_inplace=True, is_weak=False),
        NodeUser(node=SchedulerNode(name='op64'), can_inplace=True, is_weak=False),
    ]
]


op43: ExternKernelSchedulerNode(ExternKernelOut)
op43.writes = [StarDep(name='buf43', mode=None)]
op43.unmet_dependencies = []
op43.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op43.outputs = [
    buf43: ExternKernelOut
    buf43.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
    buf43.users = [
        NodeUser(node=SchedulerNode(name='op44'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op46'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op43.node.kernel = extern_kernels.mm


op44: SchedulerNode(ComputedBuffer)
op44.writes = [MemoryDep('buf44', c0, {c0: 3*s2 - 18*(((s2 + 6)//7))})]
op44.unmet_dependencies = [MemoryDep('buf43', 201088*c0 + c1, {c0: s2 - 6*(((s2 + 6)//7)), c1: 201090})]
op44.met_dependencies = []
op44.outputs = [
    buf44: ComputedBuffer
    buf44.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
    buf44.users = [NodeUser(node=SchedulerNode(name='op45'), can_inplace=False, is_weak=False)]
]
op44.group.device = cuda:0
op44.group.iteration = (3*s2 - 18*(((s2 + 6)//7)), 67030)
op44.sizes = ([s2 - 6*(((s2 + 6)//7)), 3], [67030])
buf43_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
buf44_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
class op44_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf44', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf43', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op45: SchedulerNode(ComputedBuffer)
op45.writes = [MemoryDep('buf45', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op45.unmet_dependencies = [MemoryDep('buf44', c0, {c0: 3*s2 - 18*(((s2 + 6)//7))})]
op45.met_dependencies = []
op45.outputs = [
    buf45: ComputedBuffer
    buf45.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
    buf45.users = [
        NodeUser(node=SchedulerNode(name='op46'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op45.group.device = cuda:0
op45.group.iteration = (s2 - 6*(((s2 + 6)//7)), 3)
op45.sizes = ([s2 - 6*(((s2 + 6)//7))], [3])
buf44_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
buf45_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
class op45_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf44', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf45', get_index_1, reduction)
        return store_reduction


op46: SchedulerNode(ComputedBuffer)
op46.writes = [MemoryDep('buf46', c0, {c0: 3*s2 - 18*(((s2 + 6)//7))})]
op46.unmet_dependencies = 
    [   MemoryDep('buf43', 201088*c0 + c1, {c0: s2 - 6*(((s2 + 6)//7)), c1: 201090}),
        MemoryDep('buf45', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op46.met_dependencies = []
op46.outputs = [
    buf46: ComputedBuffer
    buf46.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
    buf46.users = [NodeUser(node=SchedulerNode(name='op47'), can_inplace=False, is_weak=False)]
]
op46.group.device = cuda:0
op46.group.iteration = (3*s2 - 18*(((s2 + 6)//7)), 67030)
op46.sizes = ([s2 - 6*(((s2 + 6)//7)), 3], [67030])
buf43_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
buf45_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
buf46_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
class op46_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf46', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf43', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf45', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op47_op48: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op47_op48.writes = 
    [   MemoryDep('buf47', c0, {c0: s2 - 6*(((s2 + 6)//7))}),
        MemoryDep('buf48', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op47_op48.unmet_dependencies = [MemoryDep('buf46', c0, {c0: 3*s2 - 18*(((s2 + 6)//7))})]
op47_op48.met_dependencies = []
op47_op48.outputs = [
    buf47: ComputedBuffer
    buf47.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
    buf47.users = [NodeUser(node=SchedulerNode(name='op48'), can_inplace=True, is_weak=False)]
    buf48: ComputedBuffer
    buf48.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
    buf48.users = [
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op47_op48.snodes[0] =
op47: SchedulerNode(ComputedBuffer)
op47.writes = [MemoryDep('buf47', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op47.unmet_dependencies = [MemoryDep('buf46', c0, {c0: 3*s2 - 18*(((s2 + 6)//7))})]
op47.met_dependencies = []
op47.outputs = [
    buf47: ComputedBuffer
    buf47.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
    buf47.users = [NodeUser(node=SchedulerNode(name='op48'), can_inplace=True, is_weak=False)]
]
op47.group.device = cuda:0
op47.group.iteration = (s2 - 6*(((s2 + 6)//7)), 3)
op47.sizes = ([s2 - 6*(((s2 + 6)//7))], [3])
buf46_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1, 3], stride=[3, 3*s2 - 18*(((s2 + 6)//7)), 1])
buf47_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
class op47_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf46', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf47', get_index_1, reduction)
        return store_reduction
op47_op48.snodes[1] =
op48: SchedulerNode(ComputedBuffer)
op48.writes = [MemoryDep('buf48', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op48.unmet_dependencies = [MemoryDep('buf47', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op48.met_dependencies = []
op48.outputs = [
    buf48: ComputedBuffer
    buf48.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
    buf48.users = [
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op48.group.device = cuda:0
op48.group.iteration = (s2 - 6*(((s2 + 6)//7)), 1)
op48.sizes = ([s2 - 6*(((s2 + 6)//7))], [])
buf47_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
buf48_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
class op48_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf47', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf48', get_index_1, log, None)
        return store


op1: ExternKernelSchedulerNode(ExternKernelOut)
op1.writes = [StarDep(name='buf1', mode=None)]
op1.unmet_dependencies = []
op1.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op1.outputs = [
    buf1: ExternKernelOut
    buf1.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf1.users = [
        NodeUser(node=SchedulerNode(name='op2'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op4'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op1.node.kernel = extern_kernels.mm


op2: SchedulerNode(ComputedBuffer)
op2.writes = [MemoryDep('buf2', c0, {c0: 3*(((s2 + 6)//7))})]
op2.unmet_dependencies = [MemoryDep('buf1', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op2.met_dependencies = []
op2.outputs = [
    buf2: ComputedBuffer
    buf2.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf2.users = [NodeUser(node=SchedulerNode(name='op3'), can_inplace=False, is_weak=False)]
]
op2.group.device = cuda:0
op2.group.iteration = (3*(((s2 + 6)//7)), 67030)
op2.sizes = ([((s2 + 6)//7), 3], [67030])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf2_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op2_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf2', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf1', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op3: SchedulerNode(ComputedBuffer)
op3.writes = [MemoryDep('buf3', c0, {c0: ((s2 + 6)//7)})]
op3.unmet_dependencies = [MemoryDep('buf2', c0, {c0: 3*(((s2 + 6)//7))})]
op3.met_dependencies = []
op3.outputs = [
    buf3: ComputedBuffer
    buf3.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf3.users = [
        NodeUser(node=SchedulerNode(name='op4'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op3.group.device = cuda:0
op3.group.iteration = (((s2 + 6)//7), 3)
op3.sizes = ([((s2 + 6)//7)], [3])
buf2_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op3_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf2', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf3', get_index_1, reduction)
        return store_reduction


op4: SchedulerNode(ComputedBuffer)
op4.writes = [MemoryDep('buf4', c0, {c0: 3*(((s2 + 6)//7))})]
op4.unmet_dependencies = 
    [   MemoryDep('buf1', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090}),
        MemoryDep('buf3', c0, {c0: ((s2 + 6)//7)})]
op4.met_dependencies = []
op4.outputs = [
    buf4: ComputedBuffer
    buf4.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf4.users = [NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False)]
]
op4.group.device = cuda:0
op4.group.iteration = (3*(((s2 + 6)//7)), 67030)
op4.sizes = ([((s2 + 6)//7), 3], [67030])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf4_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op4_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf4', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf1', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf3', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op5_op6: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op5_op6.writes = 
    [   MemoryDep('buf5', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf6', c0, {c0: ((s2 + 6)//7)})]
op5_op6.unmet_dependencies = [MemoryDep('buf4', c0, {c0: 3*(((s2 + 6)//7))})]
op5_op6.met_dependencies = []
op5_op6.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf5.users = [NodeUser(node=SchedulerNode(name='op6'), can_inplace=True, is_weak=False)]
    buf6: ComputedBuffer
    buf6.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf6.users = [
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op5_op6.snodes[0] =
op5: SchedulerNode(ComputedBuffer)
op5.writes = [MemoryDep('buf5', c0, {c0: ((s2 + 6)//7)})]
op5.unmet_dependencies = [MemoryDep('buf4', c0, {c0: 3*(((s2 + 6)//7))})]
op5.met_dependencies = []
op5.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf5.users = [NodeUser(node=SchedulerNode(name='op6'), can_inplace=True, is_weak=False)]
]
op5.group.device = cuda:0
op5.group.iteration = (((s2 + 6)//7), 3)
op5.sizes = ([((s2 + 6)//7)], [3])
buf4_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op5_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf4', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf5', get_index_1, reduction)
        return store_reduction
op5_op6.snodes[1] =
op6: SchedulerNode(ComputedBuffer)
op6.writes = [MemoryDep('buf6', c0, {c0: ((s2 + 6)//7)})]
op6.unmet_dependencies = [MemoryDep('buf5', c0, {c0: ((s2 + 6)//7)})]
op6.met_dependencies = []
op6.outputs = [
    buf6: ComputedBuffer
    buf6.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf6.users = [
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op6.group.device = cuda:0
op6.group.iteration = (((s2 + 6)//7), 1)
op6.sizes = ([((s2 + 6)//7)], [])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf6_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op6_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf5', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf6', get_index_1, log, None)
        return store


op7_op63_op64: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op7_op63_op64.writes = 
    [   MemoryDep('buf63', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf64', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf7', 0, {})]
op7_op63_op64.unmet_dependencies = 
    [   MemoryDep('buf0', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf1', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf3', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf6', c0, {c0: ((s0 + 6)//7)})]
op7_op63_op64.met_dependencies = [MemoryDep('primals_3', c0 + 1, {c0: ((s0 + 6)//7)})]
op7_op63_op64.outputs = [
    buf7: ComputedBuffer
    buf7.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf7.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf63: ComputedBuffer
    buf63.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf63.users = [
        NodeUser(node=SchedulerNode(name='op64'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf64: ComputedBuffer
    buf64.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf64.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op7_op63_op64.snodes[0] =
op7: SchedulerNode(ComputedBuffer)
op7.writes = [MemoryDep('buf7', 0, {})]
op7.unmet_dependencies = 
    [   MemoryDep('buf0', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf1', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf3', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf6', c0, {c0: ((s0 + 6)//7)})]
op7.met_dependencies = [MemoryDep('primals_3', c0 + 1, {c0: ((s0 + 6)//7)})]
op7.outputs = [
    buf7: ComputedBuffer
    buf7.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf7.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op7.group.device = cuda:0
op7.group.iteration = (1, ((s0 + 6)//7))
op7.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf6_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf7_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op7_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = s0 - 1
    index2 = p0 + 1
    index3 = indirect0 + 201088*p0
    index4 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf1', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index0')
        load_4 = ops.load('buf3', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index0')
        load_5 = ops.load('buf6', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf7', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op7_op63_op64.snodes[1] =
op63: SchedulerNode(ComputedBuffer)
op63.writes = [MemoryDep('buf63', c0, {c0: ((s0 + 6)//7)})]
op63.unmet_dependencies = [MemoryDep('buf0', c0, {c0: ((s0 + 6)//7)})]
op63.met_dependencies = [MemoryDep('primals_3', c0 + 1, {c0: ((s0 + 6)//7)})]
op63.outputs = [
    buf63: ComputedBuffer
    buf63.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf63.users = [
        NodeUser(node=SchedulerNode(name='op64'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op63.group.device = cuda:0
op63.group.iteration = (((s0 + 6)//7), 1)
op63.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf63_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op63_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = s0 - 1
    index2 = p0 + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        store = ops.store('buf63', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op7_op63_op64.snodes[2] =
op64: SchedulerNode(ComputedBuffer)
op64.writes = [MemoryDep('buf64', c0, {c0: ((s0 + 6)//7)})]
op64.unmet_dependencies = 
    [   MemoryDep('buf0', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf63', c0, {c0: ((s0 + 6)//7)})]
op64.met_dependencies = [MemoryDep('primals_3', c0 + 1, {c0: ((s0 + 6)//7)})]
op64.outputs = [
    buf64: ComputedBuffer
    buf64.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf64.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op64.group.device = cuda:0
op64.group.iteration = (((s0 + 6)//7), 1)
op64.sizes = ([((s0 + 6)//7)], [])
buf63_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf64_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op64_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = s0 - 1
    index2 = p0 + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf63', get_index)
        get_index_1 = self.get_index('index0')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf64', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load


op8: ExternKernelSchedulerNode(ExternKernelOut)
op8.writes = [StarDep(name='buf8', mode=None)]
op8.unmet_dependencies = []
op8.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op8.outputs = [
    buf8: ExternKernelOut
    buf8.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf8.users = [
        NodeUser(node=SchedulerNode(name='op9'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op11'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op8.node.kernel = extern_kernels.mm


op9: SchedulerNode(ComputedBuffer)
op9.writes = [MemoryDep('buf9', c0, {c0: 3*(((s2 + 6)//7))})]
op9.unmet_dependencies = [MemoryDep('buf8', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op9.met_dependencies = []
op9.outputs = [
    buf9: ComputedBuffer
    buf9.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf9.users = [NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False)]
]
op9.group.device = cuda:0
op9.group.iteration = (3*(((s2 + 6)//7)), 67030)
op9.sizes = ([((s2 + 6)//7), 3], [67030])
buf8_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op9_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf9', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf8', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op10: SchedulerNode(ComputedBuffer)
op10.writes = [MemoryDep('buf10', c0, {c0: ((s2 + 6)//7)})]
op10.unmet_dependencies = [MemoryDep('buf9', c0, {c0: 3*(((s2 + 6)//7))})]
op10.met_dependencies = []
op10.outputs = [
    buf10: ComputedBuffer
    buf10.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf10.users = [
        NodeUser(node=SchedulerNode(name='op11'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op10.group.device = cuda:0
op10.group.iteration = (((s2 + 6)//7), 3)
op10.sizes = ([((s2 + 6)//7)], [3])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op10_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf9', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf10', get_index_1, reduction)
        return store_reduction


op11: SchedulerNode(ComputedBuffer)
op11.writes = [MemoryDep('buf11', c0, {c0: 3*(((s2 + 6)//7))})]
op11.unmet_dependencies = 
    [   MemoryDep('buf10', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf8', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op11.met_dependencies = []
op11.outputs = [
    buf11: ComputedBuffer
    buf11.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf11.users = [NodeUser(node=SchedulerNode(name='op12'), can_inplace=False, is_weak=False)]
]
op11.group.device = cuda:0
op11.group.iteration = (3*(((s2 + 6)//7)), 67030)
op11.sizes = ([((s2 + 6)//7), 3], [67030])
buf8_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf11_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op11_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf11', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf8', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf10', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op12_op13: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op12_op13.writes = 
    [   MemoryDep('buf12', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf13', c0, {c0: ((s2 + 6)//7)})]
op12_op13.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 3*(((s2 + 6)//7))})]
op12_op13.met_dependencies = []
op12_op13.outputs = [
    buf12: ComputedBuffer
    buf12.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf12.users = [NodeUser(node=SchedulerNode(name='op13'), can_inplace=True, is_weak=False)]
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf13.users = [
        NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op12_op13.snodes[0] =
op12: SchedulerNode(ComputedBuffer)
op12.writes = [MemoryDep('buf12', c0, {c0: ((s2 + 6)//7)})]
op12.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 3*(((s2 + 6)//7))})]
op12.met_dependencies = []
op12.outputs = [
    buf12: ComputedBuffer
    buf12.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf12.users = [NodeUser(node=SchedulerNode(name='op13'), can_inplace=True, is_weak=False)]
]
op12.group.device = cuda:0
op12.group.iteration = (((s2 + 6)//7), 3)
op12.sizes = ([((s2 + 6)//7)], [3])
buf11_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op12_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf11', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf12', get_index_1, reduction)
        return store_reduction
op12_op13.snodes[1] =
op13: SchedulerNode(ComputedBuffer)
op13.writes = [MemoryDep('buf13', c0, {c0: ((s2 + 6)//7)})]
op13.unmet_dependencies = [MemoryDep('buf12', c0, {c0: ((s2 + 6)//7)})]
op13.met_dependencies = []
op13.outputs = [
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf13.users = [
        NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op13.group.device = cuda:0
op13.group.iteration = (((s2 + 6)//7), 1)
op13.sizes = ([((s2 + 6)//7)], [])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op13_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf12', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf13', get_index_1, log, None)
        return store


op14_op61_op62: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op14_op61_op62.writes = 
    [   MemoryDep('buf14', 0, {}),
        MemoryDep('buf61', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf62', c0, {c0: ((s0 + 6)//7)})]
op14_op61_op62.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + (((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf10', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf13', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf8', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op14_op61_op62.met_dependencies = [MemoryDep('primals_3', c0 + (((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op14_op61_op62.outputs = [
    buf14: ComputedBuffer
    buf14.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf14.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf61: ComputedBuffer
    buf61.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf61.users = [
        NodeUser(node=SchedulerNode(name='op62'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf62: ComputedBuffer
    buf62.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf62.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op14_op61_op62.snodes[0] =
op14: SchedulerNode(ComputedBuffer)
op14.writes = [MemoryDep('buf14', 0, {})]
op14.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + (((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf10', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf13', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf8', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op14.met_dependencies = [MemoryDep('primals_3', c0 + (((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op14.outputs = [
    buf14: ComputedBuffer
    buf14.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf14.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op14.group.device = cuda:0
op14.group.iteration = (1, ((s0 + 6)//7))
op14.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf8_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf14_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op14_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + (((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + (((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf8', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf10', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf13', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf14', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op14_op61_op62.snodes[1] =
op61: SchedulerNode(ComputedBuffer)
op61.writes = [MemoryDep('buf61', c0, {c0: ((s0 + 6)//7)})]
op61.unmet_dependencies = [MemoryDep('buf0', c0 + (((s0 + 6)//7)), {c0: ((s0 + 6)//7)})]
op61.met_dependencies = [MemoryDep('primals_3', c0 + (((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op61.outputs = [
    buf61: ComputedBuffer
    buf61.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf61.users = [
        NodeUser(node=SchedulerNode(name='op62'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op61.group.device = cuda:0
op61.group.iteration = (((s0 + 6)//7), 1)
op61.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf61_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op61_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + (((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + (((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf61', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op14_op61_op62.snodes[2] =
op62: SchedulerNode(ComputedBuffer)
op62.writes = [MemoryDep('buf62', c0, {c0: ((s0 + 6)//7)})]
op62.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + (((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf61', c0, {c0: ((s0 + 6)//7)})]
op62.met_dependencies = [MemoryDep('primals_3', c0 + (((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op62.outputs = [
    buf62: ComputedBuffer
    buf62.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf62.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op62.group.device = cuda:0
op62.group.iteration = (((s0 + 6)//7), 1)
op62.sizes = ([((s0 + 6)//7)], [])
buf61_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf62_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op62_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = p0 + (((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + (((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf61', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf62', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load


op15: ExternKernelSchedulerNode(ExternKernelOut)
op15.writes = [StarDep(name='buf15', mode=None)]
op15.unmet_dependencies = []
op15.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op15.outputs = [
    buf15: ExternKernelOut
    buf15.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf15.users = [
        NodeUser(node=SchedulerNode(name='op16'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op18'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op15.node.kernel = extern_kernels.mm


op16: SchedulerNode(ComputedBuffer)
op16.writes = [MemoryDep('buf16', c0, {c0: 3*(((s2 + 6)//7))})]
op16.unmet_dependencies = [MemoryDep('buf15', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op16.met_dependencies = []
op16.outputs = [
    buf16: ComputedBuffer
    buf16.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf16.users = [NodeUser(node=SchedulerNode(name='op17'), can_inplace=False, is_weak=False)]
]
op16.group.device = cuda:0
op16.group.iteration = (3*(((s2 + 6)//7)), 67030)
op16.sizes = ([((s2 + 6)//7), 3], [67030])
buf15_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf16_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op16_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf16', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf15', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op17: SchedulerNode(ComputedBuffer)
op17.writes = [MemoryDep('buf17', c0, {c0: ((s2 + 6)//7)})]
op17.unmet_dependencies = [MemoryDep('buf16', c0, {c0: 3*(((s2 + 6)//7))})]
op17.met_dependencies = []
op17.outputs = [
    buf17: ComputedBuffer
    buf17.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf17.users = [
        NodeUser(node=SchedulerNode(name='op18'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op17.group.device = cuda:0
op17.group.iteration = (((s2 + 6)//7), 3)
op17.sizes = ([((s2 + 6)//7)], [3])
buf16_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op17_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf16', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf17', get_index_1, reduction)
        return store_reduction


op18: SchedulerNode(ComputedBuffer)
op18.writes = [MemoryDep('buf18', c0, {c0: 3*(((s2 + 6)//7))})]
op18.unmet_dependencies = 
    [   MemoryDep('buf15', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090}),
        MemoryDep('buf17', c0, {c0: ((s2 + 6)//7)})]
op18.met_dependencies = []
op18.outputs = [
    buf18: ComputedBuffer
    buf18.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf18.users = [NodeUser(node=SchedulerNode(name='op19'), can_inplace=False, is_weak=False)]
]
op18.group.device = cuda:0
op18.group.iteration = (3*(((s2 + 6)//7)), 67030)
op18.sizes = ([((s2 + 6)//7), 3], [67030])
buf15_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op18_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf18', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf15', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf17', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op19_op20: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op19_op20.writes = 
    [   MemoryDep('buf19', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf20', c0, {c0: ((s2 + 6)//7)})]
op19_op20.unmet_dependencies = [MemoryDep('buf18', c0, {c0: 3*(((s2 + 6)//7))})]
op19_op20.met_dependencies = []
op19_op20.outputs = [
    buf19: ComputedBuffer
    buf19.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf19.users = [NodeUser(node=SchedulerNode(name='op20'), can_inplace=True, is_weak=False)]
    buf20: ComputedBuffer
    buf20.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf20.users = [
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op19_op20.snodes[0] =
op19: SchedulerNode(ComputedBuffer)
op19.writes = [MemoryDep('buf19', c0, {c0: ((s2 + 6)//7)})]
op19.unmet_dependencies = [MemoryDep('buf18', c0, {c0: 3*(((s2 + 6)//7))})]
op19.met_dependencies = []
op19.outputs = [
    buf19: ComputedBuffer
    buf19.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf19.users = [NodeUser(node=SchedulerNode(name='op20'), can_inplace=True, is_weak=False)]
]
op19.group.device = cuda:0
op19.group.iteration = (((s2 + 6)//7), 3)
op19.sizes = ([((s2 + 6)//7)], [3])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf19_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op19_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf18', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf19', get_index_1, reduction)
        return store_reduction
op19_op20.snodes[1] =
op20: SchedulerNode(ComputedBuffer)
op20.writes = [MemoryDep('buf20', c0, {c0: ((s2 + 6)//7)})]
op20.unmet_dependencies = [MemoryDep('buf19', c0, {c0: ((s2 + 6)//7)})]
op20.met_dependencies = []
op20.outputs = [
    buf20: ComputedBuffer
    buf20.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf20.users = [
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op20.group.device = cuda:0
op20.group.iteration = (((s2 + 6)//7), 1)
op20.sizes = ([((s2 + 6)//7)], [])
buf19_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op20_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf19', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf20', get_index_1, log, None)
        return store


op21_op59_op60: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op21_op59_op60.writes = 
    [   MemoryDep('buf21', 0, {}),
        MemoryDep('buf59', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf60', c0, {c0: ((s0 + 6)//7)})]
op21_op59_op60.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 2*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf15', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf17', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf20', c0, {c0: ((s0 + 6)//7)})]
op21_op59_op60.met_dependencies = [MemoryDep('primals_3', c0 + 2*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op21_op59_op60.outputs = [
    buf21: ComputedBuffer
    buf21.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf21.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf59: ComputedBuffer
    buf59.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf59.users = [
        NodeUser(node=SchedulerNode(name='op60'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf60: ComputedBuffer
    buf60.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf60.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op21_op59_op60.snodes[0] =
op21: SchedulerNode(ComputedBuffer)
op21.writes = [MemoryDep('buf21', 0, {})]
op21.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 2*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf15', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf17', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf20', c0, {c0: ((s0 + 6)//7)})]
op21.met_dependencies = [MemoryDep('primals_3', c0 + 2*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op21.outputs = [
    buf21: ComputedBuffer
    buf21.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf21.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op21.group.device = cuda:0
op21.group.iteration = (1, ((s0 + 6)//7))
op21.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf15_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf21_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op21_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 2*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 2*(((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf15', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf17', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf20', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf21', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op21_op59_op60.snodes[1] =
op59: SchedulerNode(ComputedBuffer)
op59.writes = [MemoryDep('buf59', c0, {c0: ((s0 + 6)//7)})]
op59.unmet_dependencies = [MemoryDep('buf0', c0 + 2*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)})]
op59.met_dependencies = [MemoryDep('primals_3', c0 + 2*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op59.outputs = [
    buf59: ComputedBuffer
    buf59.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf59.users = [
        NodeUser(node=SchedulerNode(name='op60'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op59.group.device = cuda:0
op59.group.iteration = (((s0 + 6)//7), 1)
op59.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf59_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op59_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 2*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 2*(((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf59', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op21_op59_op60.snodes[2] =
op60: SchedulerNode(ComputedBuffer)
op60.writes = [MemoryDep('buf60', c0, {c0: ((s0 + 6)//7)})]
op60.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 2*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf59', c0, {c0: ((s0 + 6)//7)})]
op60.met_dependencies = [MemoryDep('primals_3', c0 + 2*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op60.outputs = [
    buf60: ComputedBuffer
    buf60.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf60.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op60.group.device = cuda:0
op60.group.iteration = (((s0 + 6)//7), 1)
op60.sizes = ([((s0 + 6)//7)], [])
buf59_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf60_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op60_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = p0 + 2*(((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + 2*(((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf59', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf60', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load


op22: ExternKernelSchedulerNode(ExternKernelOut)
op22.writes = [StarDep(name='buf22', mode=None)]
op22.unmet_dependencies = []
op22.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op22.outputs = [
    buf22: ExternKernelOut
    buf22.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf22.users = [
        NodeUser(node=SchedulerNode(name='op23'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op22.node.kernel = extern_kernels.mm


op23: SchedulerNode(ComputedBuffer)
op23.writes = [MemoryDep('buf23', c0, {c0: 3*(((s2 + 6)//7))})]
op23.unmet_dependencies = [MemoryDep('buf22', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op23.met_dependencies = []
op23.outputs = [
    buf23: ComputedBuffer
    buf23.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf23.users = [NodeUser(node=SchedulerNode(name='op24'), can_inplace=False, is_weak=False)]
]
op23.group.device = cuda:0
op23.group.iteration = (3*(((s2 + 6)//7)), 67030)
op23.sizes = ([((s2 + 6)//7), 3], [67030])
buf22_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op23_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf23', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf22', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op24: SchedulerNode(ComputedBuffer)
op24.writes = [MemoryDep('buf24', c0, {c0: ((s2 + 6)//7)})]
op24.unmet_dependencies = [MemoryDep('buf23', c0, {c0: 3*(((s2 + 6)//7))})]
op24.met_dependencies = []
op24.outputs = [
    buf24: ComputedBuffer
    buf24.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf24.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op24.group.device = cuda:0
op24.group.iteration = (((s2 + 6)//7), 3)
op24.sizes = ([((s2 + 6)//7)], [3])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op24_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf23', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf24', get_index_1, reduction)
        return store_reduction


op25: SchedulerNode(ComputedBuffer)
op25.writes = [MemoryDep('buf25', c0, {c0: 3*(((s2 + 6)//7))})]
op25.unmet_dependencies = 
    [   MemoryDep('buf22', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090}),
        MemoryDep('buf24', c0, {c0: ((s2 + 6)//7)})]
op25.met_dependencies = []
op25.outputs = [
    buf25: ComputedBuffer
    buf25.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf25.users = [NodeUser(node=SchedulerNode(name='op26'), can_inplace=False, is_weak=False)]
]
op25.group.device = cuda:0
op25.group.iteration = (3*(((s2 + 6)//7)), 67030)
op25.sizes = ([((s2 + 6)//7), 3], [67030])
buf22_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op25_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf25', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf22', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf24', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op26_op27: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op26_op27.writes = 
    [   MemoryDep('buf26', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf27', c0, {c0: ((s2 + 6)//7)})]
op26_op27.unmet_dependencies = [MemoryDep('buf25', c0, {c0: 3*(((s2 + 6)//7))})]
op26_op27.met_dependencies = []
op26_op27.outputs = [
    buf26: ComputedBuffer
    buf26.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf26.users = [NodeUser(node=SchedulerNode(name='op27'), can_inplace=True, is_weak=False)]
    buf27: ComputedBuffer
    buf27.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf27.users = [
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op26_op27.snodes[0] =
op26: SchedulerNode(ComputedBuffer)
op26.writes = [MemoryDep('buf26', c0, {c0: ((s2 + 6)//7)})]
op26.unmet_dependencies = [MemoryDep('buf25', c0, {c0: 3*(((s2 + 6)//7))})]
op26.met_dependencies = []
op26.outputs = [
    buf26: ComputedBuffer
    buf26.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf26.users = [NodeUser(node=SchedulerNode(name='op27'), can_inplace=True, is_weak=False)]
]
op26.group.device = cuda:0
op26.group.iteration = (((s2 + 6)//7), 3)
op26.sizes = ([((s2 + 6)//7)], [3])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf26_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op26_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf25', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf26', get_index_1, reduction)
        return store_reduction
op26_op27.snodes[1] =
op27: SchedulerNode(ComputedBuffer)
op27.writes = [MemoryDep('buf27', c0, {c0: ((s2 + 6)//7)})]
op27.unmet_dependencies = [MemoryDep('buf26', c0, {c0: ((s2 + 6)//7)})]
op27.met_dependencies = []
op27.outputs = [
    buf27: ComputedBuffer
    buf27.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf27.users = [
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op27.group.device = cuda:0
op27.group.iteration = (((s2 + 6)//7), 1)
op27.sizes = ([((s2 + 6)//7)], [])
buf26_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op27_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf26', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf27', get_index_1, log, None)
        return store


op28_op57_op58: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op28_op57_op58.writes = 
    [   MemoryDep('buf28', 0, {}),
        MemoryDep('buf57', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf58', c0, {c0: ((s0 + 6)//7)})]
op28_op57_op58.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 3*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf22', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf24', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf27', c0, {c0: ((s0 + 6)//7)})]
op28_op57_op58.met_dependencies = [MemoryDep('primals_3', c0 + 3*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op28_op57_op58.outputs = [
    buf28: ComputedBuffer
    buf28.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf28.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf57: ComputedBuffer
    buf57.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf57.users = [
        NodeUser(node=SchedulerNode(name='op58'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf58: ComputedBuffer
    buf58.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf58.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op28_op57_op58.snodes[0] =
op28: SchedulerNode(ComputedBuffer)
op28.writes = [MemoryDep('buf28', 0, {})]
op28.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 3*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf22', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf24', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf27', c0, {c0: ((s0 + 6)//7)})]
op28.met_dependencies = [MemoryDep('primals_3', c0 + 3*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op28.outputs = [
    buf28: ComputedBuffer
    buf28.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf28.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op28.group.device = cuda:0
op28.group.iteration = (1, ((s0 + 6)//7))
op28.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf22_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op28_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 3*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 3*(((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf22', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf24', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf27', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf28', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op28_op57_op58.snodes[1] =
op57: SchedulerNode(ComputedBuffer)
op57.writes = [MemoryDep('buf57', c0, {c0: ((s0 + 6)//7)})]
op57.unmet_dependencies = [MemoryDep('buf0', c0 + 3*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)})]
op57.met_dependencies = [MemoryDep('primals_3', c0 + 3*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op57.outputs = [
    buf57: ComputedBuffer
    buf57.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf57.users = [
        NodeUser(node=SchedulerNode(name='op58'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op57.group.device = cuda:0
op57.group.iteration = (((s0 + 6)//7), 1)
op57.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf57_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op57_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 3*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 3*(((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf57', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op28_op57_op58.snodes[2] =
op58: SchedulerNode(ComputedBuffer)
op58.writes = [MemoryDep('buf58', c0, {c0: ((s0 + 6)//7)})]
op58.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 3*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf57', c0, {c0: ((s0 + 6)//7)})]
op58.met_dependencies = [MemoryDep('primals_3', c0 + 3*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op58.outputs = [
    buf58: ComputedBuffer
    buf58.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf58.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op58.group.device = cuda:0
op58.group.iteration = (((s0 + 6)//7), 1)
op58.sizes = ([((s0 + 6)//7)], [])
buf57_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf58_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op58_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = p0 + 3*(((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + 3*(((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf57', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf58', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load


op29: ExternKernelSchedulerNode(ExternKernelOut)
op29.writes = [StarDep(name='buf29', mode=None)]
op29.unmet_dependencies = []
op29.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op29.outputs = [
    buf29: ExternKernelOut
    buf29.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf29.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op32'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op29.node.kernel = extern_kernels.mm


op30: SchedulerNode(ComputedBuffer)
op30.writes = [MemoryDep('buf30', c0, {c0: 3*(((s2 + 6)//7))})]
op30.unmet_dependencies = [MemoryDep('buf29', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op30.met_dependencies = []
op30.outputs = [
    buf30: ComputedBuffer
    buf30.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf30.users = [NodeUser(node=SchedulerNode(name='op31'), can_inplace=False, is_weak=False)]
]
op30.group.device = cuda:0
op30.group.iteration = (3*(((s2 + 6)//7)), 67030)
op30.sizes = ([((s2 + 6)//7), 3], [67030])
buf29_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op30_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf30', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf29', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op31: SchedulerNode(ComputedBuffer)
op31.writes = [MemoryDep('buf31', c0, {c0: ((s2 + 6)//7)})]
op31.unmet_dependencies = [MemoryDep('buf30', c0, {c0: 3*(((s2 + 6)//7))})]
op31.met_dependencies = []
op31.outputs = [
    buf31: ComputedBuffer
    buf31.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf31.users = [
        NodeUser(node=SchedulerNode(name='op32'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op31.group.device = cuda:0
op31.group.iteration = (((s2 + 6)//7), 3)
op31.sizes = ([((s2 + 6)//7)], [3])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf31_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op31_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf30', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf31', get_index_1, reduction)
        return store_reduction


op32: SchedulerNode(ComputedBuffer)
op32.writes = [MemoryDep('buf32', c0, {c0: 3*(((s2 + 6)//7))})]
op32.unmet_dependencies = 
    [   MemoryDep('buf29', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090}),
        MemoryDep('buf31', c0, {c0: ((s2 + 6)//7)})]
op32.met_dependencies = []
op32.outputs = [
    buf32: ComputedBuffer
    buf32.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf32.users = [NodeUser(node=SchedulerNode(name='op33'), can_inplace=False, is_weak=False)]
]
op32.group.device = cuda:0
op32.group.iteration = (3*(((s2 + 6)//7)), 67030)
op32.sizes = ([((s2 + 6)//7), 3], [67030])
buf29_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf31_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf32_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op32_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf32', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf29', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf31', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op33_op34: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op33_op34.writes = 
    [   MemoryDep('buf33', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf34', c0, {c0: ((s2 + 6)//7)})]
op33_op34.unmet_dependencies = [MemoryDep('buf32', c0, {c0: 3*(((s2 + 6)//7))})]
op33_op34.met_dependencies = []
op33_op34.outputs = [
    buf33: ComputedBuffer
    buf33.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf33.users = [NodeUser(node=SchedulerNode(name='op34'), can_inplace=True, is_weak=False)]
    buf34: ComputedBuffer
    buf34.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf34.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op33_op34.snodes[0] =
op33: SchedulerNode(ComputedBuffer)
op33.writes = [MemoryDep('buf33', c0, {c0: ((s2 + 6)//7)})]
op33.unmet_dependencies = [MemoryDep('buf32', c0, {c0: 3*(((s2 + 6)//7))})]
op33.met_dependencies = []
op33.outputs = [
    buf33: ComputedBuffer
    buf33.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf33.users = [NodeUser(node=SchedulerNode(name='op34'), can_inplace=True, is_weak=False)]
]
op33.group.device = cuda:0
op33.group.iteration = (((s2 + 6)//7), 3)
op33.sizes = ([((s2 + 6)//7)], [3])
buf32_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op33_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf32', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf33', get_index_1, reduction)
        return store_reduction
op33_op34.snodes[1] =
op34: SchedulerNode(ComputedBuffer)
op34.writes = [MemoryDep('buf34', c0, {c0: ((s2 + 6)//7)})]
op34.unmet_dependencies = [MemoryDep('buf33', c0, {c0: ((s2 + 6)//7)})]
op34.met_dependencies = []
op34.outputs = [
    buf34: ComputedBuffer
    buf34.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf34.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op34.group.device = cuda:0
op34.group.iteration = (((s2 + 6)//7), 1)
op34.sizes = ([((s2 + 6)//7)], [])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf34_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op34_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf33', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf34', get_index_1, log, None)
        return store


op35_op55_op56: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op35_op55_op56.writes = 
    [   MemoryDep('buf35', 0, {}),
        MemoryDep('buf55', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf56', c0, {c0: ((s0 + 6)//7)})]
op35_op55_op56.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 4*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf29', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf31', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf34', c0, {c0: ((s0 + 6)//7)})]
op35_op55_op56.met_dependencies = [MemoryDep('primals_3', c0 + 4*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op35_op55_op56.outputs = [
    buf35: ComputedBuffer
    buf35.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf35.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf55: ComputedBuffer
    buf55.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf55.users = [
        NodeUser(node=SchedulerNode(name='op56'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf56: ComputedBuffer
    buf56.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf56.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op35_op55_op56.snodes[0] =
op35: SchedulerNode(ComputedBuffer)
op35.writes = [MemoryDep('buf35', 0, {})]
op35.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 4*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf29', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf31', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf34', c0, {c0: ((s0 + 6)//7)})]
op35.met_dependencies = [MemoryDep('primals_3', c0 + 4*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op35.outputs = [
    buf35: ComputedBuffer
    buf35.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf35.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op35.group.device = cuda:0
op35.group.iteration = (1, ((s0 + 6)//7))
op35.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf29_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf31_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf34_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op35_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 4*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 4*(((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf29', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf31', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf34', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf35', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op35_op55_op56.snodes[1] =
op55: SchedulerNode(ComputedBuffer)
op55.writes = [MemoryDep('buf55', c0, {c0: ((s0 + 6)//7)})]
op55.unmet_dependencies = [MemoryDep('buf0', c0 + 4*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)})]
op55.met_dependencies = [MemoryDep('primals_3', c0 + 4*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op55.outputs = [
    buf55: ComputedBuffer
    buf55.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf55.users = [
        NodeUser(node=SchedulerNode(name='op56'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op55.group.device = cuda:0
op55.group.iteration = (((s0 + 6)//7), 1)
op55.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op55_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 4*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 4*(((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf55', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op35_op55_op56.snodes[2] =
op56: SchedulerNode(ComputedBuffer)
op56.writes = [MemoryDep('buf56', c0, {c0: ((s0 + 6)//7)})]
op56.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 4*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf55', c0, {c0: ((s0 + 6)//7)})]
op56.met_dependencies = [MemoryDep('primals_3', c0 + 4*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op56.outputs = [
    buf56: ComputedBuffer
    buf56.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf56.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op56.group.device = cuda:0
op56.group.iteration = (((s0 + 6)//7), 1)
op56.sizes = ([((s0 + 6)//7)], [])
buf55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf56_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op56_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = p0 + 4*(((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + 4*(((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf55', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf56', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load


op36: ExternKernelSchedulerNode(ExternKernelOut)
op36.writes = [StarDep(name='buf36', mode=None)]
op36.unmet_dependencies = []
op36.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_5', mode=None)]
op36.outputs = [
    buf36: ExternKernelOut
    buf36.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf36.users = [
        NodeUser(node=SchedulerNode(name='op37'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op36.node.kernel = extern_kernels.mm


op37: SchedulerNode(ComputedBuffer)
op37.writes = [MemoryDep('buf37', c0, {c0: 3*(((s2 + 6)//7))})]
op37.unmet_dependencies = [MemoryDep('buf36', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op37.met_dependencies = []
op37.outputs = [
    buf37: ComputedBuffer
    buf37.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf37.users = [NodeUser(node=SchedulerNode(name='op38'), can_inplace=False, is_weak=False)]
]
op37.group.device = cuda:0
op37.group.iteration = (3*(((s2 + 6)//7)), 67030)
op37.sizes = ([((s2 + 6)//7), 3], [67030])
buf36_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf37_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op37_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, -inf)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', masked_subblock1)
        get_index_1 = self.get_index('index2')
        store_reduction = ops.store_reduction('buf37', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf36', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        return to_dtype


op38: SchedulerNode(ComputedBuffer)
op38.writes = [MemoryDep('buf38', c0, {c0: ((s2 + 6)//7)})]
op38.unmet_dependencies = [MemoryDep('buf37', c0, {c0: 3*(((s2 + 6)//7))})]
op38.met_dependencies = []
op38.outputs = [
    buf38: ComputedBuffer
    buf38.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf38.users = [
        NodeUser(node=SchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op38.group.device = cuda:0
op38.group.iteration = (((s2 + 6)//7), 3)
op38.sizes = ([((s2 + 6)//7)], [3])
buf37_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf38_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op38_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf37', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'max', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf38', get_index_1, reduction)
        return store_reduction


op39: SchedulerNode(ComputedBuffer)
op39.writes = [MemoryDep('buf39', c0, {c0: 3*(((s2 + 6)//7))})]
op39.unmet_dependencies = 
    [   MemoryDep('buf36', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090}),
        MemoryDep('buf38', c0, {c0: ((s2 + 6)//7)})]
op39.met_dependencies = []
op39.outputs = [
    buf39: ComputedBuffer
    buf39.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf39.users = [NodeUser(node=SchedulerNode(name='op40'), can_inplace=False, is_weak=False)]
]
op39.group.device = cuda:0
op39.group.iteration = (3*(((s2 + 6)//7)), 67030)
op39.sizes = ([((s2 + 6)//7), 3], [67030])
buf36_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf38_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf39_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op39_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index3')
        store_reduction = ops.store_reduction('buf39', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf36', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('buf38', get_index_1)
        sub = ops.sub(to_dtype, load_1)
        exp = ops.exp(sub)
        return exp


op40_op41: FusedSchedulerNode(SchedulerNode,SchedulerNode)
op40_op41.writes = 
    [   MemoryDep('buf40', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('buf41', c0, {c0: ((s2 + 6)//7)})]
op40_op41.unmet_dependencies = [MemoryDep('buf39', c0, {c0: 3*(((s2 + 6)//7))})]
op40_op41.met_dependencies = []
op40_op41.outputs = [
    buf40: ComputedBuffer
    buf40.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf40.users = [NodeUser(node=SchedulerNode(name='op41'), can_inplace=True, is_weak=False)]
    buf41: ComputedBuffer
    buf41.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf41.users = [
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op40_op41.snodes[0] =
op40: SchedulerNode(ComputedBuffer)
op40.writes = [MemoryDep('buf40', c0, {c0: ((s2 + 6)//7)})]
op40.unmet_dependencies = [MemoryDep('buf39', c0, {c0: 3*(((s2 + 6)//7))})]
op40.met_dependencies = []
op40.outputs = [
    buf40: ComputedBuffer
    buf40.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf40.users = [NodeUser(node=SchedulerNode(name='op41'), can_inplace=True, is_weak=False)]
]
op40.group.device = cuda:0
op40.group.iteration = (((s2 + 6)//7), 3)
op40.sizes = ([((s2 + 6)//7)], [3])
buf39_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf40_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op40_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf39', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf40', get_index_1, reduction)
        return store_reduction
op40_op41.snodes[1] =
op41: SchedulerNode(ComputedBuffer)
op41.writes = [MemoryDep('buf41', c0, {c0: ((s2 + 6)//7)})]
op41.unmet_dependencies = [MemoryDep('buf40', c0, {c0: ((s2 + 6)//7)})]
op41.met_dependencies = []
op41.outputs = [
    buf41: ComputedBuffer
    buf41.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
    buf41.users = [
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op41.group.device = cuda:0
op41.group.iteration = (((s2 + 6)//7), 1)
op41.sizes = ([((s2 + 6)//7)], [])
buf40_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf41_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
class op41_loop_body:
    var_ranges = {p0: ((s2 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf40', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf41', get_index_1, log, None)
        return store


op42_op53_op54: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op42_op53_op54.writes = 
    [   MemoryDep('buf42', 0, {}),
        MemoryDep('buf53', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf54', c0, {c0: ((s0 + 6)//7)})]
op42_op53_op54.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 5*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf36', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf38', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf41', c0, {c0: ((s0 + 6)//7)})]
op42_op53_op54.met_dependencies = [MemoryDep('primals_3', c0 + 5*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op42_op53_op54.outputs = [
    buf42: ComputedBuffer
    buf42.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf42.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf53: ComputedBuffer
    buf53.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf53.users = [
        NodeUser(node=SchedulerNode(name='op54'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf54: ComputedBuffer
    buf54.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf54.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op42_op53_op54.snodes[0] =
op42: SchedulerNode(ComputedBuffer)
op42.writes = [MemoryDep('buf42', 0, {})]
op42.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 5*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf36', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf38', c0, {c0: ((s0 + 6)//7)}),
        MemoryDep('buf41', c0, {c0: ((s0 + 6)//7)})]
op42.met_dependencies = [MemoryDep('primals_3', c0 + 5*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op42.outputs = [
    buf42: ComputedBuffer
    buf42.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf42.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op42.group.device = cuda:0
op42.group.iteration = (1, ((s0 + 6)//7))
op42.sizes = ([], [((s0 + 6)//7)])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf36_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf38_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf41_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf42_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op42_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 5*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 5*(((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf36', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf38', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf41', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf42', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op42_op53_op54.snodes[1] =
op53: SchedulerNode(ComputedBuffer)
op53.writes = [MemoryDep('buf53', c0, {c0: ((s0 + 6)//7)})]
op53.unmet_dependencies = [MemoryDep('buf0', c0 + 5*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)})]
op53.met_dependencies = [MemoryDep('primals_3', c0 + 5*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op53.outputs = [
    buf53: ComputedBuffer
    buf53.layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf53.users = [
        NodeUser(node=SchedulerNode(name='op54'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op53.group.device = cuda:0
op53.group.iteration = (((s0 + 6)//7), 1)
op53.sizes = ([((s0 + 6)//7)], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op53_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0 + 5*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 5*(((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf53', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op42_op53_op54.snodes[2] =
op54: SchedulerNode(ComputedBuffer)
op54.writes = [MemoryDep('buf54', c0, {c0: ((s0 + 6)//7)})]
op54.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 5*(((s0 + 6)//7)), {c0: ((s0 + 6)//7)}),
        MemoryDep('buf53', c0, {c0: ((s0 + 6)//7)})]
op54.met_dependencies = [MemoryDep('primals_3', c0 + 5*(((s0 + 6)//7)) + 1, {c0: ((s0 + 6)//7)})]
op54.outputs = [
    buf54: ComputedBuffer
    buf54.layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
    buf54.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op54.group.device = cuda:0
op54.group.iteration = (((s0 + 6)//7), 1)
op54.sizes = ([((s0 + 6)//7)], [])
buf53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf54_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
class op54_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = p0 + 5*(((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + 5*(((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf53', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf54', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load


op49_op51_op52_op50_op65: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode,SchedulerNode,SchedulerNode)
op49_op51_op52_op50_op65.writes = 
    [   MemoryDep('buf49', 0, {}),
        MemoryDep('buf50', 0, {}),
        MemoryDep('buf51', c0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf52', c0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf65', 0, {})]
op49_op51_op52_op50_op65.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0 + 6)//7)), {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf14', 0, {}),
        MemoryDep('buf21', 0, {}),
        MemoryDep('buf28', 0, {}),
        MemoryDep('buf35', 0, {}),
        MemoryDep('buf42', 0, {}),
        MemoryDep('buf43', 201088*c0 + tmp0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf45', c0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf48', c0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf7', 0, {})]
op49_op51_op52_op50_op65.met_dependencies = 
    [   MemoryDep('primals_3', c0 + 6*(((s0 + 6)//7)) + 1, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('primals_6', 0, {})]
op49_op51_op52_op50_op65.outputs = [
    buf49: ComputedBuffer
    buf49.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf49.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
    buf51: ComputedBuffer
    buf51.layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
    buf51.users = [
        NodeUser(node=SchedulerNode(name='op52'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf52: ComputedBuffer
    buf52.layout = FixedLayout('cuda:0', torch.int64, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
    buf52.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
    buf50: ComputedBuffer
    buf50.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf50.users = [
        NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf65: ComputedBuffer
    buf65.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf65.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op49_op51_op52_op50_op65.snodes[0] =
op49: SchedulerNode(ComputedBuffer)
op49.writes = [MemoryDep('buf49', 0, {})]
op49.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0 + 6)//7)), {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf43', 201088*c0 + tmp0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf45', c0, {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf48', c0, {c0: s0 - 6*(((s0 + 6)//7))})]
op49.met_dependencies = [   MemoryDep('primals_3', c0 + 6*(((s0 + 6)//7)) + 1, {c0: s0 - 6*(((s0 + 6)//7))})]
op49.outputs = [
    buf49: ComputedBuffer
    buf49.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf49.users = [NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False)]
]
op49.group.device = cuda:0
op49.group.iteration = (1, s0 - 6*(((s0 + 6)//7)))
op49.sizes = ([], [s0 - 6*(((s0 + 6)//7))])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf43_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
buf45_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
buf48_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
buf49_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op49_loop_body:
    var_ranges = {p0: s0 - 6*(((s0 + 6)//7))}
    index0 = p0 + 6*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 6*(((s0 + 6)//7)) + 1
    index3 = indirect0 + 201088*p0
    index4 = p0
    index5 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index0')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index0')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index3')
        load_3 = ops.load('buf43', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index4')
        load_4 = ops.load('buf45', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index4')
        load_5 = ops.load('buf48', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index5')
        store_reduction = ops.store_reduction('buf49', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op49_op51_op52_op50_op65.snodes[1] =
op51: SchedulerNode(ComputedBuffer)
op51.writes = [MemoryDep('buf51', c0, {c0: s0 - 6*(((s0 + 6)//7))})]
op51.unmet_dependencies = [MemoryDep('buf0', c0 + 6*(((s0 + 6)//7)), {c0: s0 - 6*(((s0 + 6)//7))})]
op51.met_dependencies = [   MemoryDep('primals_3', c0 + 6*(((s0 + 6)//7)) + 1, {c0: s0 - 6*(((s0 + 6)//7))})]
op51.outputs = [
    buf51: ComputedBuffer
    buf51.layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
    buf51.users = [
        NodeUser(node=SchedulerNode(name='op52'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op51.group.device = cuda:0
op51.group.iteration = (s0 - 6*(((s0 + 6)//7)), 1)
op51.sizes = ([s0 - 6*(((s0 + 6)//7))], [])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf51_layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
class op51_loop_body:
    var_ranges = {p0: s0 - 6*(((s0 + 6)//7))}
    index0 = p0 + 6*(((s0 + 6)//7))
    index1 = s0 - 1
    index2 = p0 + 6*(((s0 + 6)//7)) + 1
    index3 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index0')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index3')
        store = ops.store('buf51', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_3', get_index)
        return load
op49_op51_op52_op50_op65.snodes[2] =
op52: SchedulerNode(ComputedBuffer)
op52.writes = [MemoryDep('buf52', c0, {c0: s0 - 6*(((s0 + 6)//7))})]
op52.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0 + 6)//7)), {c0: s0 - 6*(((s0 + 6)//7))}),
        MemoryDep('buf51', c0, {c0: s0 - 6*(((s0 + 6)//7))})]
op52.met_dependencies = [   MemoryDep('primals_3', c0 + 6*(((s0 + 6)//7)) + 1, {c0: s0 - 6*(((s0 + 6)//7))})]
op52.outputs = [
    buf52: ComputedBuffer
    buf52.layout = FixedLayout('cuda:0', torch.int64, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
    buf52.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op52.group.device = cuda:0
op52.group.iteration = (s0 - 6*(((s0 + 6)//7)), 1)
op52.sizes = ([s0 - 6*(((s0 + 6)//7))], [])
buf51_layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
primals_3_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[1, s0], stride=[s0, 1])
buf52_layout = FixedLayout('cuda:0', torch.int64, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
class op52_loop_body:
    var_ranges = {p0: s0 - 6*(((s0 + 6)//7))}
    index0 = p0
    index1 = p0 + 6*(((s0 + 6)//7))
    index2 = s0 - 1
    index3 = p0 + 6*(((s0 + 6)//7)) + 1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf51', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index1')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf52', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_3', get_index)
        return load
op49_op51_op52_op50_op65.snodes[3] =
op50: SchedulerNode(ComputedBuffer)
op50.writes = [MemoryDep('buf50', 0, {})]
op50.unmet_dependencies = []
op50.met_dependencies = [MemoryDep('primals_6', 0, {})]
op50.outputs = [
    buf50: ComputedBuffer
    buf50.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf50.users = [
        NodeUser(node=SchedulerNode(name='op65'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op50.group.device = cuda:0
op50.group.iteration = (1, 1)
op50.sizes = ([], [])
primals_6_layout = FixedLayout('cuda:0', torch.int64, size=[], stride=[])
buf50_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op50_loop_body:
    var_ranges = {}
    index0 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('primals_6', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.int64)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf50', get_index_1, to_dtype, None)
        return store
op49_op51_op52_op50_op65.snodes[4] =
op65: SchedulerNode(ComputedBuffer)
op65.writes = [MemoryDep('buf65', 0, {})]
op65.unmet_dependencies = 
    [   MemoryDep('buf14', 0, {}),
        MemoryDep('buf21', 0, {}),
        MemoryDep('buf28', 0, {}),
        MemoryDep('buf35', 0, {}),
        MemoryDep('buf42', 0, {}),
        MemoryDep('buf49', 0, {}),
        MemoryDep('buf50', 0, {}),
        MemoryDep('buf7', 0, {})]
op65.met_dependencies = []
op65.outputs = [
    buf65: ComputedBuffer
    buf65.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf65.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op65.group.device = cuda:0
op65.group.iteration = (1, 1)
op65.sizes = ([], [])
buf7_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf14_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf21_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf42_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf49_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf50_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf65_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op65_loop_body:
    var_ranges = {}
    index0 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf7', get_index)
        constant = ops.constant(0.0, torch.float32)
        add = ops.add(load, constant)
        get_index_1 = self.get_index('index0')
        load_1 = ops.load('buf14', get_index_1)
        add_1 = ops.add(add, load_1)
        get_index_2 = self.get_index('index0')
        load_2 = ops.load('buf21', get_index_2)
        add_2 = ops.add(add_1, load_2)
        get_index_3 = self.get_index('index0')
        load_3 = ops.load('buf28', get_index_3)
        add_3 = ops.add(add_2, load_3)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('buf35', get_index_4)
        add_4 = ops.add(add_3, load_4)
        get_index_5 = self.get_index('index0')
        load_5 = ops.load('buf42', get_index_5)
        add_5 = ops.add(add_4, load_5)
        get_index_6 = self.get_index('index0')
        load_6 = ops.load('buf49', get_index_6)
        add_6 = ops.add(add_5, load_6)
        get_index_7 = self.get_index('index0')
        load_7 = ops.load('buf50', get_index_7)
        truediv = ops.truediv(add_6, load_7)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf65', get_index_8, truediv, None)
        return store


