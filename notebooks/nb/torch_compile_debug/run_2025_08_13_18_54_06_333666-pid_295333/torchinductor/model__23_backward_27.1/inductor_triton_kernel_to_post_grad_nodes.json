{"triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0": ["log_6"], "triton_red_fused_nll_loss_backward_nll_loss_forward_1": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_2": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_3": ["where_22", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_4": ["where_20", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_5": ["where_18", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_6": ["where_16", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7": ["div", "add_156", "add_143", "add_130", "add_117", "add_104", "add_91", "add_78"], "triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0": ["convert_element_type_29", "sub_66", "mul_104", "scatter_upon_const_tensor", "where_15", "div_1", "full_default_2", "mul_105", "exp_7", "sub_63", "sub_62", "convert_element_type_26"], "triton_poi_fused_nll_loss_backward_1": ["scatter_6", "full_1"], "triton_poi_fused_nll_loss_backward_2": ["scatter_6", "full_1"], "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3": ["sum_28", "mul_116", "where_27", "div_1", "full_default_2"], "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4": ["convert_element_type_47", "sub_72", "mul_116", "where_27", "div_1", "full_default_2", "mul_117", "exp_13", "sub_27", "sub_26", "convert_element_type_2"]}