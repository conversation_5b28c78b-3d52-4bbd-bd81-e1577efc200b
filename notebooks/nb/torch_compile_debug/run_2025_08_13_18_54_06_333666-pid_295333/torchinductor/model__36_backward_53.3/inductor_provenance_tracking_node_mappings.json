{"preToPost": {"cross_entropy_loss": ["full_default_2", "sub_26", "sub_27"], "float_7": ["convert_element_type_26"], "cross_entropy_loss_6": ["sub_62", "sub_63"], "chunk": ["add_19", "sub_8", "floordiv"], "float_6": ["convert_element_type_22"], "cross_entropy_loss_5": ["sub_56", "sub_57"], "float_5": ["convert_element_type_18"], "cross_entropy_loss_4": ["sub_50", "sub_51"], "float_4": ["convert_element_type_14"], "cross_entropy_loss_3": ["sub_44", "sub_45"], "float_3": ["convert_element_type_10"], "cross_entropy_loss_2": ["sub_38", "sub_39"], "float_2": ["convert_element_type_6"], "cross_entropy_loss_1": ["sub_32", "sub_33"], "float_1": ["convert_element_type_2"]}, "postToPre": {"full_default_2": ["cross_entropy_loss"], "convert_element_type_26": ["float_7"], "sub_62": ["cross_entropy_loss_6"], "sub_63": ["cross_entropy_loss_6"], "add_19": ["chunk"], "sub_8": ["chunk"], "floordiv": ["chunk"], "convert_element_type_22": ["float_6"], "sub_56": ["cross_entropy_loss_5"], "sub_57": ["cross_entropy_loss_5"], "convert_element_type_18": ["float_5"], "sub_50": ["cross_entropy_loss_4"], "sub_51": ["cross_entropy_loss_4"], "convert_element_type_14": ["float_4"], "sub_44": ["cross_entropy_loss_3"], "sub_45": ["cross_entropy_loss_3"], "convert_element_type_10": ["float_3"], "sub_38": ["cross_entropy_loss_2"], "sub_39": ["cross_entropy_loss_2"], "convert_element_type_6": ["float_2"], "sub_32": ["cross_entropy_loss_1"], "sub_33": ["cross_entropy_loss_1"], "convert_element_type_2": ["float_1"], "sub_26": ["cross_entropy_loss"], "sub_27": ["cross_entropy_loss"]}, "cppCodeToPost": {"triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0": ["log_6"], "triton_red_fused_nll_loss_backward_nll_loss_forward_1": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_2": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_3": ["where_22", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_4": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_5": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_6": ["where_22", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7": ["div", "add_156", "add_143", "add_130", "add_117", "add_104", "add_91", "add_78"], "triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0": ["convert_element_type_29", "sub_66", "mul_111", "scatter_upon_const_tensor", "where_15", "div_1", "full_default_2", "mul_112", "exp_7", "sub_63", "sub_62", "convert_element_type_26"], "triton_poi_fused_nll_loss_backward_1": ["scatter_6", "full_default_18"], "triton_poi_fused_nll_loss_backward_2": ["scatter_6", "full_default_18"], "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3": ["sum_28", "mul_123", "where_27", "div_1", "full_default_2"], "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4": ["convert_element_type_47", "sub_72", "mul_116", "where_27", "div_1", "full_default_2", "mul_117", "exp_13", "sub_27", "sub_26", "convert_element_type_2"], "triton_red_fused__to_copy_prepare_softmax_online_0": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_per_fused__to_copy_prepare_softmax_online_1": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_red_fused__to_copy_prepare_softmax_online_2": ["prepare_softmax_online_default_1", "convert_element_type_22"], "triton_per_fused__log_softmax__to_copy_prepare_softmax_online_3": ["log_5"], "triton_red_fused_nll_loss_backward_nll_loss_forward_7": ["where_20", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_8": ["where_18", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_9": ["where_16", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10": ["div", "add_146", "add_133", "add_120", "add_107", "add_94", "add_81", "add_68"], "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4": ["sum_28", "mul_123", "where_27", "div_1", "full_default_2"], "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5": ["convert_element_type_47", "sub_72", "mul_123", "where_27", "div_1", "full_default_2", "mul_124", "exp_13", "sub_27", "sub_26", "convert_element_type_2"]}, "postToCppCode": {"log_6": ["triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0"], "where_26": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_4"], "full_default_1": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_4", "triton_red_fused_nll_loss_backward_nll_loss_forward_5", "triton_red_fused_nll_loss_backward_nll_loss_forward_6", "triton_red_fused_nll_loss_backward_nll_loss_forward_7", "triton_red_fused_nll_loss_backward_nll_loss_forward_8", "triton_red_fused_nll_loss_backward_nll_loss_forward_9"], "where_24": ["triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_5"], "where_22": ["triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "div": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7", "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_156": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_143": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_130": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_117": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_104": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_91": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_78": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "convert_element_type_29": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_66": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "mul_111": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_upon_const_tensor": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "where_15": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "div_1": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "full_default_2": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "mul_112": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "exp_7": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_63": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_62": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "convert_element_type_26": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_6": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "full_default_18": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "sum_28": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4"], "mul_123": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "where_27": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_per_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "convert_element_type_47": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "sub_72": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "mul_116": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "mul_117": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "exp_13": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "sub_27": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "sub_26": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "convert_element_type_2": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"], "prepare_softmax_online_default_1": ["triton_red_fused__to_copy_prepare_softmax_online_0", "triton_per_fused__to_copy_prepare_softmax_online_1", "triton_red_fused__to_copy_prepare_softmax_online_2"], "convert_element_type_22": ["triton_red_fused__to_copy_prepare_softmax_online_0", "triton_per_fused__to_copy_prepare_softmax_online_1", "triton_red_fused__to_copy_prepare_softmax_online_2"], "log_5": ["triton_per_fused__log_softmax__to_copy_prepare_softmax_online_3"], "where_20": ["triton_red_fused_nll_loss_backward_nll_loss_forward_7"], "where_18": ["triton_red_fused_nll_loss_backward_nll_loss_forward_8"], "where_16": ["triton_red_fused_nll_loss_backward_nll_loss_forward_9"], "add_146": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_133": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_120": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_107": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_94": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_81": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "add_68": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_10"], "mul_124": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_5"]}}