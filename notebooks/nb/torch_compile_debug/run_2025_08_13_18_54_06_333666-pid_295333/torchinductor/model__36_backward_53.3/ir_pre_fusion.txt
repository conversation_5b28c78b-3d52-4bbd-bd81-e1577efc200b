op0: SchedulerNode(ComputedBuffer)
op0.writes = [MemoryDep('buf0', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op0.unmet_dependencies = []
op0.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_43', c0, {c0: s2 - 6*(((s2 + 6)//7))}),
        MemoryDep('tangents_1', 0, {}),
        MemoryDep('where_14', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op0.outputs = [
    buf0: ComputedBuffer
    buf0.layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
    buf0.users = [NodeUser(node=SchedulerNode(name='op1'), can_inplace=False, is_weak=False)]
]
op0.group.device = cuda:0
op0.group.iteration = (s2 - 6*(((s2 + 6)//7)), 201088)
op0.sizes = ([s2 - 6*(((s2 + 6)//7))], [201088])
where_14_layout = FixedLayout('cuda:0', torch.int64, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
ne_43_layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf0_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
class op0_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 201088}
    index0 = p0
    index1 = p1
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_14', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int64)
        eq = ops.eq(load, index_expr)
        constant = ops.constant(-1.0, torch.float32)
        constant_1 = ops.constant(0.0, torch.float32)
        where = ops.where(eq, constant, constant_1)
        get_index_2 = self.get_index('index0')
        load_1 = ops.load('ne_43', get_index_2)
        get_index_3 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_3)
        get_index_4 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_4)
        truediv = ops.truediv(load_2, load_3)
        constant_2 = ops.constant(0.0, torch.float32)
        where_1 = ops.where(load_1, truediv, constant_2)
        mul = ops.mul(where, where_1)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_5 = self.get_index('index0')
        store_reduction = ops.store_reduction('buf0', get_index_5, reduction)
        return store_reduction


op1: SchedulerNode(ComputedBuffer)
op1.writes = [MemoryDep('buf1', c0, {c0: 201088*s2 - 1206528*(((s2 + 6)//7))})]
op1.unmet_dependencies = [MemoryDep('buf0', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op1.met_dependencies = 
    [   MemoryDep('amax_6', c0, {c0: s2 - 6*(((s2 + 6)//7))}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_6', c0, {c0: s2 - 6*(((s2 + 6)//7))}),
        MemoryDep('mm_6', c0, {c0: 201088*s2 - 1206528*(((s2 + 6)//7))}),
        MemoryDep('ne_43', c0, {c0: s2 - 6*(((s2 + 6)//7))}),
        MemoryDep('tangents_1', 0, {}),
        MemoryDep('where_14', c0, {c0: s2 - 6*(((s2 + 6)//7))})]
op1.outputs = [
    buf1: ComputedBuffer
    buf1.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
    buf1.users = [NodeUser(node=ExternKernelSchedulerNode(name='op2'), can_inplace=False, is_weak=False)]
]
op1.group.device = cuda:0
op1.group.iteration = (201088*s2 - 1206528*(((s2 + 6)//7)), 1)
op1.sizes = ([s2 - 6*(((s2 + 6)//7)), 201088], [])
where_14_layout = FixedLayout('cuda:0', torch.int64, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
ne_43_layout = FixedLayout('cuda:0', torch.bool, size=[s0 - 6*(((s0 + 6)//7)), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
amax_6_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
log_6_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, 1])
buf0_layout = FixedLayout('cuda:0', torch.float32, size=[s2 - 6*(((s2 + 6)//7)), 1], stride=[1, s2 - 6*(((s2 + 6)//7))])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 201088], stride=[201088, 1])
class op1_loop_body:
    var_ranges = {p0: s2 - 6*(((s2 + 6)//7)), p1: 201088}
    index0 = p0
    index1 = p1
    index2 = 0
    index3 = 201088*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_14', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int64)
        eq = ops.eq(load, index_expr)
        constant = ops.constant(-1.0, torch.float32)
        constant_1 = ops.constant(0.0, torch.float32)
        where = ops.where(eq, constant, constant_1)
        get_index_2 = self.get_index('index0')
        load_1 = ops.load('ne_43', get_index_2)
        get_index_3 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_3)
        get_index_4 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_4)
        truediv = ops.truediv(load_2, load_3)
        constant_2 = ops.constant(0.0, torch.float32)
        where_1 = ops.where(load_1, truediv, constant_2)
        mul = ops.mul(where, where_1)
        get_index_5 = self.get_index('index3')
        load_4 = ops.load('mm_6', get_index_5)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_6 = self.get_index('index0')
        load_5 = ops.load('amax_6', get_index_6)
        sub = ops.sub(to_dtype, load_5)
        get_index_7 = self.get_index('index0')
        load_6 = ops.load('log_6', get_index_7)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_8 = self.get_index('index0')
        load_7 = ops.load('buf0', get_index_8)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_9 = self.get_index('index3')
        store = ops.store('buf1', get_index_9, to_dtype_1, None)
        return store


op2: ExternKernelSchedulerNode(ExternKernelOut)
op2.writes = [StarDep(name='buf2', mode=None)]
op2.unmet_dependencies = [StarDep(name='buf1', mode=None)]
op2.met_dependencies = [StarDep(name='permute_8', mode=None)]
op2.outputs = [
    buf2: ExternKernelOut
    buf2.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[s2 - 6*(((s2 + 6)//7)), 2880], stride=[2880, 1])
    buf2.aliases = ['buf39']
    buf2.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op2.node.kernel = extern_kernels.mm


op3: SchedulerNode(ComputedBuffer)
op3.writes = [MemoryDep('buf3', c0, {c0: 201088*(((s2 + 6)//7))})]
op3.unmet_dependencies = []
op3.met_dependencies = []
op3.outputs = [
    buf3: ComputedBuffer
    buf3.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf3.users = [NodeUser(node=SchedulerNode(name='op4'), can_inplace=False, is_weak=False)]
]
op3.group.device = cuda:0
op3.group.iteration = (201088*(((s2 + 6)//7)), 1)
op3.sizes = ([201088*(((s2 + 6)//7))], [])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op3_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf3', get_index, constant, None)
        return store


op4: SchedulerNode(ComputedBuffer)
op4.writes = [MemoryDep('buf4', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op4.unmet_dependencies = [StarDep(name='buf3', mode=None)]
op4.met_dependencies = [MemoryDep('where_16', c0, {c0: ((s0 + 6)//7)})]
op4.outputs = [
    buf4: ComputedBuffer
    buf4.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf4.mutations = ['buf3']
    buf4.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=True, is_weak=False),
    ]
]
op4.group.device = cuda:0
op4.group.iteration = (((s0 + 6)//7), 1)
op4.sizes = ([((s0 + 6)//7)], [])
where_16_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op4_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_16', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf4', get_index_1, constant, None)
        return store


op5: SchedulerNode(ComputedBuffer)
op5.writes = [MemoryDep('buf5', c0, {c0: 3*(((s2 + 6)//7))})]
op5.unmet_dependencies = [MemoryDep('buf4', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op5.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_45', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op5.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf5.users = [NodeUser(node=SchedulerNode(name='op6'), can_inplace=False, is_weak=False)]
]
op5.group.device = cuda:0
op5.group.iteration = (3*(((s2 + 6)//7)), 67030)
op5.sizes = ([((s2 + 6)//7), 3], [67030])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_45_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op5_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf5', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf3', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_45', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op6: SchedulerNode(ComputedBuffer)
op6.writes = [MemoryDep('buf6', c0, {c0: ((s2 + 6)//7)})]
op6.unmet_dependencies = [MemoryDep('buf5', c0, {c0: 3*(((s2 + 6)//7))})]
op6.met_dependencies = []
op6.outputs = [
    buf6: ComputedBuffer
    buf6.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf6.users = [NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False)]
]
op6.group.device = cuda:0
op6.group.iteration = (((s2 + 6)//7), 3)
op6.sizes = ([((s2 + 6)//7)], [3])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf6_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op6_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf5', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf6', get_index_1, reduction)
        return store_reduction


op7: SchedulerNode(ComputedBuffer)
op7.writes = [MemoryDep('buf7', c0, {c0: 201088*(((s2 + 6)//7))})]
op7.unmet_dependencies = 
    [   MemoryDep('buf4', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf6', c0, {c0: ((s2 + 6)//7)})]
op7.met_dependencies = 
    [   MemoryDep('amax_5', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_5', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm_5', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_45', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op7.outputs = [
    buf7: ComputedBuffer
    buf7.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf7.users = [NodeUser(node=ExternKernelSchedulerNode(name='op8'), can_inplace=False, is_weak=False)]
]
op7.group.device = cuda:0
op7.group.iteration = (201088*(((s2 + 6)//7)), 1)
op7.sizes = ([((s2 + 6)//7), 201088], [])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_45_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_5_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_5_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf6_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf7_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op7_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf3', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_45', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_5', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_5', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_5', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf6', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf7', get_index_8, to_dtype_1, None)
        return store


op8: ExternKernelSchedulerNode(ExternKernelOut)
op8.writes = [StarDep(name='buf8', mode=None)]
op8.unmet_dependencies = [StarDep(name='buf7', mode=None)]
op8.met_dependencies = [StarDep(name='permute_8', mode=None)]
op8.outputs = [
    buf8: ExternKernelOut
    buf8.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf8.aliases = ['buf39']
    buf8.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op8.node.kernel = extern_kernels.mm


op9: SchedulerNode(ComputedBuffer)
op9.writes = [MemoryDep('buf9', c0, {c0: 201088*(((s2 + 6)//7))})]
op9.unmet_dependencies = []
op9.met_dependencies = []
op9.outputs = [
    buf9: ComputedBuffer
    buf9.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf9.users = [NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False)]
]
op9.group.device = cuda:0
op9.group.iteration = (201088*(((s2 + 6)//7)), 1)
op9.sizes = ([201088*(((s2 + 6)//7))], [])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op9_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf9', get_index, constant, None)
        return store


op10: SchedulerNode(ComputedBuffer)
op10.writes = [MemoryDep('buf10', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op10.unmet_dependencies = [StarDep(name='buf9', mode=None)]
op10.met_dependencies = [MemoryDep('where_18', c0, {c0: ((s0 + 6)//7)})]
op10.outputs = [
    buf10: ComputedBuffer
    buf10.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf10.mutations = ['buf9']
    buf10.users = [
        NodeUser(node=SchedulerNode(name='op11'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op13'), can_inplace=True, is_weak=False),
    ]
]
op10.group.device = cuda:0
op10.group.iteration = (((s0 + 6)//7), 1)
op10.sizes = ([((s0 + 6)//7)], [])
where_18_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf10_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op10_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_18', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf10', get_index_1, constant, None)
        return store


op11: SchedulerNode(ComputedBuffer)
op11.writes = [MemoryDep('buf11', c0, {c0: 3*(((s2 + 6)//7))})]
op11.unmet_dependencies = [MemoryDep('buf10', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op11.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_47', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op11.outputs = [
    buf11: ComputedBuffer
    buf11.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf11.users = [NodeUser(node=SchedulerNode(name='op12'), can_inplace=False, is_weak=False)]
]
op11.group.device = cuda:0
op11.group.iteration = (3*(((s2 + 6)//7)), 67030)
op11.sizes = ([((s2 + 6)//7), 3], [67030])
buf10_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf11_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op11_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf11', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf9', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_47', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op12: SchedulerNode(ComputedBuffer)
op12.writes = [MemoryDep('buf12', c0, {c0: ((s2 + 6)//7)})]
op12.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 3*(((s2 + 6)//7))})]
op12.met_dependencies = []
op12.outputs = [
    buf12: ComputedBuffer
    buf12.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf12.users = [NodeUser(node=SchedulerNode(name='op13'), can_inplace=False, is_weak=False)]
]
op12.group.device = cuda:0
op12.group.iteration = (((s2 + 6)//7), 3)
op12.sizes = ([((s2 + 6)//7)], [3])
buf11_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op12_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf11', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf12', get_index_1, reduction)
        return store_reduction


op13: SchedulerNode(ComputedBuffer)
op13.writes = [MemoryDep('buf13', c0, {c0: 201088*(((s2 + 6)//7))})]
op13.unmet_dependencies = 
    [   MemoryDep('buf10', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf12', c0, {c0: ((s2 + 6)//7)})]
op13.met_dependencies = 
    [   MemoryDep('amax_4', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_4', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm_4', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_47', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op13.outputs = [
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf13.users = [NodeUser(node=ExternKernelSchedulerNode(name='op14'), can_inplace=False, is_weak=False)]
]
op13.group.device = cuda:0
op13.group.iteration = (201088*(((s2 + 6)//7)), 1)
op13.sizes = ([((s2 + 6)//7), 201088], [])
buf10_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_4_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_4_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_4_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf13_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op13_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf9', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_47', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_4', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_4', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_4', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf12', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf13', get_index_8, to_dtype_1, None)
        return store


op14: ExternKernelSchedulerNode(ExternKernelOut)
op14.writes = [StarDep(name='buf14', mode=None)]
op14.unmet_dependencies = [StarDep(name='buf13', mode=None)]
op14.met_dependencies = [StarDep(name='permute_8', mode=None)]
op14.outputs = [
    buf14: ExternKernelOut
    buf14.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf14.aliases = ['buf39']
    buf14.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op14.node.kernel = extern_kernels.mm


op15: SchedulerNode(ComputedBuffer)
op15.writes = [MemoryDep('buf15', c0, {c0: 201088*(((s2 + 6)//7))})]
op15.unmet_dependencies = []
op15.met_dependencies = []
op15.outputs = [
    buf15: ComputedBuffer
    buf15.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf15.users = [NodeUser(node=SchedulerNode(name='op16'), can_inplace=False, is_weak=False)]
]
op15.group.device = cuda:0
op15.group.iteration = (201088*(((s2 + 6)//7)), 1)
op15.sizes = ([201088*(((s2 + 6)//7))], [])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op15_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf15', get_index, constant, None)
        return store


op16: SchedulerNode(ComputedBuffer)
op16.writes = [MemoryDep('buf16', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op16.unmet_dependencies = [StarDep(name='buf15', mode=None)]
op16.met_dependencies = [MemoryDep('where_20', c0, {c0: ((s0 + 6)//7)})]
op16.outputs = [
    buf16: ComputedBuffer
    buf16.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf16.mutations = ['buf15']
    buf16.users = [
        NodeUser(node=SchedulerNode(name='op17'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op19'), can_inplace=True, is_weak=False),
    ]
]
op16.group.device = cuda:0
op16.group.iteration = (((s0 + 6)//7), 1)
op16.sizes = ([((s0 + 6)//7)], [])
where_20_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf16_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op16_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_20', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf16', get_index_1, constant, None)
        return store


op17: SchedulerNode(ComputedBuffer)
op17.writes = [MemoryDep('buf17', c0, {c0: 3*(((s2 + 6)//7))})]
op17.unmet_dependencies = [MemoryDep('buf16', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op17.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_49', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op17.outputs = [
    buf17: ComputedBuffer
    buf17.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf17.users = [NodeUser(node=SchedulerNode(name='op18'), can_inplace=False, is_weak=False)]
]
op17.group.device = cuda:0
op17.group.iteration = (3*(((s2 + 6)//7)), 67030)
op17.sizes = ([((s2 + 6)//7), 3], [67030])
buf16_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op17_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf17', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf15', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_49', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op18: SchedulerNode(ComputedBuffer)
op18.writes = [MemoryDep('buf18', c0, {c0: ((s2 + 6)//7)})]
op18.unmet_dependencies = [MemoryDep('buf17', c0, {c0: 3*(((s2 + 6)//7))})]
op18.met_dependencies = []
op18.outputs = [
    buf18: ComputedBuffer
    buf18.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf18.users = [NodeUser(node=SchedulerNode(name='op19'), can_inplace=False, is_weak=False)]
]
op18.group.device = cuda:0
op18.group.iteration = (((s2 + 6)//7), 3)
op18.sizes = ([((s2 + 6)//7)], [3])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op18_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf17', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf18', get_index_1, reduction)
        return store_reduction


op19: SchedulerNode(ComputedBuffer)
op19.writes = [MemoryDep('buf19', c0, {c0: 201088*(((s2 + 6)//7))})]
op19.unmet_dependencies = 
    [   MemoryDep('buf16', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf18', c0, {c0: ((s2 + 6)//7)})]
op19.met_dependencies = 
    [   MemoryDep('amax_3', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_3', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm_3', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_49', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op19.outputs = [
    buf19: ComputedBuffer
    buf19.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf19.users = [NodeUser(node=ExternKernelSchedulerNode(name='op20'), can_inplace=False, is_weak=False)]
]
op19.group.device = cuda:0
op19.group.iteration = (201088*(((s2 + 6)//7)), 1)
op19.sizes = ([((s2 + 6)//7), 201088], [])
buf16_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_3_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_3_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf19_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op19_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf15', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_49', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_3', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_3', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_3', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf18', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf19', get_index_8, to_dtype_1, None)
        return store


op20: ExternKernelSchedulerNode(ExternKernelOut)
op20.writes = [StarDep(name='buf20', mode=None)]
op20.unmet_dependencies = [StarDep(name='buf19', mode=None)]
op20.met_dependencies = [StarDep(name='permute_8', mode=None)]
op20.outputs = [
    buf20: ExternKernelOut
    buf20.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf20.aliases = ['buf39']
    buf20.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op20.node.kernel = extern_kernels.mm


op21: SchedulerNode(ComputedBuffer)
op21.writes = [MemoryDep('buf21', c0, {c0: 201088*(((s2 + 6)//7))})]
op21.unmet_dependencies = []
op21.met_dependencies = []
op21.outputs = [
    buf21: ComputedBuffer
    buf21.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf21.users = [NodeUser(node=SchedulerNode(name='op22'), can_inplace=False, is_weak=False)]
]
op21.group.device = cuda:0
op21.group.iteration = (201088*(((s2 + 6)//7)), 1)
op21.sizes = ([201088*(((s2 + 6)//7))], [])
buf21_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op21_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf21', get_index, constant, None)
        return store


op22: SchedulerNode(ComputedBuffer)
op22.writes = [MemoryDep('buf22', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op22.unmet_dependencies = [StarDep(name='buf21', mode=None)]
op22.met_dependencies = [MemoryDep('where_22', c0, {c0: ((s0 + 6)//7)})]
op22.outputs = [
    buf22: ComputedBuffer
    buf22.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf22.mutations = ['buf21']
    buf22.users = [
        NodeUser(node=SchedulerNode(name='op23'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=True, is_weak=False),
    ]
]
op22.group.device = cuda:0
op22.group.iteration = (((s0 + 6)//7), 1)
op22.sizes = ([((s0 + 6)//7)], [])
where_22_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf21_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf22_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op22_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_22', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf22', get_index_1, constant, None)
        return store


op23: SchedulerNode(ComputedBuffer)
op23.writes = [MemoryDep('buf23', c0, {c0: 3*(((s2 + 6)//7))})]
op23.unmet_dependencies = [MemoryDep('buf22', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op23.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_51', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op23.outputs = [
    buf23: ComputedBuffer
    buf23.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf23.users = [NodeUser(node=SchedulerNode(name='op24'), can_inplace=False, is_weak=False)]
]
op23.group.device = cuda:0
op23.group.iteration = (3*(((s2 + 6)//7)), 67030)
op23.sizes = ([((s2 + 6)//7), 3], [67030])
buf22_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_51_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op23_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf23', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf21', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_51', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op24: SchedulerNode(ComputedBuffer)
op24.writes = [MemoryDep('buf24', c0, {c0: ((s2 + 6)//7)})]
op24.unmet_dependencies = [MemoryDep('buf23', c0, {c0: 3*(((s2 + 6)//7))})]
op24.met_dependencies = []
op24.outputs = [
    buf24: ComputedBuffer
    buf24.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf24.users = [NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False)]
]
op24.group.device = cuda:0
op24.group.iteration = (((s2 + 6)//7), 3)
op24.sizes = ([((s2 + 6)//7)], [3])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op24_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf23', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf24', get_index_1, reduction)
        return store_reduction


op25: SchedulerNode(ComputedBuffer)
op25.writes = [MemoryDep('buf25', c0, {c0: 201088*(((s2 + 6)//7))})]
op25.unmet_dependencies = 
    [   MemoryDep('buf22', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf24', c0, {c0: ((s2 + 6)//7)})]
op25.met_dependencies = 
    [   MemoryDep('amax_2', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_2', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm_2', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_51', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op25.outputs = [
    buf25: ComputedBuffer
    buf25.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf25.users = [NodeUser(node=ExternKernelSchedulerNode(name='op26'), can_inplace=False, is_weak=False)]
]
op25.group.device = cuda:0
op25.group.iteration = (201088*(((s2 + 6)//7)), 1)
op25.sizes = ([((s2 + 6)//7), 201088], [])
buf22_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_51_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_2_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_2_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_2_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf25_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op25_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf21', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_51', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_2', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_2', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_2', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf24', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf25', get_index_8, to_dtype_1, None)
        return store


op26: ExternKernelSchedulerNode(ExternKernelOut)
op26.writes = [StarDep(name='buf26', mode=None)]
op26.unmet_dependencies = [StarDep(name='buf25', mode=None)]
op26.met_dependencies = [StarDep(name='permute_8', mode=None)]
op26.outputs = [
    buf26: ExternKernelOut
    buf26.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf26.aliases = ['buf39']
    buf26.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op26.node.kernel = extern_kernels.mm


op27: SchedulerNode(ComputedBuffer)
op27.writes = [MemoryDep('buf27', c0, {c0: 201088*(((s2 + 6)//7))})]
op27.unmet_dependencies = []
op27.met_dependencies = []
op27.outputs = [
    buf27: ComputedBuffer
    buf27.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf27.users = [NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False)]
]
op27.group.device = cuda:0
op27.group.iteration = (201088*(((s2 + 6)//7)), 1)
op27.sizes = ([201088*(((s2 + 6)//7))], [])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op27_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf27', get_index, constant, None)
        return store


op28: SchedulerNode(ComputedBuffer)
op28.writes = [MemoryDep('buf28', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op28.unmet_dependencies = [StarDep(name='buf27', mode=None)]
op28.met_dependencies = [MemoryDep('where_24', c0, {c0: ((s0 + 6)//7)})]
op28.outputs = [
    buf28: ComputedBuffer
    buf28.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf28.mutations = ['buf27']
    buf28.users = [
        NodeUser(node=SchedulerNode(name='op29'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op31'), can_inplace=True, is_weak=False),
    ]
]
op28.group.device = cuda:0
op28.group.iteration = (((s0 + 6)//7), 1)
op28.sizes = ([((s0 + 6)//7)], [])
where_24_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf28_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op28_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_24', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf28', get_index_1, constant, None)
        return store


op29: SchedulerNode(ComputedBuffer)
op29.writes = [MemoryDep('buf29', c0, {c0: 3*(((s2 + 6)//7))})]
op29.unmet_dependencies = [MemoryDep('buf28', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op29.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_53', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op29.outputs = [
    buf29: ComputedBuffer
    buf29.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf29.users = [NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False)]
]
op29.group.device = cuda:0
op29.group.iteration = (3*(((s2 + 6)//7)), 67030)
op29.sizes = ([((s2 + 6)//7), 3], [67030])
buf28_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf29_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op29_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf29', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf27', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_53', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op30: SchedulerNode(ComputedBuffer)
op30.writes = [MemoryDep('buf30', c0, {c0: ((s2 + 6)//7)})]
op30.unmet_dependencies = [MemoryDep('buf29', c0, {c0: 3*(((s2 + 6)//7))})]
op30.met_dependencies = []
op30.outputs = [
    buf30: ComputedBuffer
    buf30.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf30.users = [NodeUser(node=SchedulerNode(name='op31'), can_inplace=False, is_weak=False)]
]
op30.group.device = cuda:0
op30.group.iteration = (((s2 + 6)//7), 3)
op30.sizes = ([((s2 + 6)//7)], [3])
buf29_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op30_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf29', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf30', get_index_1, reduction)
        return store_reduction


op31: SchedulerNode(ComputedBuffer)
op31.writes = [MemoryDep('buf31', c0, {c0: 201088*(((s2 + 6)//7))})]
op31.unmet_dependencies = 
    [   MemoryDep('buf28', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf30', c0, {c0: ((s2 + 6)//7)})]
op31.met_dependencies = 
    [   MemoryDep('amax_1', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_1', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm_1', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_53', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op31.outputs = [
    buf31: ComputedBuffer
    buf31.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf31.users = [NodeUser(node=ExternKernelSchedulerNode(name='op32'), can_inplace=False, is_weak=False)]
]
op31.group.device = cuda:0
op31.group.iteration = (201088*(((s2 + 6)//7)), 1)
op31.sizes = ([((s2 + 6)//7), 201088], [])
buf28_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_1_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_1_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf31_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op31_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf27', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_53', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_1', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_1', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_1', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf30', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf31', get_index_8, to_dtype_1, None)
        return store


op32: ExternKernelSchedulerNode(ExternKernelOut)
op32.writes = [StarDep(name='buf32', mode=None)]
op32.unmet_dependencies = [StarDep(name='buf31', mode=None)]
op32.met_dependencies = [StarDep(name='permute_8', mode=None)]
op32.outputs = [
    buf32: ExternKernelOut
    buf32.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf32.aliases = ['buf39']
    buf32.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op32.node.kernel = extern_kernels.mm


op33: SchedulerNode(ComputedBuffer)
op33.writes = [MemoryDep('buf33', c0, {c0: 201088*(((s2 + 6)//7))})]
op33.unmet_dependencies = []
op33.met_dependencies = []
op33.outputs = [
    buf33: ComputedBuffer
    buf33.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf33.users = [NodeUser(node=SchedulerNode(name='op34'), can_inplace=False, is_weak=False)]
]
op33.group.device = cuda:0
op33.group.iteration = (201088*(((s2 + 6)//7)), 1)
op33.sizes = ([201088*(((s2 + 6)//7))], [])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op33_loop_body:
    var_ranges = {p0: 201088*(((s2 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf33', get_index, constant, None)
        return store


op34: SchedulerNode(ComputedBuffer)
op34.writes = [MemoryDep('buf34', 201088*c0 + tmp0, {c0: ((s0 + 6)//7)})]
op34.unmet_dependencies = [StarDep(name='buf33', mode=None)]
op34.met_dependencies = [MemoryDep('where_26', c0, {c0: ((s0 + 6)//7)})]
op34.outputs = [
    buf34: ComputedBuffer
    buf34.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf34.mutations = ['buf33']
    buf34.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op37'), can_inplace=True, is_weak=False),
    ]
]
op34.group.device = cuda:0
op34.group.iteration = (((s0 + 6)//7), 1)
op34.sizes = ([((s0 + 6)//7)], [])
where_26_layout = FixedLayout('cuda:0', torch.int64, size=[((s0 + 6)//7), 1], stride=[1, 1])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
buf34_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op34_loop_body:
    var_ranges = {p0: ((s0 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_26', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf34', get_index_1, constant, None)
        return store


op35: SchedulerNode(ComputedBuffer)
op35.writes = [MemoryDep('buf35', c0, {c0: 3*(((s2 + 6)//7))})]
op35.unmet_dependencies = [MemoryDep('buf34', 201088*c0 + c1, {c0: ((s2 + 6)//7), c1: 201090})]
op35.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_55', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op35.outputs = [
    buf35: ComputedBuffer
    buf35.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
    buf35.users = [NodeUser(node=SchedulerNode(name='op36'), can_inplace=False, is_weak=False)]
]
op35.group.device = cuda:0
op35.group.iteration = (3*(((s2 + 6)//7)), 67030)
op35.sizes = ([((s2 + 6)//7), 3], [67030])
buf34_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
class op35_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3, p2: 67030}
    index0 = 67030*p1 + p2
    index1 = 201088*p0 + 67030*p1 + p2
    index2 = p0
    index3 = 0
    index4 = 3*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        constant = ops.constant(201088, torch.int32)
        lt = ops.lt(index_expr, constant)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', masked_subblock1)
        get_index_1 = self.get_index('index4')
        store_reduction = ops.store_reduction('buf35', get_index_1, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index1')
        load = ops.load('buf33', get_index)
        get_index_1 = self.get_index('index2')
        load_1 = ops.load('ne_55', get_index_1)
        get_index_2 = self.get_index('index3')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index3')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        return mul


op36: SchedulerNode(ComputedBuffer)
op36.writes = [MemoryDep('buf36', c0, {c0: ((s2 + 6)//7)})]
op36.unmet_dependencies = [MemoryDep('buf35', c0, {c0: 3*(((s2 + 6)//7))})]
op36.met_dependencies = []
op36.outputs = [
    buf36: ComputedBuffer
    buf36.layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
    buf36.users = [NodeUser(node=SchedulerNode(name='op37'), can_inplace=False, is_weak=False)]
]
op36.group.device = cuda:0
op36.group.iteration = (((s2 + 6)//7), 3)
op36.sizes = ([((s2 + 6)//7)], [3])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1, 3], stride=[3, 3*(((s2 + 6)//7)), 1])
buf36_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
class op36_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 3}
    index0 = 3*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf35', get_index)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', load)
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf36', get_index_1, reduction)
        return store_reduction


op37: SchedulerNode(ComputedBuffer)
op37.writes = [MemoryDep('buf37', c0, {c0: 201088*(((s2 + 6)//7))})]
op37.unmet_dependencies = 
    [   MemoryDep('buf34', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('buf36', c0, {c0: ((s2 + 6)//7)})]
op37.met_dependencies = 
    [   MemoryDep('amax', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('mm', c0, {c0: 201088*(((s2 + 6)//7))}),
        MemoryDep('ne_55', c0, {c0: ((s2 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op37.outputs = [
    buf37: ComputedBuffer
    buf37.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
    buf37.users = [NodeUser(node=ExternKernelSchedulerNode(name='op38'), can_inplace=False, is_weak=False)]
]
op37.group.device = cuda:0
op37.group.iteration = (201088*(((s2 + 6)//7)), 1)
op37.sizes = ([((s2 + 6)//7), 201088], [])
buf34_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
ne_55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
amax_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
log_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, 1])
buf36_layout = FixedLayout('cuda:0', torch.float32, size=[((s2 + 6)//7), 1], stride=[1, ((s2 + 6)//7)])
buf37_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 201088], stride=[201088, 1])
class op37_loop_body:
    var_ranges = {p0: ((s2 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf33', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_55', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf36', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf37', get_index_8, to_dtype_1, None)
        return store


op38: ExternKernelSchedulerNode(ExternKernelOut)
op38.writes = [StarDep(name='buf38', mode=None)]
op38.unmet_dependencies = [StarDep(name='buf37', mode=None)]
op38.met_dependencies = [StarDep(name='permute_8', mode=None)]
op38.outputs = [
    buf38: ExternKernelOut
    buf38.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s2 + 6)//7), 2880], stride=[2880, 1])
    buf38.aliases = ['buf39']
    buf38.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op38.node.kernel = extern_kernels.mm


op39: NopKernelSchedulerNode(ConcatKernel)
op39.writes = [StarDep(name='buf39', mode=None)]
op39.unmet_dependencies = 
    [   StarDep(name='buf14', mode=None),
        StarDep(name='buf2', mode=None),
        StarDep(name='buf20', mode=None),
        StarDep(name='buf26', mode=None),
        StarDep(name='buf32', mode=None),
        StarDep(name='buf38', mode=None),
        StarDep(name='buf8', mode=None)]
op39.met_dependencies = []
op39.outputs = [
    buf39: ConcatKernel
    buf39.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s2, 2880], stride=[2880, 1])
    buf39.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]


