{"preToPost": {"shift_labels": ["empty", "permute"], "getitem": ["slice_1"], "setitem": ["slice_1", "slice_scatter_default"], "setitem_1": ["full_default", "select_1", "copy_1", "select_scatter_default"], "shift_labels_1": ["sym_numel_default"], "hidden_states": ["view_1", "sym_size_int_5"], "chunk": ["add_29", "sub_17", "floordiv", "split", "getitem", "getitem_1", "getitem_2", "getitem_3", "getitem_4", "getitem_5", "getitem_6", "sym_size_int_6"], "chunk_1": ["add_51", "sub_25", "floordiv_1"], "_shift_logits": ["permute_1", "mm"], "float_1": ["convert_element_type_2"], "cross_entropy_loss": ["getitem_75", "getitem_76", "sub_tensor_6", "log", "sub_36", "view_2", "split_2", "getitem_14", "ne_6", "full_default_1", "where", "unsqueeze", "gather", "squeeze", "neg", "full_default_2", "where_1", "sum_3"], "loss": ["add_78"], "_shift_logits_1": ["mm_1"], "float_2": ["convert_element_type_6"], "cross_entropy_loss_1": ["getitem_73", "getitem_74", "sub_tensor_5", "log_1", "sub_42", "getitem_22", "ne_12", "where_2", "unsqueeze_1", "gather_1", "squeeze_1", "neg_1", "where_3", "sum_6"], "loss_1": ["add_91"], "_shift_logits_2": ["mm_2"], "float_3": ["convert_element_type_10"], "cross_entropy_loss_2": ["getitem_71", "getitem_72", "sub_tensor_4", "log_2", "sub_48", "getitem_30", "ne_18", "where_4", "unsqueeze_2", "gather_2", "squeeze_2", "neg_2", "where_5", "sum_9"], "loss_2": ["add_104"], "_shift_logits_3": ["mm_3"], "float_4": ["convert_element_type_14"], "cross_entropy_loss_3": ["getitem_69", "getitem_70", "sub_tensor_3", "log_3", "sub_54", "getitem_38", "ne_24", "where_6", "unsqueeze_3", "gather_3", "squeeze_3", "neg_3", "where_7", "sum_12"], "loss_3": ["add_117"], "_shift_logits_4": ["mm_4"], "float_5": ["convert_element_type_18"], "cross_entropy_loss_4": ["getitem_67", "getitem_68", "sub_tensor_2", "log_4", "sub_60", "getitem_46", "ne_30", "where_8", "unsqueeze_4", "gather_4", "squeeze_4", "neg_4", "where_9", "sum_15"], "loss_4": ["add_130"], "_shift_logits_5": ["mm_5"], "float_6": ["convert_element_type_22"], "cross_entropy_loss_5": ["getitem_65", "getitem_66", "sub_tensor_1", "log_5", "sub_66", "getitem_54", "ne_36", "where_10", "unsqueeze_5", "gather_5", "squeeze_5", "neg_5", "where_11", "sum_18"], "loss_5": ["add_143"], "_shift_logits_6": ["mm_6"], "float_7": ["convert_element_type_26"], "cross_entropy_loss_6": ["getitem_63", "getitem_64", "sub_tensor", "log_6", "sub_72", "getitem_62", "ne_42", "where_12", "unsqueeze_6", "gather_6", "squeeze_6", "neg_6", "where_13", "sum_21"], "loss_6": ["add_156"], "tensor": ["convert_element_type_28"], "loss_7": ["div"]}, "postToPre": {"empty": ["shift_labels"], "permute": ["shift_labels"], "slice_1": ["getitem", "setitem"], "slice_scatter_default": ["setitem"], "full_default": ["setitem_1"], "select_1": ["setitem_1"], "copy_1": ["setitem_1"], "select_scatter_default": ["setitem_1"], "sym_numel_default": ["shift_labels_1"], "view_1": ["hidden_states"], "sym_size_int_5": ["hidden_states"], "add_29": ["chunk"], "sub_17": ["chunk"], "floordiv": ["chunk"], "split": ["chunk"], "getitem": ["chunk"], "getitem_1": ["chunk"], "getitem_2": ["chunk"], "getitem_3": ["chunk"], "getitem_4": ["chunk"], "getitem_5": ["chunk"], "getitem_6": ["chunk"], "sym_size_int_6": ["chunk"], "add_51": ["chunk_1"], "sub_25": ["chunk_1"], "floordiv_1": ["chunk_1"], "permute_1": ["_shift_logits"], "mm": ["_shift_logits"], "convert_element_type_2": ["float_1"], "getitem_75": ["cross_entropy_loss"], "getitem_76": ["cross_entropy_loss"], "sub_tensor_6": ["cross_entropy_loss"], "log": ["cross_entropy_loss"], "sub_36": ["cross_entropy_loss"], "view_2": ["cross_entropy_loss"], "split_2": ["cross_entropy_loss"], "getitem_14": ["cross_entropy_loss"], "ne_6": ["cross_entropy_loss"], "full_default_1": ["cross_entropy_loss"], "where": ["cross_entropy_loss"], "unsqueeze": ["cross_entropy_loss"], "gather": ["cross_entropy_loss"], "squeeze": ["cross_entropy_loss"], "neg": ["cross_entropy_loss"], "full_default_2": ["cross_entropy_loss"], "where_1": ["cross_entropy_loss"], "sum_3": ["cross_entropy_loss"], "add_78": ["loss"], "mm_1": ["_shift_logits_1"], "convert_element_type_6": ["float_2"], "getitem_73": ["cross_entropy_loss_1"], "getitem_74": ["cross_entropy_loss_1"], "sub_tensor_5": ["cross_entropy_loss_1"], "log_1": ["cross_entropy_loss_1"], "sub_42": ["cross_entropy_loss_1"], "getitem_22": ["cross_entropy_loss_1"], "ne_12": ["cross_entropy_loss_1"], "where_2": ["cross_entropy_loss_1"], "unsqueeze_1": ["cross_entropy_loss_1"], "gather_1": ["cross_entropy_loss_1"], "squeeze_1": ["cross_entropy_loss_1"], "neg_1": ["cross_entropy_loss_1"], "where_3": ["cross_entropy_loss_1"], "sum_6": ["cross_entropy_loss_1"], "add_91": ["loss_1"], "mm_2": ["_shift_logits_2"], "convert_element_type_10": ["float_3"], "getitem_71": ["cross_entropy_loss_2"], "getitem_72": ["cross_entropy_loss_2"], "sub_tensor_4": ["cross_entropy_loss_2"], "log_2": ["cross_entropy_loss_2"], "sub_48": ["cross_entropy_loss_2"], "getitem_30": ["cross_entropy_loss_2"], "ne_18": ["cross_entropy_loss_2"], "where_4": ["cross_entropy_loss_2"], "unsqueeze_2": ["cross_entropy_loss_2"], "gather_2": ["cross_entropy_loss_2"], "squeeze_2": ["cross_entropy_loss_2"], "neg_2": ["cross_entropy_loss_2"], "where_5": ["cross_entropy_loss_2"], "sum_9": ["cross_entropy_loss_2"], "add_104": ["loss_2"], "mm_3": ["_shift_logits_3"], "convert_element_type_14": ["float_4"], "getitem_69": ["cross_entropy_loss_3"], "getitem_70": ["cross_entropy_loss_3"], "sub_tensor_3": ["cross_entropy_loss_3"], "log_3": ["cross_entropy_loss_3"], "sub_54": ["cross_entropy_loss_3"], "getitem_38": ["cross_entropy_loss_3"], "ne_24": ["cross_entropy_loss_3"], "where_6": ["cross_entropy_loss_3"], "unsqueeze_3": ["cross_entropy_loss_3"], "gather_3": ["cross_entropy_loss_3"], "squeeze_3": ["cross_entropy_loss_3"], "neg_3": ["cross_entropy_loss_3"], "where_7": ["cross_entropy_loss_3"], "sum_12": ["cross_entropy_loss_3"], "add_117": ["loss_3"], "mm_4": ["_shift_logits_4"], "convert_element_type_18": ["float_5"], "getitem_67": ["cross_entropy_loss_4"], "getitem_68": ["cross_entropy_loss_4"], "sub_tensor_2": ["cross_entropy_loss_4"], "log_4": ["cross_entropy_loss_4"], "sub_60": ["cross_entropy_loss_4"], "getitem_46": ["cross_entropy_loss_4"], "ne_30": ["cross_entropy_loss_4"], "where_8": ["cross_entropy_loss_4"], "unsqueeze_4": ["cross_entropy_loss_4"], "gather_4": ["cross_entropy_loss_4"], "squeeze_4": ["cross_entropy_loss_4"], "neg_4": ["cross_entropy_loss_4"], "where_9": ["cross_entropy_loss_4"], "sum_15": ["cross_entropy_loss_4"], "add_130": ["loss_4"], "mm_5": ["_shift_logits_5"], "convert_element_type_22": ["float_6"], "getitem_65": ["cross_entropy_loss_5"], "getitem_66": ["cross_entropy_loss_5"], "sub_tensor_1": ["cross_entropy_loss_5"], "log_5": ["cross_entropy_loss_5"], "sub_66": ["cross_entropy_loss_5"], "getitem_54": ["cross_entropy_loss_5"], "ne_36": ["cross_entropy_loss_5"], "where_10": ["cross_entropy_loss_5"], "unsqueeze_5": ["cross_entropy_loss_5"], "gather_5": ["cross_entropy_loss_5"], "squeeze_5": ["cross_entropy_loss_5"], "neg_5": ["cross_entropy_loss_5"], "where_11": ["cross_entropy_loss_5"], "sum_18": ["cross_entropy_loss_5"], "add_143": ["loss_5"], "mm_6": ["_shift_logits_6"], "convert_element_type_26": ["float_7"], "getitem_63": ["cross_entropy_loss_6"], "getitem_64": ["cross_entropy_loss_6"], "sub_tensor": ["cross_entropy_loss_6"], "log_6": ["cross_entropy_loss_6"], "sub_72": ["cross_entropy_loss_6"], "getitem_62": ["cross_entropy_loss_6"], "ne_42": ["cross_entropy_loss_6"], "where_12": ["cross_entropy_loss_6"], "unsqueeze_6": ["cross_entropy_loss_6"], "gather_6": ["cross_entropy_loss_6"], "squeeze_6": ["cross_entropy_loss_6"], "neg_6": ["cross_entropy_loss_6"], "where_13": ["cross_entropy_loss_6"], "sum_21": ["cross_entropy_loss_6"], "add_156": ["loss_6"], "convert_element_type_28": ["tensor"], "div": ["loss_7"]}, "cppCodeToPost": {"triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0": ["log_6"], "triton_red_fused_nll_loss_backward_nll_loss_forward_1": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_2": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_3": ["where_22", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_4": ["where_20", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_5": ["where_18", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_6": ["where_16", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7": ["div", "add_156", "add_143", "add_130", "add_117", "add_104", "add_91", "add_78"]}, "postToCppCode": {"log_6": ["triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0"], "where_26": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1"], "full_default_1": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_4", "triton_red_fused_nll_loss_backward_nll_loss_forward_5", "triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "where_24": ["triton_red_fused_nll_loss_backward_nll_loss_forward_2"], "where_22": ["triton_red_fused_nll_loss_backward_nll_loss_forward_3"], "where_20": ["triton_red_fused_nll_loss_backward_nll_loss_forward_4"], "where_18": ["triton_red_fused_nll_loss_backward_nll_loss_forward_5"], "where_16": ["triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "div": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_156": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_143": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_130": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_117": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_104": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_91": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_78": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"]}}