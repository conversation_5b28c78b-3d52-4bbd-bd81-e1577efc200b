op0: NopKernelSchedulerNode(ComputedBuffer)
op0.writes = [MemoryDep('buf0', d0*s1 + d1, {d0: 0, d1: 0})]
op0.unmet_dependencies = []
op0.met_dependencies = []
op0.outputs = [
    buf0: ComputedBuffer
    buf0.layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
    buf0.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op37'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op38'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op39'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op40'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op41'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op43'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op44'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op45'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op46'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op47'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op48'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op49'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op50'), can_inplace=False, is_weak=False),
    ]
]


op1: ExternKernelSchedulerNode(ExternKernelOut)
op1.writes = [StarDep(name='buf1', mode=None)]
op1.unmet_dependencies = []
op1.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op1.outputs = [
    buf1: ExternKernelOut
    buf1.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf1.users = [
        NodeUser(node=SchedulerNode(name='op2'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op3'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op1.node.kernel = extern_kernels.mm


op2_op3_op4: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op2_op3_op4.writes = 
    [   MemoryDep('buf2', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf3', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf4', c0, {c0: ((s4*s5 + 6)//7)})]
op2_op3_op4.unmet_dependencies = [MemoryDep('buf1', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op2_op3_op4.met_dependencies = []
op2_op3_op4.outputs = [
    buf2: ComputedBuffer
    buf2.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf2.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf3: ComputedBuffer
    buf3.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf3.users = [NodeUser(node=SchedulerNode(name='op4'), can_inplace=True, is_weak=False)]
    buf4: ComputedBuffer
    buf4.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf4.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op2_op3_op4.snodes[0] =
op2: SchedulerNode(ComputedBuffer)
op2.writes = [MemoryDep('buf2', c0, {c0: ((s4*s5 + 6)//7)})]
op2.unmet_dependencies = [MemoryDep('buf1', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op2.met_dependencies = []
op2.outputs = [
    buf2: ComputedBuffer
    buf2.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf2.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op2.group.device = cuda:0
op2.group.iteration = (((s4*s5 + 6)//7), 201088)
op2.sizes = ([((s4*s5 + 6)//7)], [201088])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf2_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op2_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf1', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf2', get_index_1, getitem)
        return store_reduction
op2_op3_op4.snodes[1] =
op3: SchedulerNode(ComputedBuffer)
op3.writes = [MemoryDep('buf3', c0, {c0: ((s4*s5 + 6)//7)})]
op3.unmet_dependencies = [MemoryDep('buf1', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op3.met_dependencies = []
op3.outputs = [
    buf3: ComputedBuffer
    buf3.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf3.users = [NodeUser(node=SchedulerNode(name='op4'), can_inplace=True, is_weak=False)]
]
op3.group.device = cuda:0
op3.group.iteration = (((s4*s5 + 6)//7), 201088)
op3.sizes = ([((s4*s5 + 6)//7)], [201088])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op3_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf1', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf3', get_index_1, getitem_1)
        return store_reduction
op2_op3_op4.snodes[2] =
op4: SchedulerNode(ComputedBuffer)
op4.writes = [MemoryDep('buf4', c0, {c0: ((s4*s5 + 6)//7)})]
op4.unmet_dependencies = [MemoryDep('buf3', c0, {c0: ((s4*s5 + 6)//7)})]
op4.met_dependencies = []
op4.outputs = [
    buf4: ComputedBuffer
    buf4.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf4.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op4.group.device = cuda:0
op4.group.iteration = (((s4*s5 + 6)//7), 1)
op4.sizes = ([((s4*s5 + 6)//7)], [])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf4_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op4_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf3', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf4', get_index_1, log, None)
        return store


op5_op49_op50: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op5_op49_op50.writes = 
    [   MemoryDep('buf49', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf5', 0, {}),
        MemoryDep('buf50', c0, {c0: ((s0*s1 + 6)//7)})]
op5_op49_op50.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0, 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf1', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf2', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf4', c0, {c0: ((s0*s1 + 6)//7)})]
op5_op49_op50.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0, 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op5_op49_op50.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf5.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf49: ComputedBuffer
    buf49.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf49.users = [
        NodeUser(node=SchedulerNode(name='op50'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf50: ComputedBuffer
    buf50.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf50.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op5_op49_op50.snodes[0] =
op5: SchedulerNode(ComputedBuffer)
op5.writes = [MemoryDep('buf5', 0, {})]
op5.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0, 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf1', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf2', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf4', c0, {c0: ((s0*s1 + 6)//7)})]
op5.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0, 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op5.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf5.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op5.group.device = cuda:0
op5.group.iteration = (1, ((s0*s1 + 6)//7))
op5.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf2_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf4_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op5_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0, 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0, 1, s0*s1)) + 1
    index3 = ModularIndexing(p0, 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf1', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf2', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf4', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf5', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op5_op49_op50.snodes[1] =
op49: SchedulerNode(ComputedBuffer)
op49.writes = [MemoryDep('buf49', c0, {c0: ((s0*s1 + 6)//7)})]
op49.unmet_dependencies = [MemoryDep('buf0', ModularIndexing(c0, 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op49.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0, 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op49.outputs = [
    buf49: ComputedBuffer
    buf49.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf49.users = [
        NodeUser(node=SchedulerNode(name='op50'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op49.group.device = cuda:0
op49.group.iteration = (((s0*s1 + 6)//7), 1)
op49.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op49_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0, 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0, 1, s0*s1)) + 1
    index3 = ModularIndexing(p0, 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf49', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op5_op49_op50.snodes[2] =
op50: SchedulerNode(ComputedBuffer)
op50.writes = [MemoryDep('buf50', c0, {c0: ((s0*s1 + 6)//7)})]
op50.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0, 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf49', c0, {c0: ((s0*s1 + 6)//7)})]
op50.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0, 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op50.outputs = [
    buf50: ComputedBuffer
    buf50.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf50.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op50.group.device = cuda:0
op50.group.iteration = (((s0*s1 + 6)//7), 1)
op50.sizes = ([((s0*s1 + 6)//7)], [])
buf49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf50_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op50_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0, 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0, 1, s0*s1)) + 1
    index4 = ModularIndexing(p0, 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf49', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf50', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op6: ExternKernelSchedulerNode(ExternKernelOut)
op6.writes = [StarDep(name='buf6', mode=None)]
op6.unmet_dependencies = []
op6.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op6.outputs = [
    buf6: ExternKernelOut
    buf6.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf6.users = [
        NodeUser(node=SchedulerNode(name='op7'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op8'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op6.node.kernel = extern_kernels.mm


op7_op8_op9: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op7_op8_op9.writes = 
    [   MemoryDep('buf7', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf8', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf9', c0, {c0: ((s4*s5 + 6)//7)})]
op7_op8_op9.unmet_dependencies = [MemoryDep('buf6', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op7_op8_op9.met_dependencies = []
op7_op8_op9.outputs = [
    buf7: ComputedBuffer
    buf7.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf7.users = [
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf8: ComputedBuffer
    buf8.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf8.users = [NodeUser(node=SchedulerNode(name='op9'), can_inplace=True, is_weak=False)]
    buf9: ComputedBuffer
    buf9.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf9.users = [
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op7_op8_op9.snodes[0] =
op7: SchedulerNode(ComputedBuffer)
op7.writes = [MemoryDep('buf7', c0, {c0: ((s4*s5 + 6)//7)})]
op7.unmet_dependencies = [MemoryDep('buf6', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op7.met_dependencies = []
op7.outputs = [
    buf7: ComputedBuffer
    buf7.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf7.users = [
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op7.group.device = cuda:0
op7.group.iteration = (((s4*s5 + 6)//7), 201088)
op7.sizes = ([((s4*s5 + 6)//7)], [201088])
buf6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf7_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op7_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf6', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf7', get_index_1, getitem)
        return store_reduction
op7_op8_op9.snodes[1] =
op8: SchedulerNode(ComputedBuffer)
op8.writes = [MemoryDep('buf8', c0, {c0: ((s4*s5 + 6)//7)})]
op8.unmet_dependencies = [MemoryDep('buf6', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op8.met_dependencies = []
op8.outputs = [
    buf8: ComputedBuffer
    buf8.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf8.users = [NodeUser(node=SchedulerNode(name='op9'), can_inplace=True, is_weak=False)]
]
op8.group.device = cuda:0
op8.group.iteration = (((s4*s5 + 6)//7), 201088)
op8.sizes = ([((s4*s5 + 6)//7)], [201088])
buf6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf8_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op8_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf6', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf8', get_index_1, getitem_1)
        return store_reduction
op7_op8_op9.snodes[2] =
op9: SchedulerNode(ComputedBuffer)
op9.writes = [MemoryDep('buf9', c0, {c0: ((s4*s5 + 6)//7)})]
op9.unmet_dependencies = [MemoryDep('buf8', c0, {c0: ((s4*s5 + 6)//7)})]
op9.met_dependencies = []
op9.outputs = [
    buf9: ComputedBuffer
    buf9.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf9.users = [
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op9.group.device = cuda:0
op9.group.iteration = (((s4*s5 + 6)//7), 1)
op9.sizes = ([((s4*s5 + 6)//7)], [])
buf8_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op9_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf8', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf9', get_index_1, log, None)
        return store


op10_op47_op48: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op10_op47_op48.writes = 
    [   MemoryDep('buf10', 0, {}),
        MemoryDep('buf47', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf48', c0, {c0: ((s0*s1 + 6)//7)})]
op10_op47_op48.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf6', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf7', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf9', c0, {c0: ((s0*s1 + 6)//7)})]
op10_op47_op48.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op10_op47_op48.outputs = [
    buf10: ComputedBuffer
    buf10.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf10.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf47: ComputedBuffer
    buf47.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf47.users = [
        NodeUser(node=SchedulerNode(name='op48'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf48: ComputedBuffer
    buf48.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf48.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op10_op47_op48.snodes[0] =
op10: SchedulerNode(ComputedBuffer)
op10.writes = [MemoryDep('buf10', 0, {})]
op10.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf6', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf7', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf9', c0, {c0: ((s0*s1 + 6)//7)})]
op10.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op10.outputs = [
    buf10: ComputedBuffer
    buf10.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf10.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op10.group.device = cuda:0
op10.group.iteration = (1, ((s0*s1 + 6)//7))
op10.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf7_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf9_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op10_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf6', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf7', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf9', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf10', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op10_op47_op48.snodes[1] =
op47: SchedulerNode(ComputedBuffer)
op47.writes = [MemoryDep('buf47', c0, {c0: ((s0*s1 + 6)//7)})]
op47.unmet_dependencies = [   MemoryDep('buf0', ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op47.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op47.outputs = [
    buf47: ComputedBuffer
    buf47.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf47.users = [
        NodeUser(node=SchedulerNode(name='op48'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op47.group.device = cuda:0
op47.group.iteration = (((s0*s1 + 6)//7), 1)
op47.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op47_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf47', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op10_op47_op48.snodes[2] =
op48: SchedulerNode(ComputedBuffer)
op48.writes = [MemoryDep('buf48', c0, {c0: ((s0*s1 + 6)//7)})]
op48.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf47', c0, {c0: ((s0*s1 + 6)//7)})]
op48.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op48.outputs = [
    buf48: ComputedBuffer
    buf48.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf48.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op48.group.device = cuda:0
op48.group.iteration = (((s0*s1 + 6)//7), 1)
op48.sizes = ([((s0*s1 + 6)//7)], [])
buf47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf48_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op48_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index4 = ModularIndexing(p0 + (((s0*s1 + 6)//7)), 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf47', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf48', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op11: ExternKernelSchedulerNode(ExternKernelOut)
op11.writes = [StarDep(name='buf11', mode=None)]
op11.unmet_dependencies = []
op11.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op11.outputs = [
    buf11: ExternKernelOut
    buf11.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf11.users = [
        NodeUser(node=SchedulerNode(name='op12'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op13'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op11.node.kernel = extern_kernels.mm


op12_op13_op14: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op12_op13_op14.writes = 
    [   MemoryDep('buf12', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf13', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf14', c0, {c0: ((s4*s5 + 6)//7)})]
op12_op13_op14.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op12_op13_op14.met_dependencies = []
op12_op13_op14.outputs = [
    buf12: ComputedBuffer
    buf12.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf12.users = [
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf13.users = [NodeUser(node=SchedulerNode(name='op14'), can_inplace=True, is_weak=False)]
    buf14: ComputedBuffer
    buf14.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf14.users = [
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op12_op13_op14.snodes[0] =
op12: SchedulerNode(ComputedBuffer)
op12.writes = [MemoryDep('buf12', c0, {c0: ((s4*s5 + 6)//7)})]
op12.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op12.met_dependencies = []
op12.outputs = [
    buf12: ComputedBuffer
    buf12.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf12.users = [
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op12.group.device = cuda:0
op12.group.iteration = (((s4*s5 + 6)//7), 201088)
op12.sizes = ([((s4*s5 + 6)//7)], [201088])
buf11_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op12_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf11', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf12', get_index_1, getitem)
        return store_reduction
op12_op13_op14.snodes[1] =
op13: SchedulerNode(ComputedBuffer)
op13.writes = [MemoryDep('buf13', c0, {c0: ((s4*s5 + 6)//7)})]
op13.unmet_dependencies = [MemoryDep('buf11', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op13.met_dependencies = []
op13.outputs = [
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf13.users = [NodeUser(node=SchedulerNode(name='op14'), can_inplace=True, is_weak=False)]
]
op13.group.device = cuda:0
op13.group.iteration = (((s4*s5 + 6)//7), 201088)
op13.sizes = ([((s4*s5 + 6)//7)], [201088])
buf11_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op13_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf11', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf13', get_index_1, getitem_1)
        return store_reduction
op12_op13_op14.snodes[2] =
op14: SchedulerNode(ComputedBuffer)
op14.writes = [MemoryDep('buf14', c0, {c0: ((s4*s5 + 6)//7)})]
op14.unmet_dependencies = [MemoryDep('buf13', c0, {c0: ((s4*s5 + 6)//7)})]
op14.met_dependencies = []
op14.outputs = [
    buf14: ComputedBuffer
    buf14.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf14.users = [
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op14.group.device = cuda:0
op14.group.iteration = (((s4*s5 + 6)//7), 1)
op14.sizes = ([((s4*s5 + 6)//7)], [])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf14_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op14_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf13', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf14', get_index_1, log, None)
        return store


op15_op45_op46: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op15_op45_op46.writes = 
    [   MemoryDep('buf15', 0, {}),
        MemoryDep('buf45', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf46', c0, {c0: ((s0*s1 + 6)//7)})]
op15_op45_op46.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf11', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf12', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf14', c0, {c0: ((s0*s1 + 6)//7)})]
op15_op45_op46.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op15_op45_op46.outputs = [
    buf15: ComputedBuffer
    buf15.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf15.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf45: ComputedBuffer
    buf45.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf45.users = [
        NodeUser(node=SchedulerNode(name='op46'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf46: ComputedBuffer
    buf46.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf46.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op15_op45_op46.snodes[0] =
op15: SchedulerNode(ComputedBuffer)
op15.writes = [MemoryDep('buf15', 0, {})]
op15.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf11', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf12', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf14', c0, {c0: ((s0*s1 + 6)//7)})]
op15.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op15.outputs = [
    buf15: ComputedBuffer
    buf15.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf15.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op15.group.device = cuda:0
op15.group.iteration = (1, ((s0*s1 + 6)//7))
op15.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf11_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf12_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf14_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op15_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf11', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf12', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf14', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf15', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op15_op45_op46.snodes[1] =
op45: SchedulerNode(ComputedBuffer)
op45.writes = [MemoryDep('buf45', c0, {c0: ((s0*s1 + 6)//7)})]
op45.unmet_dependencies = [   MemoryDep('buf0', ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op45.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op45.outputs = [
    buf45: ComputedBuffer
    buf45.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf45.users = [
        NodeUser(node=SchedulerNode(name='op46'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op45.group.device = cuda:0
op45.group.iteration = (((s0*s1 + 6)//7), 1)
op45.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf45_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op45_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf45', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op15_op45_op46.snodes[2] =
op46: SchedulerNode(ComputedBuffer)
op46.writes = [MemoryDep('buf46', c0, {c0: ((s0*s1 + 6)//7)})]
op46.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf45', c0, {c0: ((s0*s1 + 6)//7)})]
op46.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op46.outputs = [
    buf46: ComputedBuffer
    buf46.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf46.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op46.group.device = cuda:0
op46.group.iteration = (((s0*s1 + 6)//7), 1)
op46.sizes = ([((s0*s1 + 6)//7)], [])
buf45_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf46_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op46_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index4 = ModularIndexing(p0 + 2*(((s0*s1 + 6)//7)), 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf45', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf46', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op16: ExternKernelSchedulerNode(ExternKernelOut)
op16.writes = [StarDep(name='buf16', mode=None)]
op16.unmet_dependencies = []
op16.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op16.outputs = [
    buf16: ExternKernelOut
    buf16.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf16.users = [
        NodeUser(node=SchedulerNode(name='op17'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op18'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op16.node.kernel = extern_kernels.mm


op17_op18_op19: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op17_op18_op19.writes = 
    [   MemoryDep('buf17', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf18', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf19', c0, {c0: ((s4*s5 + 6)//7)})]
op17_op18_op19.unmet_dependencies = [MemoryDep('buf16', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op17_op18_op19.met_dependencies = []
op17_op18_op19.outputs = [
    buf17: ComputedBuffer
    buf17.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf17.users = [
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf18: ComputedBuffer
    buf18.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf18.users = [NodeUser(node=SchedulerNode(name='op19'), can_inplace=True, is_weak=False)]
    buf19: ComputedBuffer
    buf19.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf19.users = [
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op17_op18_op19.snodes[0] =
op17: SchedulerNode(ComputedBuffer)
op17.writes = [MemoryDep('buf17', c0, {c0: ((s4*s5 + 6)//7)})]
op17.unmet_dependencies = [MemoryDep('buf16', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op17.met_dependencies = []
op17.outputs = [
    buf17: ComputedBuffer
    buf17.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf17.users = [
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op17.group.device = cuda:0
op17.group.iteration = (((s4*s5 + 6)//7), 201088)
op17.sizes = ([((s4*s5 + 6)//7)], [201088])
buf16_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op17_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf16', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf17', get_index_1, getitem)
        return store_reduction
op17_op18_op19.snodes[1] =
op18: SchedulerNode(ComputedBuffer)
op18.writes = [MemoryDep('buf18', c0, {c0: ((s4*s5 + 6)//7)})]
op18.unmet_dependencies = [MemoryDep('buf16', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op18.met_dependencies = []
op18.outputs = [
    buf18: ComputedBuffer
    buf18.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf18.users = [NodeUser(node=SchedulerNode(name='op19'), can_inplace=True, is_weak=False)]
]
op18.group.device = cuda:0
op18.group.iteration = (((s4*s5 + 6)//7), 201088)
op18.sizes = ([((s4*s5 + 6)//7)], [201088])
buf16_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op18_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf16', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf18', get_index_1, getitem_1)
        return store_reduction
op17_op18_op19.snodes[2] =
op19: SchedulerNode(ComputedBuffer)
op19.writes = [MemoryDep('buf19', c0, {c0: ((s4*s5 + 6)//7)})]
op19.unmet_dependencies = [MemoryDep('buf18', c0, {c0: ((s4*s5 + 6)//7)})]
op19.met_dependencies = []
op19.outputs = [
    buf19: ComputedBuffer
    buf19.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf19.users = [
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op19.group.device = cuda:0
op19.group.iteration = (((s4*s5 + 6)//7), 1)
op19.sizes = ([((s4*s5 + 6)//7)], [])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf19_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op19_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf18', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf19', get_index_1, log, None)
        return store


op20_op43_op44: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op20_op43_op44.writes = 
    [   MemoryDep('buf20', 0, {}),
        MemoryDep('buf43', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf44', c0, {c0: ((s0*s1 + 6)//7)})]
op20_op43_op44.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf16', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf17', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf19', c0, {c0: ((s0*s1 + 6)//7)})]
op20_op43_op44.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op20_op43_op44.outputs = [
    buf20: ComputedBuffer
    buf20.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf20.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf43: ComputedBuffer
    buf43.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf43.users = [
        NodeUser(node=SchedulerNode(name='op44'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf44: ComputedBuffer
    buf44.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf44.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op20_op43_op44.snodes[0] =
op20: SchedulerNode(ComputedBuffer)
op20.writes = [MemoryDep('buf20', 0, {})]
op20.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf16', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf17', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf19', c0, {c0: ((s0*s1 + 6)//7)})]
op20.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op20.outputs = [
    buf20: ComputedBuffer
    buf20.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf20.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op20.group.device = cuda:0
op20.group.iteration = (1, ((s0*s1 + 6)//7))
op20.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf16_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf17_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf19_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op20_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf16', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf17', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf19', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf20', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op20_op43_op44.snodes[1] =
op43: SchedulerNode(ComputedBuffer)
op43.writes = [MemoryDep('buf43', c0, {c0: ((s0*s1 + 6)//7)})]
op43.unmet_dependencies = [   MemoryDep('buf0', ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op43.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op43.outputs = [
    buf43: ComputedBuffer
    buf43.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf43.users = [
        NodeUser(node=SchedulerNode(name='op44'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op43.group.device = cuda:0
op43.group.iteration = (((s0*s1 + 6)//7), 1)
op43.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf43_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op43_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf43', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op20_op43_op44.snodes[2] =
op44: SchedulerNode(ComputedBuffer)
op44.writes = [MemoryDep('buf44', c0, {c0: ((s0*s1 + 6)//7)})]
op44.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf43', c0, {c0: ((s0*s1 + 6)//7)})]
op44.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op44.outputs = [
    buf44: ComputedBuffer
    buf44.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf44.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op44.group.device = cuda:0
op44.group.iteration = (((s0*s1 + 6)//7), 1)
op44.sizes = ([((s0*s1 + 6)//7)], [])
buf43_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf44_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op44_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index4 = ModularIndexing(p0 + 3*(((s0*s1 + 6)//7)), 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf43', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf44', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op21: ExternKernelSchedulerNode(ExternKernelOut)
op21.writes = [StarDep(name='buf21', mode=None)]
op21.unmet_dependencies = []
op21.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op21.outputs = [
    buf21: ExternKernelOut
    buf21.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf21.users = [
        NodeUser(node=SchedulerNode(name='op22'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op23'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op21.node.kernel = extern_kernels.mm


op22_op23_op24: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op22_op23_op24.writes = 
    [   MemoryDep('buf22', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf23', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf24', c0, {c0: ((s4*s5 + 6)//7)})]
op22_op23_op24.unmet_dependencies = [MemoryDep('buf21', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op22_op23_op24.met_dependencies = []
op22_op23_op24.outputs = [
    buf22: ComputedBuffer
    buf22.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf22.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf23: ComputedBuffer
    buf23.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf23.users = [NodeUser(node=SchedulerNode(name='op24'), can_inplace=True, is_weak=False)]
    buf24: ComputedBuffer
    buf24.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf24.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op22_op23_op24.snodes[0] =
op22: SchedulerNode(ComputedBuffer)
op22.writes = [MemoryDep('buf22', c0, {c0: ((s4*s5 + 6)//7)})]
op22.unmet_dependencies = [MemoryDep('buf21', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op22.met_dependencies = []
op22.outputs = [
    buf22: ComputedBuffer
    buf22.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf22.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op22.group.device = cuda:0
op22.group.iteration = (((s4*s5 + 6)//7), 201088)
op22.sizes = ([((s4*s5 + 6)//7)], [201088])
buf21_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf22_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op22_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf21', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf22', get_index_1, getitem)
        return store_reduction
op22_op23_op24.snodes[1] =
op23: SchedulerNode(ComputedBuffer)
op23.writes = [MemoryDep('buf23', c0, {c0: ((s4*s5 + 6)//7)})]
op23.unmet_dependencies = [MemoryDep('buf21', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op23.met_dependencies = []
op23.outputs = [
    buf23: ComputedBuffer
    buf23.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf23.users = [NodeUser(node=SchedulerNode(name='op24'), can_inplace=True, is_weak=False)]
]
op23.group.device = cuda:0
op23.group.iteration = (((s4*s5 + 6)//7), 201088)
op23.sizes = ([((s4*s5 + 6)//7)], [201088])
buf21_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op23_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf21', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf23', get_index_1, getitem_1)
        return store_reduction
op22_op23_op24.snodes[2] =
op24: SchedulerNode(ComputedBuffer)
op24.writes = [MemoryDep('buf24', c0, {c0: ((s4*s5 + 6)//7)})]
op24.unmet_dependencies = [MemoryDep('buf23', c0, {c0: ((s4*s5 + 6)//7)})]
op24.met_dependencies = []
op24.outputs = [
    buf24: ComputedBuffer
    buf24.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf24.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op24.group.device = cuda:0
op24.group.iteration = (((s4*s5 + 6)//7), 1)
op24.sizes = ([((s4*s5 + 6)//7)], [])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op24_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf23', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf24', get_index_1, log, None)
        return store


op25_op41_op42: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op25_op41_op42.writes = 
    [   MemoryDep('buf25', 0, {}),
        MemoryDep('buf41', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf42', c0, {c0: ((s0*s1 + 6)//7)})]
op25_op41_op42.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf21', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf22', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf24', c0, {c0: ((s0*s1 + 6)//7)})]
op25_op41_op42.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op25_op41_op42.outputs = [
    buf25: ComputedBuffer
    buf25.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf25.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf41: ComputedBuffer
    buf41.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf41.users = [
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf42: ComputedBuffer
    buf42.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf42.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op25_op41_op42.snodes[0] =
op25: SchedulerNode(ComputedBuffer)
op25.writes = [MemoryDep('buf25', 0, {})]
op25.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf21', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf22', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf24', c0, {c0: ((s0*s1 + 6)//7)})]
op25.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op25.outputs = [
    buf25: ComputedBuffer
    buf25.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf25.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op25.group.device = cuda:0
op25.group.iteration = (1, ((s0*s1 + 6)//7))
op25.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf21_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf22_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf24_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op25_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf21', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf22', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf24', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf25', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op25_op41_op42.snodes[1] =
op41: SchedulerNode(ComputedBuffer)
op41.writes = [MemoryDep('buf41', c0, {c0: ((s0*s1 + 6)//7)})]
op41.unmet_dependencies = [   MemoryDep('buf0', ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op41.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op41.outputs = [
    buf41: ComputedBuffer
    buf41.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf41.users = [
        NodeUser(node=SchedulerNode(name='op42'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op41.group.device = cuda:0
op41.group.iteration = (((s0*s1 + 6)//7), 1)
op41.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf41_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op41_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf41', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op25_op41_op42.snodes[2] =
op42: SchedulerNode(ComputedBuffer)
op42.writes = [MemoryDep('buf42', c0, {c0: ((s0*s1 + 6)//7)})]
op42.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf41', c0, {c0: ((s0*s1 + 6)//7)})]
op42.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op42.outputs = [
    buf42: ComputedBuffer
    buf42.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf42.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op42.group.device = cuda:0
op42.group.iteration = (((s0*s1 + 6)//7), 1)
op42.sizes = ([((s0*s1 + 6)//7)], [])
buf41_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf42_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op42_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index4 = ModularIndexing(p0 + 4*(((s0*s1 + 6)//7)), 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf41', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf42', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op26: ExternKernelSchedulerNode(ExternKernelOut)
op26.writes = [StarDep(name='buf26', mode=None)]
op26.unmet_dependencies = []
op26.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op26.outputs = [
    buf26: ExternKernelOut
    buf26.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf26.users = [
        NodeUser(node=SchedulerNode(name='op27'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op28'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op26.node.kernel = extern_kernels.mm


op27_op28_op29: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op27_op28_op29.writes = 
    [   MemoryDep('buf27', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf28', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf29', c0, {c0: ((s4*s5 + 6)//7)})]
op27_op28_op29.unmet_dependencies = [MemoryDep('buf26', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op27_op28_op29.met_dependencies = []
op27_op28_op29.outputs = [
    buf27: ComputedBuffer
    buf27.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf27.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf28: ComputedBuffer
    buf28.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf28.users = [NodeUser(node=SchedulerNode(name='op29'), can_inplace=True, is_weak=False)]
    buf29: ComputedBuffer
    buf29.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf29.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op27_op28_op29.snodes[0] =
op27: SchedulerNode(ComputedBuffer)
op27.writes = [MemoryDep('buf27', c0, {c0: ((s4*s5 + 6)//7)})]
op27.unmet_dependencies = [MemoryDep('buf26', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op27.met_dependencies = []
op27.outputs = [
    buf27: ComputedBuffer
    buf27.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf27.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op27.group.device = cuda:0
op27.group.iteration = (((s4*s5 + 6)//7), 201088)
op27.sizes = ([((s4*s5 + 6)//7)], [201088])
buf26_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op27_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf26', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf27', get_index_1, getitem)
        return store_reduction
op27_op28_op29.snodes[1] =
op28: SchedulerNode(ComputedBuffer)
op28.writes = [MemoryDep('buf28', c0, {c0: ((s4*s5 + 6)//7)})]
op28.unmet_dependencies = [MemoryDep('buf26', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op28.met_dependencies = []
op28.outputs = [
    buf28: ComputedBuffer
    buf28.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf28.users = [NodeUser(node=SchedulerNode(name='op29'), can_inplace=True, is_weak=False)]
]
op28.group.device = cuda:0
op28.group.iteration = (((s4*s5 + 6)//7), 201088)
op28.sizes = ([((s4*s5 + 6)//7)], [201088])
buf26_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op28_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf26', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf28', get_index_1, getitem_1)
        return store_reduction
op27_op28_op29.snodes[2] =
op29: SchedulerNode(ComputedBuffer)
op29.writes = [MemoryDep('buf29', c0, {c0: ((s4*s5 + 6)//7)})]
op29.unmet_dependencies = [MemoryDep('buf28', c0, {c0: ((s4*s5 + 6)//7)})]
op29.met_dependencies = []
op29.outputs = [
    buf29: ComputedBuffer
    buf29.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
    buf29.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op29.group.device = cuda:0
op29.group.iteration = (((s4*s5 + 6)//7), 1)
op29.sizes = ([((s4*s5 + 6)//7)], [])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf29_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
class op29_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7)}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf28', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf29', get_index_1, log, None)
        return store


op30_op39_op40: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op30_op39_op40.writes = 
    [   MemoryDep('buf30', 0, {}),
        MemoryDep('buf39', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf40', c0, {c0: ((s0*s1 + 6)//7)})]
op30_op39_op40.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf26', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf27', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf29', c0, {c0: ((s0*s1 + 6)//7)})]
op30_op39_op40.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op30_op39_op40.outputs = [
    buf30: ComputedBuffer
    buf30.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf30.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf39: ComputedBuffer
    buf39.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf39.users = [
        NodeUser(node=SchedulerNode(name='op40'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf40: ComputedBuffer
    buf40.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf40.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op30_op39_op40.snodes[0] =
op30: SchedulerNode(ComputedBuffer)
op30.writes = [MemoryDep('buf30', 0, {})]
op30.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf26', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf27', c0, {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf29', c0, {c0: ((s0*s1 + 6)//7)})]
op30.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op30.outputs = [
    buf30: ComputedBuffer
    buf30.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf30.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op30.group.device = cuda:0
op30.group.iteration = (1, ((s0*s1 + 6)//7))
op30.sizes = ([], [((s0*s1 + 6)//7)])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf26_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf27_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf29_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op30_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf26', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf27', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf29', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf30', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op30_op39_op40.snodes[1] =
op39: SchedulerNode(ComputedBuffer)
op39.writes = [MemoryDep('buf39', c0, {c0: ((s0*s1 + 6)//7)})]
op39.unmet_dependencies = [   MemoryDep('buf0', ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)})]
op39.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op39.outputs = [
    buf39: ComputedBuffer
    buf39.layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf39.users = [
        NodeUser(node=SchedulerNode(name='op40'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op39.group.device = cuda:0
op39.group.iteration = (((s0*s1 + 6)//7), 1)
op39.sizes = ([((s0*s1 + 6)//7)], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf39_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op39_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = (ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index3 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf39', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op30_op39_op40.snodes[2] =
op40: SchedulerNode(ComputedBuffer)
op40.writes = [MemoryDep('buf40', c0, {c0: ((s0*s1 + 6)//7)})]
op40.unmet_dependencies = 
    [   MemoryDep('buf0', ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1), {c0: ((s0*s1 + 6)//7)}),
        MemoryDep('buf39', c0, {c0: ((s0*s1 + 6)//7)})]
op40.met_dependencies = [   MemoryDep('primals_4', (ModularIndexing(c0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1, {c0: ((s0*s1 + 6)//7)})]
op40.outputs = [
    buf40: ComputedBuffer
    buf40.layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
    buf40.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op40.group.device = cuda:0
op40.group.iteration = (((s0*s1 + 6)//7), 1)
op40.sizes = ([((s0*s1 + 6)//7)], [])
buf39_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf40_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
class op40_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = (ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)) + 1
    index4 = ModularIndexing(p0 + 5*(((s0*s1 + 6)//7)), 1, s0*s1)
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf39', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf40', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load


op31: ExternKernelSchedulerNode(ExternKernelOut)
op31.writes = [StarDep(name='buf31', mode=None)]
op31.unmet_dependencies = []
op31.met_dependencies = [StarDep(name='primals_1', mode=None), StarDep(name='primals_7', mode=None)]
op31.outputs = [
    buf31: ExternKernelOut
    buf31.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
    buf31.users = [
        NodeUser(node=SchedulerNode(name='op32'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op31.node.kernel = extern_kernels.mm


op32_op33_op34: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode)
op32_op33_op34.writes = 
    [   MemoryDep('buf32', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('buf33', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('buf34', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op32_op33_op34.unmet_dependencies = [MemoryDep('buf31', c0, {c0: 201088*s4*s5 - 1206528*(((s4*s5 + 6)//7))})]
op32_op33_op34.met_dependencies = []
op32_op33_op34.outputs = [
    buf32: ComputedBuffer
    buf32.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
    buf32.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf33: ComputedBuffer
    buf33.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
    buf33.users = [NodeUser(node=SchedulerNode(name='op34'), can_inplace=True, is_weak=False)]
    buf34: ComputedBuffer
    buf34.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
    buf34.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op32_op33_op34.snodes[0] =
op32: SchedulerNode(ComputedBuffer)
op32.writes = [MemoryDep('buf32', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op32.unmet_dependencies = [MemoryDep('buf31', c0, {c0: 201088*s4*s5 - 1206528*(((s4*s5 + 6)//7))})]
op32.met_dependencies = []
op32.outputs = [
    buf32: ComputedBuffer
    buf32.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
    buf32.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op32.group.device = cuda:0
op32.group.iteration = (s4*s5 - 6*(((s4*s5 + 6)//7)), 201088)
op32.sizes = ([s4*s5 - 6*(((s4*s5 + 6)//7))], [201088])
buf31_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
buf32_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
class op32_loop_body:
    var_ranges = {p0: s4*s5 - 6*(((s4*s5 + 6)//7)), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf31', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf32', get_index_1, getitem)
        return store_reduction
op32_op33_op34.snodes[1] =
op33: SchedulerNode(ComputedBuffer)
op33.writes = [MemoryDep('buf33', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op33.unmet_dependencies = [MemoryDep('buf31', c0, {c0: 201088*s4*s5 - 1206528*(((s4*s5 + 6)//7))})]
op33.met_dependencies = []
op33.outputs = [
    buf33: ComputedBuffer
    buf33.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
    buf33.users = [NodeUser(node=SchedulerNode(name='op34'), can_inplace=True, is_weak=False)]
]
op33.group.device = cuda:0
op33.group.iteration = (s4*s5 - 6*(((s4*s5 + 6)//7)), 201088)
op33.sizes = ([s4*s5 - 6*(((s4*s5 + 6)//7))], [201088])
buf31_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
class op33_loop_body:
    var_ranges = {p0: s4*s5 - 6*(((s4*s5 + 6)//7)), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf31', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.bfloat16)
        reduction = ops.reduction(torch.float32, torch.float32, 'online_softmax_reduce', to_dtype)
        getitem = reduction[0]
        getitem_1 = reduction[1]
        get_index_1 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf33', get_index_1, getitem_1)
        return store_reduction
op32_op33_op34.snodes[2] =
op34: SchedulerNode(ComputedBuffer)
op34.writes = [MemoryDep('buf34', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op34.unmet_dependencies = [MemoryDep('buf33', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op34.met_dependencies = []
op34.outputs = [
    buf34: ComputedBuffer
    buf34.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
    buf34.users = [
        NodeUser(node=SchedulerNode(name='op35'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op34.group.device = cuda:0
op34.group.iteration = (s4*s5 - 6*(((s4*s5 + 6)//7)), 1)
op34.sizes = ([s4*s5 - 6*(((s4*s5 + 6)//7))], [])
buf33_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
buf34_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
class op34_loop_body:
    var_ranges = {p0: s4*s5 - 6*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf33', get_index)
        log = ops.log(load)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf34', get_index_1, log, None)
        return store


op35_op37_op38_op36_op51: FusedSchedulerNode(SchedulerNode,SchedulerNode,SchedulerNode,SchedulerNode,SchedulerNode)
op35_op37_op38_op36_op51.writes = 
    [   MemoryDep('buf35', 0, {}),
        MemoryDep('buf36', 0, {}),
        MemoryDep('buf37', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf38', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf51', 0, {})]
op35_op37_op38_op36_op51.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0*s1 + 6)//7)), {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf10', 0, {}),
        MemoryDep('buf15', 0, {}),
        MemoryDep('buf20', 0, {}),
        MemoryDep('buf25', 0, {}),
        MemoryDep('buf30', 0, {}),
        MemoryDep('buf31', 201088*c0 + tmp0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf32', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf34', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf5', 0, {})]
op35_op37_op38_op36_op51.met_dependencies = 
    [   MemoryDep('primals_4', c0 + 6*(((s0*s1 + 6)//7)) + 1, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('primals_8', 0, {})]
op35_op37_op38_op36_op51.outputs = [
    buf35: ComputedBuffer
    buf35.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf35.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
    buf37: ComputedBuffer
    buf37.layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
    buf37.users = [
        NodeUser(node=SchedulerNode(name='op38'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf38: ComputedBuffer
    buf38.layout = FixedLayout('cuda:0', torch.int64, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
    buf38.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
    buf36: ComputedBuffer
    buf36.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf36.users = [
        NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
    buf51: ComputedBuffer
    buf51.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf51.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op35_op37_op38_op36_op51.snodes[0] =
op35: SchedulerNode(ComputedBuffer)
op35.writes = [MemoryDep('buf35', 0, {})]
op35.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0*s1 + 6)//7)), {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf31', 201088*c0 + tmp0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf32', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf34', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op35.met_dependencies = [   MemoryDep('primals_4', c0 + 6*(((s0*s1 + 6)//7)) + 1, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op35.outputs = [
    buf35: ComputedBuffer
    buf35.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf35.users = [NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False)]
]
op35.group.device = cuda:0
op35.group.iteration = (1, s0*s1 - 6*(((s0*s1 + 6)//7)))
op35.sizes = ([], [s0*s1 - 6*(((s0*s1 + 6)//7))])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf31_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
buf32_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
buf34_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op35_loop_body:
    var_ranges = {p0: s0*s1 - 6*(((s0*s1 + 6)//7))}
    index0 = ModularIndexing(p0 + 6*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = p0 + 6*(((s0*s1 + 6)//7)) + 1
    index3 = p0 + 6*(((s0*s1 + 6)//7))
    index4 = indirect0 + 201088*p0
    index5 = p0
    index6 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index0')
        index_expr_4 = ops.index_expr(get_index_5, torch.int32)
        get_index_6 = self.get_index('index1')
        index_expr_5 = ops.index_expr(get_index_6, torch.int32)
        eq_1 = ops.eq(index_expr_4, index_expr_5)
        get_index_7 = self.get_index('index0')
        index_expr_6 = ops.index_expr(get_index_7, torch.int64)
        get_index_8 = self.get_index('index1')
        index_expr_7 = ops.index_expr(get_index_8, torch.int64)
        lt_1 = ops.lt(index_expr_6, index_expr_7)
        masked_subblock2 = self.masked_subblock2(lt_1, 0)
        get_index_9 = self.get_index('index3')
        load_1 = ops.load('buf0', get_index_9)
        where_2 = ops.where(lt_1, masked_subblock2, load_1)
        constant_2 = ops.constant(-100, torch.int64)
        where_3 = ops.where(eq_1, constant_2, where_2)
        constant_3 = ops.constant(-100, torch.int64)
        ne_1 = ops.ne(where_3, constant_3)
        get_index_10 = self.get_index('index0')
        index_expr_8 = ops.index_expr(get_index_10, torch.int32)
        get_index_11 = self.get_index('index1')
        index_expr_9 = ops.index_expr(get_index_11, torch.int32)
        eq_2 = ops.eq(index_expr_8, index_expr_9)
        get_index_12 = self.get_index('index0')
        index_expr_10 = ops.index_expr(get_index_12, torch.int64)
        get_index_13 = self.get_index('index1')
        index_expr_11 = ops.index_expr(get_index_13, torch.int64)
        lt_2 = ops.lt(index_expr_10, index_expr_11)
        masked_subblock3 = self.masked_subblock3(lt_2, 0)
        get_index_14 = self.get_index('index3')
        load_2 = ops.load('buf0', get_index_14)
        where_4 = ops.where(lt_2, masked_subblock3, load_2)
        constant_4 = ops.constant(-100, torch.int64)
        where_5 = ops.where(eq_2, constant_4, where_4)
        constant_5 = ops.constant(0, torch.int64)
        where_6 = ops.where(ne_1, where_5, constant_5)
        set_indirect0 = self.set_indirect0(where_6)
        get_index_15 = self.get_index('index4')
        load_3 = ops.load('buf31', get_index_15)
        to_dtype = ops.to_dtype(load_3, torch.float32, src_dtype = torch.bfloat16)
        get_index_16 = self.get_index('index5')
        load_4 = ops.load('buf32', get_index_16)
        sub = ops.sub(to_dtype, load_4)
        get_index_17 = self.get_index('index5')
        load_5 = ops.load('buf34', get_index_17)
        sub_1 = ops.sub(sub, load_5)
        neg = ops.neg(sub_1)
        constant_6 = ops.constant(0.0, torch.float32)
        where_7 = ops.where(ne, neg, constant_6)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', where_7)
        get_index_18 = self.get_index('index6')
        store_reduction = ops.store_reduction('buf35', get_index_18, reduction)
        return store_reduction
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock2(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
    def masked_subblock3(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op35_op37_op38_op36_op51.snodes[1] =
op37: SchedulerNode(ComputedBuffer)
op37.writes = [MemoryDep('buf37', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op37.unmet_dependencies = [   MemoryDep('buf0', c0 + 6*(((s0*s1 + 6)//7)), {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op37.met_dependencies = [   MemoryDep('primals_4', c0 + 6*(((s0*s1 + 6)//7)) + 1, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op37.outputs = [
    buf37: ComputedBuffer
    buf37.layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
    buf37.users = [
        NodeUser(node=SchedulerNode(name='op38'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op37.group.device = cuda:0
op37.group.iteration = (s0*s1 - 6*(((s0*s1 + 6)//7)), 1)
op37.sizes = ([s0*s1 - 6*(((s0*s1 + 6)//7))], [])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf37_layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
class op37_loop_body:
    var_ranges = {p0: s0*s1 - 6*(((s0*s1 + 6)//7))}
    index0 = ModularIndexing(p0 + 6*(((s0*s1 + 6)//7)), 1, s1)
    index1 = s1 - 1
    index2 = p0 + 6*(((s0*s1 + 6)//7)) + 1
    index3 = p0 + 6*(((s0*s1 + 6)//7))
    index4 = p0
    def body(self, ops):
        get_index = self.get_index('index0')
        index_expr = ops.index_expr(get_index, torch.int32)
        get_index_1 = self.get_index('index1')
        index_expr_1 = ops.index_expr(get_index_1, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_2 = self.get_index('index0')
        index_expr_2 = ops.index_expr(get_index_2, torch.int64)
        get_index_3 = self.get_index('index1')
        index_expr_3 = ops.index_expr(get_index_3, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_4 = self.get_index('index3')
        load = ops.load('buf0', get_index_4)
        where = ops.where(lt, masked_subblock1, load)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(-100, torch.int64)
        ne = ops.ne(where_1, constant_1)
        get_index_5 = self.get_index('index4')
        store = ops.store('buf37', get_index_5, ne, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index2')
        load = ops.load('primals_4', get_index)
        return load
op35_op37_op38_op36_op51.snodes[2] =
op38: SchedulerNode(ComputedBuffer)
op38.writes = [MemoryDep('buf38', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op38.unmet_dependencies = 
    [   MemoryDep('buf0', c0 + 6*(((s0*s1 + 6)//7)), {c0: s0*s1 - 6*(((s0*s1 + 6)//7))}),
        MemoryDep('buf37', c0, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op38.met_dependencies = [   MemoryDep('primals_4', c0 + 6*(((s0*s1 + 6)//7)) + 1, {c0: s0*s1 - 6*(((s0*s1 + 6)//7))})]
op38.outputs = [
    buf38: ComputedBuffer
    buf38.layout = FixedLayout('cuda:0', torch.int64, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
    buf38.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op38.group.device = cuda:0
op38.group.iteration = (s0*s1 - 6*(((s0*s1 + 6)//7)), 1)
op38.sizes = ([s0*s1 - 6*(((s0*s1 + 6)//7))], [])
buf37_layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
primals_4_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf0_layout = FixedLayout('cuda:0', torch.int64, size=[s0, s1], stride=[s1, 1])
buf38_layout = FixedLayout('cuda:0', torch.int64, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
class op38_loop_body:
    var_ranges = {p0: s0*s1 - 6*(((s0*s1 + 6)//7))}
    index0 = p0
    index1 = ModularIndexing(p0 + 6*(((s0*s1 + 6)//7)), 1, s1)
    index2 = s1 - 1
    index3 = p0 + 6*(((s0*s1 + 6)//7)) + 1
    index4 = p0 + 6*(((s0*s1 + 6)//7))
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf37', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int32)
        get_index_2 = self.get_index('index2')
        index_expr_1 = ops.index_expr(get_index_2, torch.int32)
        eq = ops.eq(index_expr, index_expr_1)
        get_index_3 = self.get_index('index1')
        index_expr_2 = ops.index_expr(get_index_3, torch.int64)
        get_index_4 = self.get_index('index2')
        index_expr_3 = ops.index_expr(get_index_4, torch.int64)
        lt = ops.lt(index_expr_2, index_expr_3)
        masked_subblock1 = self.masked_subblock1(lt, 0)
        get_index_5 = self.get_index('index4')
        load_1 = ops.load('buf0', get_index_5)
        where = ops.where(lt, masked_subblock1, load_1)
        constant = ops.constant(-100, torch.int64)
        where_1 = ops.where(eq, constant, where)
        constant_1 = ops.constant(0, torch.int64)
        where_2 = ops.where(load, where_1, constant_1)
        get_index_6 = self.get_index('index0')
        store = ops.store('buf38', get_index_6, where_2, None)
        return store
    def masked_subblock1(self, ops):
        get_index = self.get_index('index3')
        load = ops.load('primals_4', get_index)
        return load
op35_op37_op38_op36_op51.snodes[3] =
op36: SchedulerNode(ComputedBuffer)
op36.writes = [MemoryDep('buf36', 0, {})]
op36.unmet_dependencies = []
op36.met_dependencies = [MemoryDep('primals_8', 0, {})]
op36.outputs = [
    buf36: ComputedBuffer
    buf36.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf36.users = [
        NodeUser(node=SchedulerNode(name='op51'), can_inplace=True, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op36.group.device = cuda:0
op36.group.iteration = (1, 1)
op36.sizes = ([], [])
primals_8_layout = FixedLayout('cuda:0', torch.int64, size=[], stride=[])
buf36_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op36_loop_body:
    var_ranges = {}
    index0 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('primals_8', get_index)
        to_dtype = ops.to_dtype(load, torch.float32, src_dtype = torch.int64)
        get_index_1 = self.get_index('index0')
        store = ops.store('buf36', get_index_1, to_dtype, None)
        return store
op35_op37_op38_op36_op51.snodes[4] =
op51: SchedulerNode(ComputedBuffer)
op51.writes = [MemoryDep('buf51', 0, {})]
op51.unmet_dependencies = 
    [   MemoryDep('buf10', 0, {}),
        MemoryDep('buf15', 0, {}),
        MemoryDep('buf20', 0, {}),
        MemoryDep('buf25', 0, {}),
        MemoryDep('buf30', 0, {}),
        MemoryDep('buf35', 0, {}),
        MemoryDep('buf36', 0, {}),
        MemoryDep('buf5', 0, {})]
op51.met_dependencies = []
op51.outputs = [
    buf51: ComputedBuffer
    buf51.layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
    buf51.users = [NodeUser(node=OUTPUT, can_inplace=False, is_weak=False)]
]
op51.group.device = cuda:0
op51.group.iteration = (1, 1)
op51.sizes = ([], [])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf35_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf36_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf51_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
class op51_loop_body:
    var_ranges = {}
    index0 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf5', get_index)
        constant = ops.constant(0.0, torch.float32)
        add = ops.add(load, constant)
        get_index_1 = self.get_index('index0')
        load_1 = ops.load('buf10', get_index_1)
        add_1 = ops.add(add, load_1)
        get_index_2 = self.get_index('index0')
        load_2 = ops.load('buf15', get_index_2)
        add_2 = ops.add(add_1, load_2)
        get_index_3 = self.get_index('index0')
        load_3 = ops.load('buf20', get_index_3)
        add_3 = ops.add(add_2, load_3)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('buf25', get_index_4)
        add_4 = ops.add(add_3, load_4)
        get_index_5 = self.get_index('index0')
        load_5 = ops.load('buf30', get_index_5)
        add_5 = ops.add(add_4, load_5)
        get_index_6 = self.get_index('index0')
        load_6 = ops.load('buf35', get_index_6)
        add_6 = ops.add(add_5, load_6)
        get_index_7 = self.get_index('index0')
        load_7 = ops.load('buf36', get_index_7)
        truediv = ops.truediv(add_6, load_7)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf51', get_index_8, truediv, None)
        return store


