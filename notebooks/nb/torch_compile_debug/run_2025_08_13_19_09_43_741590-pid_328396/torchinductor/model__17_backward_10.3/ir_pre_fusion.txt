op0: SchedulerNode(ComputedBuffer)
op0.writes = [MemoryDep('buf0', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op0.unmet_dependencies = []
op0.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_45', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('tangents_1', 0, {}),
        MemoryDep('where_14', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op0.outputs = [
    buf0: ComputedBuffer
    buf0.layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
    buf0.users = [NodeUser(node=SchedulerNode(name='op1'), can_inplace=False, is_weak=False)]
]
op0.group.device = cuda:0
op0.group.iteration = (s4*s5 - 6*(((s4*s5 + 6)//7)), 201088)
op0.sizes = ([s4*s5 - 6*(((s4*s5 + 6)//7))], [201088])
where_14_layout = FixedLayout('cuda:0', torch.int64, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
ne_45_layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf0_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
class op0_loop_body:
    var_ranges = {p0: s4*s5 - 6*(((s4*s5 + 6)//7)), p1: 201088}
    index0 = p0
    index1 = p1
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_14', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int64)
        eq = ops.eq(load, index_expr)
        constant = ops.constant(-1.0, torch.float32)
        constant_1 = ops.constant(0.0, torch.float32)
        where = ops.where(eq, constant, constant_1)
        get_index_2 = self.get_index('index0')
        load_1 = ops.load('ne_45', get_index_2)
        get_index_3 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_3)
        get_index_4 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_4)
        truediv = ops.truediv(load_2, load_3)
        constant_2 = ops.constant(0.0, torch.float32)
        where_1 = ops.where(load_1, truediv, constant_2)
        mul = ops.mul(where, where_1)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_5 = self.get_index('index0')
        store_reduction = ops.store_reduction('buf0', get_index_5, reduction)
        return store_reduction


op1: SchedulerNode(ComputedBuffer)
op1.writes = [MemoryDep('buf1', c0, {c0: 201088*s4*s5 - 1206528*(((s4*s5 + 6)//7))})]
op1.unmet_dependencies = [MemoryDep('buf0', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op1.met_dependencies = 
    [   MemoryDep('amax_6', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_6', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('mm_6', c0, {c0: 201088*s4*s5 - 1206528*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_45', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))}),
        MemoryDep('tangents_1', 0, {}),
        MemoryDep('where_14', c0, {c0: s4*s5 - 6*(((s4*s5 + 6)//7))})]
op1.outputs = [
    buf1: ComputedBuffer
    buf1.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
    buf1.users = [NodeUser(node=ExternKernelSchedulerNode(name='op2'), can_inplace=False, is_weak=False)]
]
op1.group.device = cuda:0
op1.group.iteration = (201088*s4*s5 - 1206528*(((s4*s5 + 6)//7)), 1)
op1.sizes = ([s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], [])
where_14_layout = FixedLayout('cuda:0', torch.int64, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
ne_45_layout = FixedLayout('cuda:0', torch.bool, size=[s0*s1 - 6*(((s0*s1 + 6)//7)), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
amax_6_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
log_6_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, 1])
buf0_layout = FixedLayout('cuda:0', torch.float32, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 1], stride=[1, s4*s5 - 6*(((s4*s5 + 6)//7))])
buf1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 201088], stride=[201088, 1])
class op1_loop_body:
    var_ranges = {p0: s4*s5 - 6*(((s4*s5 + 6)//7)), p1: 201088}
    index0 = p0
    index1 = p1
    index2 = 0
    index3 = 201088*p0 + p1
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_14', get_index)
        get_index_1 = self.get_index('index1')
        index_expr = ops.index_expr(get_index_1, torch.int64)
        eq = ops.eq(load, index_expr)
        constant = ops.constant(-1.0, torch.float32)
        constant_1 = ops.constant(0.0, torch.float32)
        where = ops.where(eq, constant, constant_1)
        get_index_2 = self.get_index('index0')
        load_1 = ops.load('ne_45', get_index_2)
        get_index_3 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_3)
        get_index_4 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_4)
        truediv = ops.truediv(load_2, load_3)
        constant_2 = ops.constant(0.0, torch.float32)
        where_1 = ops.where(load_1, truediv, constant_2)
        mul = ops.mul(where, where_1)
        get_index_5 = self.get_index('index3')
        load_4 = ops.load('mm_6', get_index_5)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_6 = self.get_index('index0')
        load_5 = ops.load('amax_6', get_index_6)
        sub = ops.sub(to_dtype, load_5)
        get_index_7 = self.get_index('index0')
        load_6 = ops.load('log_6', get_index_7)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_8 = self.get_index('index0')
        load_7 = ops.load('buf0', get_index_8)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_9 = self.get_index('index3')
        store = ops.store('buf1', get_index_9, to_dtype_1, None)
        return store


op2: ExternKernelSchedulerNode(ExternKernelOut)
op2.writes = [StarDep(name='buf2', mode=None)]
op2.unmet_dependencies = [StarDep(name='buf1', mode=None)]
op2.met_dependencies = [StarDep(name='permute_8', mode=None)]
op2.outputs = [
    buf2: ExternKernelOut
    buf2.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[s4*s5 - 6*(((s4*s5 + 6)//7)), 2880], stride=[2880, 1])
    buf2.aliases = ['buf33']
    buf2.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op2.node.kernel = extern_kernels.mm


op3: SchedulerNode(ComputedBuffer)
op3.writes = [MemoryDep('buf3', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op3.unmet_dependencies = []
op3.met_dependencies = []
op3.outputs = [
    buf3: ComputedBuffer
    buf3.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf3.users = [NodeUser(node=SchedulerNode(name='op4'), can_inplace=False, is_weak=False)]
]
op3.group.device = cuda:0
op3.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op3.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op3_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf3', get_index, constant, None)
        return store


op4: SchedulerNode(ComputedBuffer)
op4.writes = [MemoryDep('buf4', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op4.unmet_dependencies = [StarDep(name='buf3', mode=None)]
op4.met_dependencies = [MemoryDep('where_16', c0, {c0: ((s0*s1 + 6)//7)})]
op4.outputs = [
    buf4: ComputedBuffer
    buf4.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf4.mutations = ['buf3']
    buf4.users = [
        NodeUser(node=SchedulerNode(name='op5'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op6'), can_inplace=True, is_weak=False),
    ]
]
op4.group.device = cuda:0
op4.group.iteration = (((s0*s1 + 6)//7), 1)
op4.sizes = ([((s0*s1 + 6)//7)], [])
where_16_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op4_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_16', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf4', get_index_1, constant, None)
        return store


op5: SchedulerNode(ComputedBuffer)
op5.writes = [MemoryDep('buf5', c0, {c0: ((s4*s5 + 6)//7)})]
op5.unmet_dependencies = [MemoryDep('buf4', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op5.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_47', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op5.outputs = [
    buf5: ComputedBuffer
    buf5.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf5.users = [NodeUser(node=SchedulerNode(name='op6'), can_inplace=False, is_weak=False)]
]
op5.group.device = cuda:0
op5.group.iteration = (((s4*s5 + 6)//7), 201088)
op5.sizes = ([((s4*s5 + 6)//7)], [201088])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op5_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf3', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_47', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf5', get_index_4, reduction)
        return store_reduction


op6: SchedulerNode(ComputedBuffer)
op6.writes = [MemoryDep('buf6', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op6.unmet_dependencies = 
    [   MemoryDep('buf4', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('buf5', c0, {c0: ((s4*s5 + 6)//7)})]
op6.met_dependencies = 
    [   MemoryDep('amax_5', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_5', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm_5', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_47', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op6.outputs = [
    buf6: ComputedBuffer
    buf6.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf6.users = [NodeUser(node=ExternKernelSchedulerNode(name='op7'), can_inplace=False, is_weak=False)]
]
op6.group.device = cuda:0
op6.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op6.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf4_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_47_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_5_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_5_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_5_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf5_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf6_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op6_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf3', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_47', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_5', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_5', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_5', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf5', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf6', get_index_8, to_dtype_1, None)
        return store


op7: ExternKernelSchedulerNode(ExternKernelOut)
op7.writes = [StarDep(name='buf7', mode=None)]
op7.unmet_dependencies = [StarDep(name='buf6', mode=None)]
op7.met_dependencies = [StarDep(name='permute_8', mode=None)]
op7.outputs = [
    buf7: ExternKernelOut
    buf7.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf7.aliases = ['buf33']
    buf7.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op7.node.kernel = extern_kernels.mm


op8: SchedulerNode(ComputedBuffer)
op8.writes = [MemoryDep('buf8', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op8.unmet_dependencies = []
op8.met_dependencies = []
op8.outputs = [
    buf8: ComputedBuffer
    buf8.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf8.users = [NodeUser(node=SchedulerNode(name='op9'), can_inplace=False, is_weak=False)]
]
op8.group.device = cuda:0
op8.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op8.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf8_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op8_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf8', get_index, constant, None)
        return store


op9: SchedulerNode(ComputedBuffer)
op9.writes = [MemoryDep('buf9', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op9.unmet_dependencies = [StarDep(name='buf8', mode=None)]
op9.met_dependencies = [MemoryDep('where_18', c0, {c0: ((s0*s1 + 6)//7)})]
op9.outputs = [
    buf9: ComputedBuffer
    buf9.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf9.mutations = ['buf8']
    buf9.users = [
        NodeUser(node=SchedulerNode(name='op10'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op11'), can_inplace=True, is_weak=False),
    ]
]
op9.group.device = cuda:0
op9.group.iteration = (((s0*s1 + 6)//7), 1)
op9.sizes = ([((s0*s1 + 6)//7)], [])
where_18_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf8_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf9_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op9_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_18', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf9', get_index_1, constant, None)
        return store


op10: SchedulerNode(ComputedBuffer)
op10.writes = [MemoryDep('buf10', c0, {c0: ((s4*s5 + 6)//7)})]
op10.unmet_dependencies = [MemoryDep('buf9', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op10.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_49', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op10.outputs = [
    buf10: ComputedBuffer
    buf10.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf10.users = [NodeUser(node=SchedulerNode(name='op11'), can_inplace=False, is_weak=False)]
]
op10.group.device = cuda:0
op10.group.iteration = (((s4*s5 + 6)//7), 201088)
op10.sizes = ([((s4*s5 + 6)//7)], [201088])
buf9_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op10_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf8', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_49', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf10', get_index_4, reduction)
        return store_reduction


op11: SchedulerNode(ComputedBuffer)
op11.writes = [MemoryDep('buf11', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op11.unmet_dependencies = 
    [   MemoryDep('buf10', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('buf9', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op11.met_dependencies = 
    [   MemoryDep('amax_4', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_4', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm_4', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_49', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op11.outputs = [
    buf11: ComputedBuffer
    buf11.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf11.users = [NodeUser(node=ExternKernelSchedulerNode(name='op12'), can_inplace=False, is_weak=False)]
]
op11.group.device = cuda:0
op11.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op11.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf9_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_49_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_4_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_4_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_4_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf10_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf11_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op11_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf8', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_49', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_4', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_4', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_4', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf10', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf11', get_index_8, to_dtype_1, None)
        return store


op12: ExternKernelSchedulerNode(ExternKernelOut)
op12.writes = [StarDep(name='buf12', mode=None)]
op12.unmet_dependencies = [StarDep(name='buf11', mode=None)]
op12.met_dependencies = [StarDep(name='permute_8', mode=None)]
op12.outputs = [
    buf12: ExternKernelOut
    buf12.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf12.aliases = ['buf33']
    buf12.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op12.node.kernel = extern_kernels.mm


op13: SchedulerNode(ComputedBuffer)
op13.writes = [MemoryDep('buf13', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op13.unmet_dependencies = []
op13.met_dependencies = []
op13.outputs = [
    buf13: ComputedBuffer
    buf13.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf13.users = [NodeUser(node=SchedulerNode(name='op14'), can_inplace=False, is_weak=False)]
]
op13.group.device = cuda:0
op13.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op13.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op13_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf13', get_index, constant, None)
        return store


op14: SchedulerNode(ComputedBuffer)
op14.writes = [MemoryDep('buf14', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op14.unmet_dependencies = [StarDep(name='buf13', mode=None)]
op14.met_dependencies = [MemoryDep('where_20', c0, {c0: ((s0*s1 + 6)//7)})]
op14.outputs = [
    buf14: ComputedBuffer
    buf14.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf14.mutations = ['buf13']
    buf14.users = [
        NodeUser(node=SchedulerNode(name='op15'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op16'), can_inplace=True, is_weak=False),
    ]
]
op14.group.device = cuda:0
op14.group.iteration = (((s0*s1 + 6)//7), 1)
op14.sizes = ([((s0*s1 + 6)//7)], [])
where_20_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf13_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf14_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op14_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_20', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf14', get_index_1, constant, None)
        return store


op15: SchedulerNode(ComputedBuffer)
op15.writes = [MemoryDep('buf15', c0, {c0: ((s4*s5 + 6)//7)})]
op15.unmet_dependencies = [MemoryDep('buf14', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op15.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_51', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op15.outputs = [
    buf15: ComputedBuffer
    buf15.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf15.users = [NodeUser(node=SchedulerNode(name='op16'), can_inplace=False, is_weak=False)]
]
op15.group.device = cuda:0
op15.group.iteration = (((s4*s5 + 6)//7), 201088)
op15.sizes = ([((s4*s5 + 6)//7)], [201088])
buf14_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_51_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op15_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf13', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_51', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf15', get_index_4, reduction)
        return store_reduction


op16: SchedulerNode(ComputedBuffer)
op16.writes = [MemoryDep('buf16', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op16.unmet_dependencies = 
    [   MemoryDep('buf14', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('buf15', c0, {c0: ((s4*s5 + 6)//7)})]
op16.met_dependencies = 
    [   MemoryDep('amax_3', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_3', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm_3', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_51', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op16.outputs = [
    buf16: ComputedBuffer
    buf16.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf16.users = [NodeUser(node=ExternKernelSchedulerNode(name='op17'), can_inplace=False, is_weak=False)]
]
op16.group.device = cuda:0
op16.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op16.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf14_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_51_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_3_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_3_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf15_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf16_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op16_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf13', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_51', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_3', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_3', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_3', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf15', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf16', get_index_8, to_dtype_1, None)
        return store


op17: ExternKernelSchedulerNode(ExternKernelOut)
op17.writes = [StarDep(name='buf17', mode=None)]
op17.unmet_dependencies = [StarDep(name='buf16', mode=None)]
op17.met_dependencies = [StarDep(name='permute_8', mode=None)]
op17.outputs = [
    buf17: ExternKernelOut
    buf17.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf17.aliases = ['buf33']
    buf17.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op17.node.kernel = extern_kernels.mm


op18: SchedulerNode(ComputedBuffer)
op18.writes = [MemoryDep('buf18', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op18.unmet_dependencies = []
op18.met_dependencies = []
op18.outputs = [
    buf18: ComputedBuffer
    buf18.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf18.users = [NodeUser(node=SchedulerNode(name='op19'), can_inplace=False, is_weak=False)]
]
op18.group.device = cuda:0
op18.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op18.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op18_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf18', get_index, constant, None)
        return store


op19: SchedulerNode(ComputedBuffer)
op19.writes = [MemoryDep('buf19', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op19.unmet_dependencies = [StarDep(name='buf18', mode=None)]
op19.met_dependencies = [MemoryDep('where_22', c0, {c0: ((s0*s1 + 6)//7)})]
op19.outputs = [
    buf19: ComputedBuffer
    buf19.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf19.mutations = ['buf18']
    buf19.users = [
        NodeUser(node=SchedulerNode(name='op20'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op21'), can_inplace=True, is_weak=False),
    ]
]
op19.group.device = cuda:0
op19.group.iteration = (((s0*s1 + 6)//7), 1)
op19.sizes = ([((s0*s1 + 6)//7)], [])
where_22_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf18_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf19_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op19_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_22', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf19', get_index_1, constant, None)
        return store


op20: SchedulerNode(ComputedBuffer)
op20.writes = [MemoryDep('buf20', c0, {c0: ((s4*s5 + 6)//7)})]
op20.unmet_dependencies = [MemoryDep('buf19', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op20.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_53', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op20.outputs = [
    buf20: ComputedBuffer
    buf20.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf20.users = [NodeUser(node=SchedulerNode(name='op21'), can_inplace=False, is_weak=False)]
]
op20.group.device = cuda:0
op20.group.iteration = (((s4*s5 + 6)//7), 201088)
op20.sizes = ([((s4*s5 + 6)//7)], [201088])
buf19_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op20_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf18', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_53', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf20', get_index_4, reduction)
        return store_reduction


op21: SchedulerNode(ComputedBuffer)
op21.writes = [MemoryDep('buf21', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op21.unmet_dependencies = 
    [   MemoryDep('buf19', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('buf20', c0, {c0: ((s4*s5 + 6)//7)})]
op21.met_dependencies = 
    [   MemoryDep('amax_2', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_2', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm_2', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_53', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op21.outputs = [
    buf21: ComputedBuffer
    buf21.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf21.users = [NodeUser(node=ExternKernelSchedulerNode(name='op22'), can_inplace=False, is_weak=False)]
]
op21.group.device = cuda:0
op21.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op21.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf19_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_53_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_2_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_2_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_2_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf20_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf21_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op21_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf18', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_53', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_2', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_2', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_2', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf20', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf21', get_index_8, to_dtype_1, None)
        return store


op22: ExternKernelSchedulerNode(ExternKernelOut)
op22.writes = [StarDep(name='buf22', mode=None)]
op22.unmet_dependencies = [StarDep(name='buf21', mode=None)]
op22.met_dependencies = [StarDep(name='permute_8', mode=None)]
op22.outputs = [
    buf22: ExternKernelOut
    buf22.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf22.aliases = ['buf33']
    buf22.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op22.node.kernel = extern_kernels.mm


op23: SchedulerNode(ComputedBuffer)
op23.writes = [MemoryDep('buf23', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op23.unmet_dependencies = []
op23.met_dependencies = []
op23.outputs = [
    buf23: ComputedBuffer
    buf23.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf23.users = [NodeUser(node=SchedulerNode(name='op24'), can_inplace=False, is_weak=False)]
]
op23.group.device = cuda:0
op23.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op23.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op23_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf23', get_index, constant, None)
        return store


op24: SchedulerNode(ComputedBuffer)
op24.writes = [MemoryDep('buf24', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op24.unmet_dependencies = [StarDep(name='buf23', mode=None)]
op24.met_dependencies = [MemoryDep('where_24', c0, {c0: ((s0*s1 + 6)//7)})]
op24.outputs = [
    buf24: ComputedBuffer
    buf24.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf24.mutations = ['buf23']
    buf24.users = [
        NodeUser(node=SchedulerNode(name='op25'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op26'), can_inplace=True, is_weak=False),
    ]
]
op24.group.device = cuda:0
op24.group.iteration = (((s0*s1 + 6)//7), 1)
op24.sizes = ([((s0*s1 + 6)//7)], [])
where_24_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf23_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf24_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op24_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_24', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf24', get_index_1, constant, None)
        return store


op25: SchedulerNode(ComputedBuffer)
op25.writes = [MemoryDep('buf25', c0, {c0: ((s4*s5 + 6)//7)})]
op25.unmet_dependencies = [MemoryDep('buf24', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op25.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_55', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op25.outputs = [
    buf25: ComputedBuffer
    buf25.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf25.users = [NodeUser(node=SchedulerNode(name='op26'), can_inplace=False, is_weak=False)]
]
op25.group.device = cuda:0
op25.group.iteration = (((s4*s5 + 6)//7), 201088)
op25.sizes = ([((s4*s5 + 6)//7)], [201088])
buf24_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op25_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf23', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_55', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf25', get_index_4, reduction)
        return store_reduction


op26: SchedulerNode(ComputedBuffer)
op26.writes = [MemoryDep('buf26', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op26.unmet_dependencies = 
    [   MemoryDep('buf24', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('buf25', c0, {c0: ((s4*s5 + 6)//7)})]
op26.met_dependencies = 
    [   MemoryDep('amax_1', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log_1', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm_1', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_55', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op26.outputs = [
    buf26: ComputedBuffer
    buf26.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf26.users = [NodeUser(node=ExternKernelSchedulerNode(name='op27'), can_inplace=False, is_weak=False)]
]
op26.group.device = cuda:0
op26.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op26.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf24_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_55_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_1_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_1_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_1_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf25_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf26_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op26_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf23', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_55', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm_1', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax_1', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log_1', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf25', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf26', get_index_8, to_dtype_1, None)
        return store


op27: ExternKernelSchedulerNode(ExternKernelOut)
op27.writes = [StarDep(name='buf27', mode=None)]
op27.unmet_dependencies = [StarDep(name='buf26', mode=None)]
op27.met_dependencies = [StarDep(name='permute_8', mode=None)]
op27.outputs = [
    buf27: ExternKernelOut
    buf27.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf27.aliases = ['buf33']
    buf27.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op27.node.kernel = extern_kernels.mm


op28: SchedulerNode(ComputedBuffer)
op28.writes = [MemoryDep('buf28', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op28.unmet_dependencies = []
op28.met_dependencies = []
op28.outputs = [
    buf28: ComputedBuffer
    buf28.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf28.users = [NodeUser(node=SchedulerNode(name='op29'), can_inplace=False, is_weak=False)]
]
op28.group.device = cuda:0
op28.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op28.sizes = ([201088*(((s4*s5 + 6)//7))], [])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op28_loop_body:
    var_ranges = {p0: 201088*(((s4*s5 + 6)//7))}
    index0 = p0
    def body(self, ops):
        constant = ops.constant(0.0, torch.float32)
        get_index = self.get_index('index0')
        store = ops.store('buf28', get_index, constant, None)
        return store


op29: SchedulerNode(ComputedBuffer)
op29.writes = [MemoryDep('buf29', 201088*c0 + tmp0, {c0: ((s0*s1 + 6)//7)})]
op29.unmet_dependencies = [StarDep(name='buf28', mode=None)]
op29.met_dependencies = [MemoryDep('where_26', c0, {c0: ((s0*s1 + 6)//7)})]
op29.outputs = [
    buf29: ComputedBuffer
    buf29.layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf29.mutations = ['buf28']
    buf29.users = [
        NodeUser(node=SchedulerNode(name='op30'), can_inplace=False, is_weak=False),
        NodeUser(node=SchedulerNode(name='op31'), can_inplace=True, is_weak=False),
    ]
]
op29.group.device = cuda:0
op29.group.iteration = (((s0*s1 + 6)//7), 1)
op29.sizes = ([((s0*s1 + 6)//7)], [])
where_26_layout = FixedLayout('cuda:0', torch.int64, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
buf28_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
buf29_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op29_loop_body:
    var_ranges = {p0: ((s0*s1 + 6)//7)}
    index0 = p0
    index1 = indirect0 + 201088*p0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('where_26', get_index)
        set_indirect0 = self.set_indirect0(load)
        constant = ops.constant(-1.0, torch.float32)
        get_index_1 = self.get_index('index1')
        store = ops.store('buf29', get_index_1, constant, None)
        return store


op30: SchedulerNode(ComputedBuffer)
op30.writes = [MemoryDep('buf30', c0, {c0: ((s4*s5 + 6)//7)})]
op30.unmet_dependencies = [MemoryDep('buf29', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op30.met_dependencies = 
    [   MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('ne_57', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op30.outputs = [
    buf30: ComputedBuffer
    buf30.layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
    buf30.users = [NodeUser(node=SchedulerNode(name='op31'), can_inplace=False, is_weak=False)]
]
op30.group.device = cuda:0
op30.group.iteration = (((s4*s5 + 6)//7), 201088)
op30.sizes = ([((s4*s5 + 6)//7)], [201088])
buf29_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_57_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
class op30_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf28', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_57', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        reduction = ops.reduction(torch.float32, torch.float32, 'sum', mul)
        get_index_4 = self.get_index('index1')
        store_reduction = ops.store_reduction('buf30', get_index_4, reduction)
        return store_reduction


op31: SchedulerNode(ComputedBuffer)
op31.writes = [MemoryDep('buf31', c0, {c0: 201088*(((s4*s5 + 6)//7))})]
op31.unmet_dependencies = 
    [   MemoryDep('buf29', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('buf30', c0, {c0: ((s4*s5 + 6)//7)})]
op31.met_dependencies = 
    [   MemoryDep('amax', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('convert_element_type_28', 0, {}),
        MemoryDep('log', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('mm', c0, {c0: 201088*(((s4*s5 + 6)//7))}),
        MemoryDep('ne_57', c0, {c0: ((s4*s5 + 6)//7)}),
        MemoryDep('tangents_1', 0, {})]
op31.outputs = [
    buf31: ComputedBuffer
    buf31.layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
    buf31.users = [NodeUser(node=ExternKernelSchedulerNode(name='op32'), can_inplace=False, is_weak=False)]
]
op31.group.device = cuda:0
op31.group.iteration = (201088*(((s4*s5 + 6)//7)), 1)
op31.sizes = ([((s4*s5 + 6)//7), 201088], [])
buf29_layout = MutationLayoutSHOULDREMOVE('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
ne_57_layout = FixedLayout('cuda:0', torch.bool, size=[((s0*s1 + 6)//7), 1], stride=[1, 1])
tangents_1_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
convert_element_type_28_layout = FixedLayout('cuda:0', torch.float32, size=[], stride=[])
mm_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
amax_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
log_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, 1])
buf30_layout = FixedLayout('cuda:0', torch.float32, size=[((s4*s5 + 6)//7), 1], stride=[1, ((s4*s5 + 6)//7)])
buf31_layout = FixedLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 201088], stride=[201088, 1])
class op31_loop_body:
    var_ranges = {p0: ((s4*s5 + 6)//7), p1: 201088}
    index0 = 201088*p0 + p1
    index1 = p0
    index2 = 0
    def body(self, ops):
        get_index = self.get_index('index0')
        load = ops.load('buf28', get_index)
        get_index_1 = self.get_index('index1')
        load_1 = ops.load('ne_57', get_index_1)
        get_index_2 = self.get_index('index2')
        load_2 = ops.load('tangents_1', get_index_2)
        get_index_3 = self.get_index('index2')
        load_3 = ops.load('convert_element_type_28', get_index_3)
        truediv = ops.truediv(load_2, load_3)
        constant = ops.constant(0.0, torch.float32)
        where = ops.where(load_1, truediv, constant)
        mul = ops.mul(load, where)
        get_index_4 = self.get_index('index0')
        load_4 = ops.load('mm', get_index_4)
        to_dtype = ops.to_dtype(load_4, torch.float32, src_dtype = torch.bfloat16)
        get_index_5 = self.get_index('index1')
        load_5 = ops.load('amax', get_index_5)
        sub = ops.sub(to_dtype, load_5)
        get_index_6 = self.get_index('index1')
        load_6 = ops.load('log', get_index_6)
        sub_1 = ops.sub(sub, load_6)
        exp = ops.exp(sub_1)
        get_index_7 = self.get_index('index1')
        load_7 = ops.load('buf30', get_index_7)
        mul_1 = ops.mul(exp, load_7)
        sub_2 = ops.sub(mul, mul_1)
        to_dtype_1 = ops.to_dtype(sub_2, torch.bfloat16, src_dtype = torch.float32)
        get_index_8 = self.get_index('index0')
        store = ops.store('buf31', get_index_8, to_dtype_1, None)
        return store


op32: ExternKernelSchedulerNode(ExternKernelOut)
op32.writes = [StarDep(name='buf32', mode=None)]
op32.unmet_dependencies = [StarDep(name='buf31', mode=None)]
op32.met_dependencies = [StarDep(name='permute_8', mode=None)]
op32.outputs = [
    buf32: ExternKernelOut
    buf32.layout = NonOwningLayout('cuda:0', torch.bfloat16, size=[((s4*s5 + 6)//7), 2880], stride=[2880, 1])
    buf32.aliases = ['buf33']
    buf32.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]
op32.node.kernel = extern_kernels.mm


op33: NopKernelSchedulerNode(ConcatKernel)
op33.writes = [StarDep(name='buf33', mode=None)]
op33.unmet_dependencies = 
    [   StarDep(name='buf12', mode=None),
        StarDep(name='buf17', mode=None),
        StarDep(name='buf2', mode=None),
        StarDep(name='buf22', mode=None),
        StarDep(name='buf27', mode=None),
        StarDep(name='buf32', mode=None),
        StarDep(name='buf7', mode=None)]
op33.met_dependencies = []
op33.outputs = [
    buf33: ConcatKernel
    buf33.layout = FixedLayout('cuda:0', torch.bfloat16, size=[s4*s5, 2880], stride=[2880, 1])
    buf33.users = [
        NodeUser(node=NopKernelSchedulerNode(name='op33'), can_inplace=False, is_weak=False),
        NodeUser(node=OUTPUT, can_inplace=False, is_weak=False),
    ]
]


