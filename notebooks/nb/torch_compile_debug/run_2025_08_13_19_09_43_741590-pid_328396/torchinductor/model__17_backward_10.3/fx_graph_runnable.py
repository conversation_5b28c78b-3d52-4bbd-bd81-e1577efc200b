
import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,roundup_power2_divisions:[32:256,64:128,256:64,>:32]'
os.environ['TRITON_DISABLE_LINE_INFO'] = '1'
os.environ['TRITON_FRONT_END_DEBUGGING'] = '0'
os.environ['TORCHINDUCTOR_FX_GRAPH_CACHE'] = '1'
os.environ['TORCHINDUCTOR_AUTOTUNE_REMOTE_CACHE'] = '1'
os.environ['TORCHINDUCTOR_CACHE_DIR'] = '/tmp/torchinductor_lile'
os.environ['TRITON_CACHE_DIR'] = '/tmp/torchinductor_lile/triton/0'

import torch
from torch import tensor, device
import torch.fx as fx
from torch._dynamo.testing import rand_strided
from math import inf
import torch._inductor.inductor_prims

import torch._dynamo.config
import torch._inductor.config
import torch._functorch.config
import torch.fx.experimental._config
torch._dynamo.config.verbose = False
torch._dynamo.config.recompile_limit = 16
torch._dynamo.config.accumulated_recompile_limit = 1024
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.capture_scalar_outputs = True
torch._dynamo.config.capture_dynamic_output_shape_ops = True
torch._dynamo.config.allow_unspec_int_on_nn_module = True
torch._dynamo.config.optimize_ddp = True
torch._dynamo.config.do_not_emit_runtime_asserts = True
torch._dynamo.config.numpy_default_float = 'float32'
torch._dynamo.config.inline_inbuilt_nn_modules = True
torch._dynamo.config.compiled_autograd = False
torch._inductor.config.debug = False
torch._inductor.config.disable_progress = True
torch._inductor.config.verbose_progress = False
torch._inductor.config.dce = True
torch._inductor.config.memory_planning = True
torch._inductor.config.memory_pool = 'none'
torch._inductor.config.epilogue_fusion = True
torch._inductor.config.efficient_conv_bn_eval_fx_passes = True
torch._inductor.config.group_fusion = False
torch._inductor.config.dynamic_scale_rblock = True
torch._inductor.config.max_autotune = False
torch._inductor.config.max_autotune_pointwise = False
torch._inductor.config.max_autotune_gemm = False
torch._inductor.config.max_autotune_gemm_backends = 'ATEN,TRITON,CPP'
torch._inductor.config.autotune_fallback_to_aten = True
torch._inductor.config.autotune_multi_device = True
torch._inductor.config.coordinate_descent_tuning = False
torch._inductor.config.aggressive_fusion = False
torch._inductor.config.combo_kernels = False
torch._inductor.config.benchmark_combo_kernel = False
torch._inductor.config.combo_kernel_foreach_dynamic_shapes = False
torch._inductor.config.emulate_precision_casts = False
torch._inductor.config.compile_threads = 24
torch._inductor.config.shape_padding = True
torch._inductor.config.freezing = False
torch._inductor.config.triton.cudagraphs = False
torch._inductor.config.triton.autotune_at_compile_time = None
torch._inductor.config.triton.cooperative_reductions = False
torch._inductor.config.triton.multi_kernel = 0
torch._inductor.config.triton.store_cubin = False
torch._inductor.config.triton.use_block_ptr = False
torch._inductor.config.triton.enable_persistent_tma_matmul = False
torch._inductor.config.cuda.compile_opt_level = '-O1'
torch._inductor.config.cuda.enable_cuda_lto = True
torch._inductor.config.cuda.use_fast_math = True
torch._inductor.config.trace.enabled = False
torch._inductor.config.trace.save_real_tensors = False
torch._inductor.config.trace.graph_diagram = False
torch._inductor.config.test_configs.runtime_triton_dtype_assert = False
torch._functorch.config.functionalize_rng_ops = False
torch._functorch.config.fake_tensor_allow_unsafe_data_ptr_access = True
torch._functorch.config.unlift_effect_tokens = True



isolate_fails_code_str = None




# torch version: 2.7.1+cu126
# torch cuda version: 12.6
# torch git version: e2d141dbde55c2a4370fac5165b0561b6af4798b


# CUDA Info: 
# nvcc: NVIDIA (R) Cuda compiler driver 
# Copyright (c) 2005-2024 NVIDIA Corporation 
# Built on Wed_Apr_17_19:19:55_PDT_2024 
# Cuda compilation tools, release 12.5, V12.5.40 
# Build cuda_12.5.r12.5/compiler.34177558_0 

# GPU Hardware Info: 
# NVIDIA H800 : 1 


from torch.nn import *
class Repro(torch.nn.Module):
    def __init__(self) -> None:
        super().__init__()

    
    
    def forward(self, primals_5, primals_6, primals_2, primals_3, floordiv, sym_size_int_6, mm, amax, log, mm_1, amax_1, log_1, mm_2, amax_2, log_2, mm_3, amax_3, log_3, mm_4, amax_4, log_4, mm_5, amax_5, log_5, mm_6, amax_6, log_6, convert_element_type_28, ne_45, where_14, permute_8, ne_47, where_16, ne_49, where_18, ne_51, where_20, ne_53, where_22, ne_55, where_24, ne_57, where_26, tangents_1):
        div_1 = torch.ops.aten.div.Tensor(tangents_1, convert_element_type_28);  tangents_1 = convert_element_type_28 = None
        full = torch.ops.aten.full.default([sym_size_int_6, 201088], 0, dtype = torch.float32, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False);  sym_size_int_6 = None
        scatter = torch.ops.aten.scatter.value(full, 1, where_14, -1.0);  full = where_14 = None
        full_default_2 = torch.ops.aten.full.default([], 0.0, dtype = torch.float32, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False)
        where_15 = torch.ops.aten.where.self(ne_45, div_1, full_default_2);  ne_45 = None
        mul_113 = torch.ops.aten.mul.Tensor(scatter, where_15);  scatter = where_15 = None
        convert_element_type_26 = torch.ops.prims.convert_element_type.default(mm_6, torch.float32);  mm_6 = None
        sub_71 = torch.ops.aten.sub.Tensor(convert_element_type_26, amax_6);  convert_element_type_26 = amax_6 = None
        sub_72 = torch.ops.aten.sub.Tensor(sub_71, log_6);  sub_71 = log_6 = None
        exp_7 = torch.ops.aten.exp.default(sub_72);  sub_72 = None
        sum_22 = torch.ops.aten.sum.dim_IntList(mul_113, [1], True)
        mul_114 = torch.ops.aten.mul.Tensor(exp_7, sum_22);  exp_7 = sum_22 = None
        sub_75 = torch.ops.aten.sub.Tensor(mul_113, mul_114);  mul_113 = mul_114 = None
        convert_element_type_29 = torch.ops.prims.convert_element_type.default(sub_75, torch.bfloat16);  sub_75 = None
        mm_7 = torch.ops.aten.mm.default(convert_element_type_29, permute_8);  convert_element_type_29 = None
        full_1 = torch.ops.aten.full.default([floordiv, 201088], 0, dtype = torch.float32, layout = torch.strided, device = device(type='cuda', index=0), pin_memory = False);  floordiv = None
        scatter_1 = torch.ops.aten.scatter.value(full_1, 1, where_16, -1.0);  where_16 = None
        where_17 = torch.ops.aten.where.self(ne_47, div_1, full_default_2);  ne_47 = None
        mul_115 = torch.ops.aten.mul.Tensor(scatter_1, where_17);  scatter_1 = where_17 = None
        convert_element_type_22 = torch.ops.prims.convert_element_type.default(mm_5, torch.float32);  mm_5 = None
        sub_65 = torch.ops.aten.sub.Tensor(convert_element_type_22, amax_5);  convert_element_type_22 = amax_5 = None
        sub_66 = torch.ops.aten.sub.Tensor(sub_65, log_5);  sub_65 = log_5 = None
        exp_8 = torch.ops.aten.exp.default(sub_66);  sub_66 = None
        sum_23 = torch.ops.aten.sum.dim_IntList(mul_115, [1], True)
        mul_116 = torch.ops.aten.mul.Tensor(exp_8, sum_23);  exp_8 = sum_23 = None
        sub_76 = torch.ops.aten.sub.Tensor(mul_115, mul_116);  mul_115 = mul_116 = None
        convert_element_type_32 = torch.ops.prims.convert_element_type.default(sub_76, torch.bfloat16);  sub_76 = None
        mm_8 = torch.ops.aten.mm.default(convert_element_type_32, permute_8);  convert_element_type_32 = None
        scatter_2 = torch.ops.aten.scatter.value(full_1, 1, where_18, -1.0);  where_18 = None
        where_19 = torch.ops.aten.where.self(ne_49, div_1, full_default_2);  ne_49 = None
        mul_117 = torch.ops.aten.mul.Tensor(scatter_2, where_19);  scatter_2 = where_19 = None
        convert_element_type_18 = torch.ops.prims.convert_element_type.default(mm_4, torch.float32);  mm_4 = None
        sub_59 = torch.ops.aten.sub.Tensor(convert_element_type_18, amax_4);  convert_element_type_18 = amax_4 = None
        sub_60 = torch.ops.aten.sub.Tensor(sub_59, log_4);  sub_59 = log_4 = None
        exp_9 = torch.ops.aten.exp.default(sub_60);  sub_60 = None
        sum_24 = torch.ops.aten.sum.dim_IntList(mul_117, [1], True)
        mul_118 = torch.ops.aten.mul.Tensor(exp_9, sum_24);  exp_9 = sum_24 = None
        sub_77 = torch.ops.aten.sub.Tensor(mul_117, mul_118);  mul_117 = mul_118 = None
        convert_element_type_35 = torch.ops.prims.convert_element_type.default(sub_77, torch.bfloat16);  sub_77 = None
        mm_9 = torch.ops.aten.mm.default(convert_element_type_35, permute_8);  convert_element_type_35 = None
        scatter_3 = torch.ops.aten.scatter.value(full_1, 1, where_20, -1.0);  where_20 = None
        where_21 = torch.ops.aten.where.self(ne_51, div_1, full_default_2);  ne_51 = None
        mul_119 = torch.ops.aten.mul.Tensor(scatter_3, where_21);  scatter_3 = where_21 = None
        convert_element_type_14 = torch.ops.prims.convert_element_type.default(mm_3, torch.float32);  mm_3 = None
        sub_53 = torch.ops.aten.sub.Tensor(convert_element_type_14, amax_3);  convert_element_type_14 = amax_3 = None
        sub_54 = torch.ops.aten.sub.Tensor(sub_53, log_3);  sub_53 = log_3 = None
        exp_10 = torch.ops.aten.exp.default(sub_54);  sub_54 = None
        sum_25 = torch.ops.aten.sum.dim_IntList(mul_119, [1], True)
        mul_120 = torch.ops.aten.mul.Tensor(exp_10, sum_25);  exp_10 = sum_25 = None
        sub_78 = torch.ops.aten.sub.Tensor(mul_119, mul_120);  mul_119 = mul_120 = None
        convert_element_type_38 = torch.ops.prims.convert_element_type.default(sub_78, torch.bfloat16);  sub_78 = None
        mm_10 = torch.ops.aten.mm.default(convert_element_type_38, permute_8);  convert_element_type_38 = None
        scatter_4 = torch.ops.aten.scatter.value(full_1, 1, where_22, -1.0);  where_22 = None
        where_23 = torch.ops.aten.where.self(ne_53, div_1, full_default_2);  ne_53 = None
        mul_121 = torch.ops.aten.mul.Tensor(scatter_4, where_23);  scatter_4 = where_23 = None
        convert_element_type_10 = torch.ops.prims.convert_element_type.default(mm_2, torch.float32);  mm_2 = None
        sub_47 = torch.ops.aten.sub.Tensor(convert_element_type_10, amax_2);  convert_element_type_10 = amax_2 = None
        sub_48 = torch.ops.aten.sub.Tensor(sub_47, log_2);  sub_47 = log_2 = None
        exp_11 = torch.ops.aten.exp.default(sub_48);  sub_48 = None
        sum_26 = torch.ops.aten.sum.dim_IntList(mul_121, [1], True)
        mul_122 = torch.ops.aten.mul.Tensor(exp_11, sum_26);  exp_11 = sum_26 = None
        sub_79 = torch.ops.aten.sub.Tensor(mul_121, mul_122);  mul_121 = mul_122 = None
        convert_element_type_41 = torch.ops.prims.convert_element_type.default(sub_79, torch.bfloat16);  sub_79 = None
        mm_11 = torch.ops.aten.mm.default(convert_element_type_41, permute_8);  convert_element_type_41 = None
        scatter_5 = torch.ops.aten.scatter.value(full_1, 1, where_24, -1.0);  where_24 = None
        where_25 = torch.ops.aten.where.self(ne_55, div_1, full_default_2);  ne_55 = None
        mul_123 = torch.ops.aten.mul.Tensor(scatter_5, where_25);  scatter_5 = where_25 = None
        convert_element_type_6 = torch.ops.prims.convert_element_type.default(mm_1, torch.float32);  mm_1 = None
        sub_41 = torch.ops.aten.sub.Tensor(convert_element_type_6, amax_1);  convert_element_type_6 = amax_1 = None
        sub_42 = torch.ops.aten.sub.Tensor(sub_41, log_1);  sub_41 = log_1 = None
        exp_12 = torch.ops.aten.exp.default(sub_42);  sub_42 = None
        sum_27 = torch.ops.aten.sum.dim_IntList(mul_123, [1], True)
        mul_124 = torch.ops.aten.mul.Tensor(exp_12, sum_27);  exp_12 = sum_27 = None
        sub_80 = torch.ops.aten.sub.Tensor(mul_123, mul_124);  mul_123 = mul_124 = None
        convert_element_type_44 = torch.ops.prims.convert_element_type.default(sub_80, torch.bfloat16);  sub_80 = None
        mm_12 = torch.ops.aten.mm.default(convert_element_type_44, permute_8);  convert_element_type_44 = None
        scatter_6 = torch.ops.aten.scatter.value(full_1, 1, where_26, -1.0);  full_1 = where_26 = None
        where_27 = torch.ops.aten.where.self(ne_57, div_1, full_default_2);  ne_57 = div_1 = full_default_2 = None
        mul_125 = torch.ops.aten.mul.Tensor(scatter_6, where_27);  scatter_6 = where_27 = None
        convert_element_type_2 = torch.ops.prims.convert_element_type.default(mm, torch.float32);  mm = None
        sub_35 = torch.ops.aten.sub.Tensor(convert_element_type_2, amax);  convert_element_type_2 = amax = None
        sub_36 = torch.ops.aten.sub.Tensor(sub_35, log);  sub_35 = log = None
        exp_13 = torch.ops.aten.exp.default(sub_36);  sub_36 = None
        sum_28 = torch.ops.aten.sum.dim_IntList(mul_125, [1], True)
        mul_126 = torch.ops.aten.mul.Tensor(exp_13, sum_28);  exp_13 = sum_28 = None
        sub_81 = torch.ops.aten.sub.Tensor(mul_125, mul_126);  mul_125 = mul_126 = None
        convert_element_type_47 = torch.ops.prims.convert_element_type.default(sub_81, torch.bfloat16);  sub_81 = None
        mm_13 = torch.ops.aten.mm.default(convert_element_type_47, permute_8);  convert_element_type_47 = permute_8 = None
        cat = torch.ops.aten.cat.default([mm_13, mm_12, mm_11, mm_10, mm_9, mm_8, mm_7]);  mm_13 = mm_12 = mm_11 = mm_10 = mm_9 = mm_8 = mm_7 = None
        view_9 = torch.ops.aten.view.default(cat, [primals_5, primals_6, 2880]);  cat = primals_5 = primals_6 = None
        return (None, None, None, None, None, None, view_9, None)
        
def load_args(reader):
    reader.symint(2)  # primals_5
    reader.symint(1359)  # primals_6
    reader.symint(2)  # primals_2
    reader.symint(1359)  # primals_3
    reader.symint(389)  # floordiv
    reader.symint(384)  # sym_size_int_6
    buf0 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf0, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm
    buf1 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf1, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax
    buf2 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf2, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log
    buf3 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf3, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_1
    buf4 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf4, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax_1
    buf5 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf5, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log_1
    buf6 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf6, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_2
    buf7 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf7, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax_2
    buf8 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf8, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log_2
    buf9 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf9, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_3
    buf10 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf10, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax_3
    buf11 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf11, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log_3
    buf12 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf12, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_4
    buf13 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf13, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax_4
    buf14 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf14, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log_4
    buf15 = reader.storage(None, 402176*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf15, (((s4*s5 + 6)//7), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_5
    buf16 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf16, (((s4*s5 + 6)//7), 1), is_leaf=True)  # amax_5
    buf17 = reader.storage(None, 4*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf17, (((s4*s5 + 6)//7), 1), is_leaf=True)  # log_5
    buf18 = reader.storage(None, 402176*s4*s5 - 2413056*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf18, (s4*s5 - 6*(((s4*s5 + 6)//7)), 201088), dtype=torch.bfloat16, is_leaf=True)  # mm_6
    buf19 = reader.storage(None, 4*s4*s5 - 24*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf19, (s4*s5 - 6*(((s4*s5 + 6)//7)), 1), is_leaf=True)  # amax_6
    buf20 = reader.storage(None, 4*s4*s5 - 24*(((s4*s5 + 6)//7)), device=device(type='cuda', index=0))
    reader.tensor(buf20, (s4*s5 - 6*(((s4*s5 + 6)//7)), 1), is_leaf=True)  # log_6
    buf21 = reader.storage(None, 4, device=device(type='cuda', index=0))
    reader.tensor(buf21, (), is_leaf=True)  # convert_element_type_28
    buf22 = reader.storage(None, s0*s1 - 6*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf22, (s0*s1 - 6*(((s0*s1 + 6)//7)), 1), dtype=torch.bool, is_leaf=True)  # ne_45
    buf23 = reader.storage(None, 8*s0*s1 - 48*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf23, (s0*s1 - 6*(((s0*s1 + 6)//7)), 1), dtype=torch.int64, is_leaf=True)  # where_14
    buf24 = reader.storage(None, 1158266880, device=device(type='cuda', index=0), dtype_hint=torch.bfloat16)
    reader.tensor(buf24, (201088, 2880), dtype=torch.bfloat16, is_leaf=True)  # permute_8
    buf25 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf25, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_47
    buf26 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf26, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_16
    buf27 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf27, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_49
    buf28 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf28, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_18
    buf29 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf29, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_51
    buf30 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf30, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_20
    buf31 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf31, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_53
    buf32 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf32, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_22
    buf33 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf33, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_55
    buf34 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf34, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_24
    buf35 = reader.storage(None, ((s0*s1 + 6)//7), device=device(type='cuda', index=0), dtype_hint=torch.bool)
    reader.tensor(buf35, (((s0*s1 + 6)//7), 1), dtype=torch.bool, is_leaf=True)  # ne_57
    buf36 = reader.storage(None, 8*(((s0*s1 + 6)//7)), device=device(type='cuda', index=0), dtype_hint=torch.int64)
    reader.tensor(buf36, (((s0*s1 + 6)//7), 1), dtype=torch.int64, is_leaf=True)  # where_26
    buf37 = reader.storage(None, 4, device=device(type='cuda', index=0))
    reader.tensor(buf37, (), is_leaf=True)  # tangents_1
load_args._version = 0
mod = Repro()
if __name__ == '__main__':
    from torch._dynamo.repro.after_aot import run_repro
    with torch.no_grad():
        run_repro(mod, load_args, accuracy=False, command='run', save_dir=None, tracing_mode='symbolic', check_str=None)
        # To run it separately, do 
        # mod, args = run_repro(mod, load_args, accuracy=False, command='get_args', save_dir=None, tracing_mode='symbolic', check_str=None)
        # mod(*args)