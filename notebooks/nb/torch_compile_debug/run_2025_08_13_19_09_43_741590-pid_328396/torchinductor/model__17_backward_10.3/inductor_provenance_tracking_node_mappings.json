{"preToPost": {"cross_entropy_loss": ["full_default_2", "sub_35", "sub_36"], "float_7": ["convert_element_type_26"], "cross_entropy_loss_6": ["sub_71", "sub_72"], "float_6": ["convert_element_type_22"], "cross_entropy_loss_5": ["sub_65", "sub_66"], "float_5": ["convert_element_type_18"], "cross_entropy_loss_4": ["sub_59", "sub_60"], "float_4": ["convert_element_type_14"], "cross_entropy_loss_3": ["sub_53", "sub_54"], "float_3": ["convert_element_type_10"], "cross_entropy_loss_2": ["sub_47", "sub_48"], "float_2": ["convert_element_type_6"], "cross_entropy_loss_1": ["sub_41", "sub_42"], "float_1": ["convert_element_type_2"]}, "postToPre": {"full_default_2": ["cross_entropy_loss"], "convert_element_type_26": ["float_7"], "sub_71": ["cross_entropy_loss_6"], "sub_72": ["cross_entropy_loss_6"], "convert_element_type_22": ["float_6"], "sub_65": ["cross_entropy_loss_5"], "sub_66": ["cross_entropy_loss_5"], "convert_element_type_18": ["float_5"], "sub_59": ["cross_entropy_loss_4"], "sub_60": ["cross_entropy_loss_4"], "convert_element_type_14": ["float_4"], "sub_53": ["cross_entropy_loss_3"], "sub_54": ["cross_entropy_loss_3"], "convert_element_type_10": ["float_3"], "sub_47": ["cross_entropy_loss_2"], "sub_48": ["cross_entropy_loss_2"], "convert_element_type_6": ["float_2"], "sub_41": ["cross_entropy_loss_1"], "sub_42": ["cross_entropy_loss_1"], "convert_element_type_2": ["float_1"], "sub_35": ["cross_entropy_loss"], "sub_36": ["cross_entropy_loss"]}, "cppCodeToPost": {"triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0": ["log_6"], "triton_red_fused_nll_loss_backward_nll_loss_forward_1": ["where_26", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_2": ["where_24", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_3": ["where_22", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_4": ["where_20", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_5": ["where_18", "full_default_1"], "triton_red_fused_nll_loss_backward_nll_loss_forward_6": ["where_16", "full_default_1"], "triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7": ["div", "add_156", "add_143", "add_130", "add_117", "add_104", "add_91", "add_78"], "triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0": ["convert_element_type_29", "sub_75", "mul_113", "scatter_upon_const_tensor", "where_15", "div_1", "full_default_2", "mul_114", "exp_7", "sub_72", "sub_71", "convert_element_type_26"], "triton_poi_fused_nll_loss_backward_1": ["scatter_6", "full_1"], "triton_poi_fused_nll_loss_backward_2": ["scatter_6", "full_1"], "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3": ["sum_28", "mul_125", "where_27", "div_1", "full_default_2"], "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4": ["convert_element_type_47", "sub_81", "mul_125", "where_27", "div_1", "full_default_2", "mul_126", "exp_13", "sub_36", "sub_35", "convert_element_type_2"]}, "postToCppCode": {"log_6": ["triton_red_fused__log_softmax__to_copy_prepare_softmax_online_0"], "where_26": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1"], "full_default_1": ["triton_red_fused_nll_loss_backward_nll_loss_forward_1", "triton_red_fused_nll_loss_backward_nll_loss_forward_2", "triton_red_fused_nll_loss_backward_nll_loss_forward_3", "triton_red_fused_nll_loss_backward_nll_loss_forward_4", "triton_red_fused_nll_loss_backward_nll_loss_forward_5", "triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "where_24": ["triton_red_fused_nll_loss_backward_nll_loss_forward_2"], "where_22": ["triton_red_fused_nll_loss_backward_nll_loss_forward_3"], "where_20": ["triton_red_fused_nll_loss_backward_nll_loss_forward_4"], "where_18": ["triton_red_fused_nll_loss_backward_nll_loss_forward_5"], "where_16": ["triton_red_fused_nll_loss_backward_nll_loss_forward_6"], "div": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_156": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_143": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_130": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_117": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_104": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_91": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "add_78": ["triton_red_fused__to_copy_add_div_nll_loss_backward_nll_loss_forward_7"], "convert_element_type_29": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_75": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "mul_113": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_upon_const_tensor": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "where_15": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "div_1": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "full_default_2": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0", "triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "mul_114": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "exp_7": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_72": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "sub_71": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "convert_element_type_26": ["triton_red_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_0"], "scatter_6": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "full_1": ["triton_poi_fused_nll_loss_backward_1", "triton_poi_fused_nll_loss_backward_2"], "sum_28": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3"], "mul_125": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "where_27": ["triton_red_fused__log_softmax_backward_data_div_nll_loss_backward_nll_loss_forward_3", "triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "convert_element_type_47": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_81": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "mul_126": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "exp_13": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_36": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "sub_35": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"], "convert_element_type_2": ["triton_poi_fused__log_softmax__log_softmax_backward_data__to_copy_div_nll_loss_backward_nll_loss_forward_4"]}}