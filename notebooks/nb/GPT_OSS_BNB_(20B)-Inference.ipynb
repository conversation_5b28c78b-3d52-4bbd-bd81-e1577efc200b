{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "# We're installing the latest Torch, Triton, OpenAI's Triton kernels, Transformers and Unsloth!\n", "!pip install --upgrade -qqq uv\n", "try: import numpy; install_numpy = f\"numpy=={numpy.__version__}\"\n", "except: install_numpy = \"numpy\"\n", "!uv pip install -qqq \\\n", "    \"torch>=2.8.0\" \"triton>=3.4.0\" {install_numpy} \\\n", "    \"unsloth_zoo[base] @ git+https://github.com/unslothai/unsloth-zoo\" \\\n", "    \"unsloth[base] @ git+https://github.com/unslothai/unsloth\" \\\n", "    torchvision bitsandbytes \\\n", "    git+https://github.com/huggingface/transformers \\\n", "    git+https://github.com/triton-lang/triton.git@main#subdirectory=python/triton_kernels\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "!uv pip install --force-reinstall --no-deps git+https://github.com/unslothai/unsloth-zoo\n", "!uv pip install --force-reinstall --no-deps git+https://github.com/unslothai/unsloth"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['https_proxy']=\"http://***************:39995\"\n", "os.environ['http_proxy']=\"http://***************:39995\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "r2v_X2fA0Df5"}, "source": ["We're about to demonstrate the power of the new OpenAI GPT-OSS 20B model through an inference example. For our `MXFP4` version, use this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/GPT_OSS_MXFP4_(20B)-Inference.ipynb) instead."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 371, "referenced_widgets": ["7ca2facea2414549ab2a0a3fd07a0723", "78676eb3a75f45ed860b488a42fb9bc5", "b69b75dd3b5745f4a7f869f7ba6593d4", "ea10da0d677849c28bfdfc9206ac1b73", "493f6a1af5e94b5eb88a4935361a88c3", "1a4ef35565f54388afdf1839055eb5c8", "07ae7e9f6861403ea8064a98f4063754", "fc2ce36bb32f4c648ccb06d65a4e2aea", "bf44ec9413b24dda932f575ebe32b222", "2ad28d8e556b4e17b0859aa3683d31c6", "e32220b1d3f14386834bcd3736ac9888", "d9c5466214ed4f06876a89c64c29048f", "1e0c170f075b43ad9e2d39fcdbaa0a06", "00abd0bd091f4258b7f61d80ecb3118e", "0f2c8641a38043d2a218ec0809e7a7d3", "948972fa1a2e400097b590ad47ff6f3d", "47a13a9b02ad4181b74cc1f5ddeb3f3f", "7c01111484b14a5999d48393085b0264", "fb1a288281fc446788fdd0f432b4732c", "2f026e4c1ff34aafbf7c5deff6c1e66f", "acc52213c033476885ee30fb92f55e23", "9caec4d2cb6e4a72b7155912787d3139", "2abdab3fdd4f4a70a8faaa7b0e39f811", "4d432e4349964027bae76c34f5734fcf", "630c170c04494811b21992d6886a3015", "cb9a92b76a1d4b189e7ed783bde524c9", "ccc49354213c4c1394855986a0e07483", "7bf239e457f04e15b4b0e458f0dd1e19", "f5c6558ef929438eb117159de0285a58", "447aa145c09c4c6f8bd9ce3ea8b5cd44", "9c82690bd07346d1ab875bdf5ed80154", "5200dae3ba2346c1bca27530b82b38b4", "c7a3b0cba7ee49ba929e0038d9c03e98", "36250ac65fcb4756a4368f98902a1617", "705a1673de934cce8418f7893fbf780a", "1f7ed3fc3a824e119cf559af8bc7c370", "cafc86be3bd94bc79cb07bc94461ec5d", "0c190d903a38402ab452de0a0100a8eb", "b3362bda7942432692049558d0cdf14e", "4afb54f2995447e68ae3a8129325ffdc", "31c7337bfd504409992b7e04b713c243", "9b5a45647bd74e8c878cf6ba43b27190", "b9c9010af82b4678af43a20546f3ad49", "9b818c8e8bac40edb97abf8a266ab28e", "ebaa8d22074d495ea675f51dc2d5a4d6", "8fecfa968d55466ba8f42730d32bd9b4", "dd0e8e4cb46945df8dbec4fefd720358", "60a80b3316c14ce79945e06cb190be39", "4f0e06a2e2204dad9f29a1697edd4218", "66527058da914c73b48221011a2b605a", "58413234f68b484693f0719166942214", "84b3814386ee4d04b9817f0da2108725", "01ede422dc5f4d4da7f913880db4984d", "2e9f87f895344785855cca39bb64d5e2", "5512e17f3864445a875e640937f296ef", "932920c0ee834e3e8ffe53ebf7833fa7", "cb65c93ecef84c2c947c67481326691c", "73c3f04df8044f34ab45b92c174e121e", "13b8be4a5af14bfdbbce24d185187906", "21d8ebf7cbfd4e2b9db111d163aad8b8", "3122a97298124d0ab0e6297960c132e2", "cc829a40832849f586b077f6d8e5795b", "3f5a8b6d049244959c1558c56483f643", "11fea1d7e4a8426d98165fa5ca7fc650", "fc3715b03c5249438965886892ba07bb", "05d6f6c3efed4ecf9893c8aa86fe4176"]}, "id": "QmUBVEnvCDJv", "outputId": "14ca09b9-e1ff-4f91-b98c-7a1ed22eef3a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.8.4: Fast Gpt_Oss patching. Transformers: 4.55.0.\n", "   \\\\   /|    NVIDIA H800. Num GPUs = 1. Max memory: 79.097 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 9.0. CUDA Toolkit: 12.6. Triton: 3.3.1\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.31.post1. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: QLoRA and full finetuning all not selected. Switching to 16bit LoRA.\n"]}, {"ename": "ValueError", "evalue": "Some modules are dispatched on the CPU or the disk. Make sure you have enough GPU RAM to fit the quantized model. If you want to dispatch the model on the CPU or the disk while keeping these modules in 32-bit, you need to set `llm_int8_enable_fp32_cpu_offload=True` and pass a custom `device_map` to `from_pretrained`. Check https://huggingface.co/docs/transformers/main/en/main_classes/quantization#offload-between-cpu-and-gpu for more details. ", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 12\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\u001b[39;00m\n\u001b[32m      5\u001b[39m fourbit_models = [\n\u001b[32m      6\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33munsloth/gpt-oss-20b-unsloth-bnb-4bit\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;66;03m# 20B model using bitsandbytes 4bit quantization\u001b[39;00m\n\u001b[32m      7\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33munsloth/gpt-oss-120b-unsloth-bnb-4bit\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      8\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33munsloth/gpt-oss-20b\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;66;03m# 20B model using MXFP4 format\u001b[39;00m\n\u001b[32m      9\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33munsloth/gpt-oss-120b\u001b[39m\u001b[33m\"\u001b[39m, \n\u001b[32m     10\u001b[39m ] \u001b[38;5;66;03m# More models at https://huggingface.co/unsloth\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m model, tokenizer = \u001b[43mFastLanguageModel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     13\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/mnt/vdb1/model/gpt-oss-20b-unsloth-bnb-4bit\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m#unsloth/gpt-oss-20b-unsloth-bnb-4bit\",\u001b[39;49;00m\n\u001b[32m     14\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# None for auto detection\u001b[39;49;00m\n\u001b[32m     15\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_seq_length\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m4096\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# Choose any for long context!\u001b[39;49;00m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[43mload_in_4bit\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# 4 bit quantization to reduce memory\u001b[39;49;00m\n\u001b[32m     17\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfull_finetuning\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# [NEW!] We have full finetuning now!\u001b[39;49;00m\n\u001b[32m     18\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# token = \"hf_...\", # use one if using gated models\u001b[39;49;00m\n\u001b[32m     19\u001b[39m \u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth/models/loader.py:340\u001b[39m, in \u001b[36mFastLanguageModel.from_pretrained\u001b[39m\u001b[34m(model_name, max_seq_length, dtype, load_in_4bit, load_in_8bit, full_finetuning, token, device_map, rope_scaling, fix_tokenizer, trust_remote_code, use_gradient_checkpointing, resize_model_vocab, revision, use_exact_model_name, fast_inference, gpu_memory_utilization, float8_kv_cache, random_state, max_lora_rank, disable_log_stats, *args, **kwargs)\u001b[39m\n\u001b[32m    323\u001b[39m     dispatch_model = FastQwen3Model \u001b[38;5;28;01mif\u001b[39;00m model_type == \u001b[33m\"\u001b[39m\u001b[33mqwen3\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m FastQwen3MoeModel\n\u001b[32m    324\u001b[39m \u001b[38;5;66;03m# elif model_type == \"falcon_h1\":\u001b[39;00m\n\u001b[32m    325\u001b[39m \u001b[38;5;66;03m#     dispatch_model = FastFalconH1Model\u001b[39;00m\n\u001b[32m    326\u001b[39m \u001b[38;5;66;03m#     if not SUPPORTS_FALCON_H1:\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    338\u001b[39m \u001b[38;5;66;03m#     dispatch_model = FastGraniteModel\u001b[39;00m\n\u001b[32m    339\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m340\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mFastModel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    341\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m                 \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    342\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmax_seq_length\u001b[49m\u001b[43m             \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_seq_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    343\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m                      \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    344\u001b[39m \u001b[43m        \u001b[49m\u001b[43mload_in_4bit\u001b[49m\u001b[43m               \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mload_in_4bit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    345\u001b[39m \u001b[43m        \u001b[49m\u001b[43mload_in_8bit\u001b[49m\u001b[43m               \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mload_in_8bit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    346\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfull_finetuning\u001b[49m\u001b[43m            \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mfull_finetuning\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    347\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m                      \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    348\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m                 \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    349\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrope_scaling\u001b[49m\u001b[43m               \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mrope_scaling\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# [TODO] No effect\u001b[39;49;00m\n\u001b[32m    350\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfix_tokenizer\u001b[49m\u001b[43m              \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mfix_tokenizer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# [TODO] No effect\u001b[39;49;00m\n\u001b[32m    351\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m          \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    352\u001b[39m \u001b[43m        \u001b[49m\u001b[43muse_gradient_checkpointing\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43muse_gradient_checkpointing\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    353\u001b[39m \u001b[43m        \u001b[49m\u001b[43mresize_model_vocab\u001b[49m\u001b[43m         \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mresize_model_vocab\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# [TODO] No effect\u001b[39;49;00m\n\u001b[32m    354\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m                   \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    355\u001b[39m \u001b[43m        \u001b[49m\u001b[43mreturn_logits\u001b[49m\u001b[43m              \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# Return logits\u001b[39;49;00m\n\u001b[32m    356\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfullgraph\u001b[49m\u001b[43m                  \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;66;43;03m# No graph breaks\u001b[39;49;00m\n\u001b[32m    357\u001b[39m \u001b[43m        \u001b[49m\u001b[43muse_exact_model_name\u001b[49m\u001b[43m       \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43muse_exact_model_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    358\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    359\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    360\u001b[39m \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m    362\u001b[39m \u001b[38;5;66;03m# Check if this is local model since the tokenizer gets overwritten\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth/models/loader.py:812\u001b[39m, in \u001b[36mFastModel.from_pretrained\u001b[39m\u001b[34m(model_name, max_seq_length, dtype, load_in_4bit, load_in_8bit, full_finetuning, token, device_map, rope_scaling, fix_tokenizer, trust_remote_code, use_gradient_checkpointing, resize_model_vocab, revision, return_logits, fullgraph, use_exact_model_name, auto_model, whisper_language, whisper_task, unsloth_force_compile, *args, **kwargs)\u001b[39m\n\u001b[32m    809\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m auto_model \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    810\u001b[39m     auto_model = AutoModelForVision2Seq \u001b[38;5;28;01mif\u001b[39;00m is_vlm \u001b[38;5;28;01melse\u001b[39;00m AutoModelForCausalLM\n\u001b[32m--> \u001b[39m\u001b[32m812\u001b[39m model, tokenizer = \u001b[43mFastBaseModel\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    813\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m        \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    814\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmax_seq_length\u001b[49m\u001b[43m    \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_seq_length\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    815\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m             \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43m_get_dtype\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    816\u001b[39m \u001b[43m    \u001b[49m\u001b[43mload_in_4bit\u001b[49m\u001b[43m      \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mload_in_4bit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    817\u001b[39m \u001b[43m    \u001b[49m\u001b[43mload_in_8bit\u001b[49m\u001b[43m      \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mload_in_8bit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    818\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfull_finetuning\u001b[49m\u001b[43m   \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mfull_finetuning\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    819\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m             \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    820\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m        \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    821\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    822\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m          \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mis_peft\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    823\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel_types\u001b[49m\u001b[43m       \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel_types\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    824\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtokenizer_name\u001b[49m\u001b[43m    \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtokenizer_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    825\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauto_model\u001b[49m\u001b[43m        \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mauto_model\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    826\u001b[39m \u001b[43m    \u001b[49m\u001b[43muse_gradient_checkpointing\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43muse_gradient_checkpointing\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    827\u001b[39m \u001b[43m    \u001b[49m\u001b[43msupports_sdpa\u001b[49m\u001b[43m     \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43msupports_sdpa\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    828\u001b[39m \u001b[43m    \u001b[49m\u001b[43mwhisper_language\u001b[49m\u001b[43m  \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhisper_language\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    829\u001b[39m \u001b[43m    \u001b[49m\u001b[43mwhisper_task\u001b[49m\u001b[43m      \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhisper_task\u001b[49m\u001b[43m,\u001b[49m\u001b[43m            \u001b[49m\n\u001b[32m    830\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    831\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    833\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m resize_model_vocab \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    834\u001b[39m     model.resize_token_embeddings(resize_model_vocab)\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/unsloth/models/vision.py:444\u001b[39m, in \u001b[36mFastBaseModel.from_pretrained\u001b[39m\u001b[34m(model_name, max_seq_length, dtype, load_in_4bit, load_in_8bit, full_finetuning, token, device_map, trust_remote_code, model_types, tokenizer_name, auto_model, use_gradient_checkpointing, supports_sdpa, whisper_language, whisper_task, **kwargs)\u001b[39m\n\u001b[32m    441\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m do_forced_float32: torch_dtype = torch.bfloat16\n\u001b[32m    443\u001b[39m raise_handler = RaiseUninitialized()\n\u001b[32m--> \u001b[39m\u001b[32m444\u001b[39m model = \u001b[43mauto_model\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    445\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    446\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m              \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    447\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtorch_dtype\u001b[49m\u001b[43m             \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtorch_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    448\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# quantization_config   = bnb_config,\u001b[39;49;00m\n\u001b[32m    449\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m                   \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    450\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m       \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    451\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# attn_implementation   = attn_implementation,\u001b[39;49;00m\n\u001b[32m    452\u001b[39m \u001b[43m    \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    453\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    454\u001b[39m raise_handler.remove()\n\u001b[32m    455\u001b[39m \u001b[38;5;66;03m# Return old flag\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/transformers/models/auto/auto_factory.py:600\u001b[39m, in \u001b[36m_BaseAutoModelClass.from_pretrained\u001b[39m\u001b[34m(cls, pretrained_model_name_or_path, *model_args, **kwargs)\u001b[39m\n\u001b[32m    598\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m model_class.config_class == config.sub_configs.get(\u001b[33m\"\u001b[39m\u001b[33mtext_config\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m    599\u001b[39m         config = config.get_text_config()\n\u001b[32m--> \u001b[39m\u001b[32m600\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmodel_class\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    601\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43mmodel_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mhub_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\n\u001b[32m    602\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    603\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    604\u001b[39m     \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mUnrecognized configuration class \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mconfig.\u001b[34m__class__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m for this kind of AutoModel: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mcls\u001b[39m.\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    605\u001b[39m     \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mModel type should be one of \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m, \u001b[39m\u001b[33m'\u001b[39m.join(c.\u001b[34m__name__\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mfor\u001b[39;00m\u001b[38;5;250m \u001b[39mc\u001b[38;5;250m \u001b[39m\u001b[38;5;129;01min\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28mcls\u001b[39m._model_mapping)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    606\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/transformers/modeling_utils.py:316\u001b[39m, in \u001b[36mrestore_default_torch_dtype.<locals>._wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    314\u001b[39m old_dtype = torch.get_default_dtype()\n\u001b[32m    315\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m316\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    317\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    318\u001b[39m     torch.set_default_dtype(old_dtype)\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/transformers/modeling_utils.py:5042\u001b[39m, in \u001b[36mPreTrainedModel.from_pretrained\u001b[39m\u001b[34m(cls, pretrained_model_name_or_path, config, cache_dir, ignore_mismatched_sizes, force_download, local_files_only, token, revision, use_safetensors, weights_only, *model_args, **kwargs)\u001b[39m\n\u001b[32m   5040\u001b[39m \u001b[38;5;66;03m# Prepare the full device map\u001b[39;00m\n\u001b[32m   5041\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m device_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m5042\u001b[39m     device_map = \u001b[43m_get_device_map\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_memory\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhf_quantizer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtorch_dtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeep_in_fp32_regex\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   5044\u001b[39m \u001b[38;5;66;03m# Finalize model weight initialization\u001b[39;00m\n\u001b[32m   5045\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m from_tf:\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/transformers/modeling_utils.py:1501\u001b[39m, in \u001b[36m_get_device_map\u001b[39m\u001b[34m(model, device_map, max_memory, hf_quantizer, torch_dtype, keep_in_fp32_regex)\u001b[39m\n\u001b[32m   1498\u001b[39m     device_map = infer_auto_device_map(model, dtype=target_dtype, **device_map_kwargs)\n\u001b[32m   1500\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m hf_quantizer \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1501\u001b[39m         \u001b[43mhf_quantizer\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_environment\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdevice_map\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1503\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m device_map \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   1504\u001b[39m     tied_params = find_tied_parameters(model)\n", "\u001b[36mFile \u001b[39m\u001b[32m/mnt/vdb1/envs/unsloth/lib/python3.11/site-packages/transformers/quantizers/quantizer_bnb_4bit.py:117\u001b[39m, in \u001b[36mBnb4BitHfQuantizer.validate_environment\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    115\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m    116\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mcpu\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m device_map_without_lm_head.values() \u001b[38;5;129;01mor\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mdisk\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m device_map_without_lm_head.values():\n\u001b[32m--> \u001b[39m\u001b[32m117\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    118\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mSome modules are dispatched on the CPU or the disk. Make sure you have enough GPU RAM to fit the \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    119\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mquantized model. If you want to dispatch the model on the CPU or the disk while keeping these modules \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    120\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33min 32-bit, you need to set `llm_int8_enable_fp32_cpu_offload=True` and pass a custom `device_map` to \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    121\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m`from_pretrained`. Check \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    122\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mhttps://huggingface.co/docs/transformers/main/en/main_classes/quantization#offload-between-cpu-and-gpu \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    123\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mfor more details. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    124\u001b[39m     )\n", "\u001b[31mValueError\u001b[39m: Some modules are dispatched on the CPU or the disk. Make sure you have enough GPU RAM to fit the quantized model. If you want to dispatch the model on the CPU or the disk while keeping these modules in 32-bit, you need to set `llm_int8_enable_fp32_cpu_offload=True` and pass a custom `device_map` to `from_pretrained`. Check https://huggingface.co/docs/transformers/main/en/main_classes/quantization#offload-between-cpu-and-gpu for more details. "]}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/gpt-oss-20b-unsloth-bnb-4bit\", # 20B model using bitsandbytes 4bit quantization\n", "    \"unsloth/gpt-oss-120b-unsloth-bnb-4bit\",\n", "    \"unsloth/gpt-oss-20b\", # 20B model using MXFP4 format\n", "    \"unsloth/gpt-oss-120b\", \n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"/mnt/vdb1/model/gpt-oss-20b-unsloth-bnb-4bit\", #unsloth/gpt-oss-20b-unsloth-bnb-4bit\",\n", "    dtype = None, # None for auto detection\n", "    max_seq_length = 4096, # Choose any for long context!\n", "    load_in_4bit = False,  # 4 bit quantization to reduce memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Reasoning Effort\n", "The `gpt-oss` models from OpenAI include a feature that allows users to adjust the model's \"reasoning effort.\" This gives you control over the trade-off between the model's performance and its response speed (latency) which by the amount of token the model will use to think.\n", "\n", "----\n", "\n", "The `gpt-oss` models offer three distinct levels of reasoning effort you can choose from:\n", "\n", "* **Low**: Optimized for tasks that need very fast responses and don't require complex, multi-step reasoning.\n", "* **Medium**: A balance between performance and speed.\n", "* **High**: Provides the strongest reasoning performance for tasks that require it, though this results in higher latency."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"low\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 512, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Changing the `reasoning_effort` to `medium` will make the model think longer. We have to increase the `max_new_tokens` to occupy the amount of the generated tokens but it will give better and more correct answer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"medium\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 1024, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lastly we will test it using `reasoning_effort` to `high`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"high\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 2048, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "unsloth", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00abd0bd091f4258b7f61d80ecb3118e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb1a288281fc446788fdd0f432b4732c", "max": 5702746403, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2f026e4c1ff34aafbf7c5deff6c1e66f", "value": 5702746403}}, "01ede422dc5f4d4da7f913880db4984d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "05d6f6c3efed4ecf9893c8aa86fe4176": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07ae7e9f6861403ea8064a98f4063754": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0c190d903a38402ab452de0a0100a8eb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f2c8641a38043d2a218ec0809e7a7d3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_acc52213c033476885ee30fb92f55e23", "placeholder": "​", "style": "IPY_MODEL_9caec4d2cb6e4a72b7155912787d3139", "value": " 5.70G/5.70G [00:40&lt;00:00, 158MB/s]"}}, "11fea1d7e4a8426d98165fa5ca7fc650": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "13b8be4a5af14bfdbbce24d185187906": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fc3715b03c5249438965886892ba07bb", "placeholder": "​", "style": "IPY_MODEL_05d6f6c3efed4ecf9893c8aa86fe4176", "value": " 449/449 [00:00&lt;00:00, 27.4kB/s]"}}, "1a4ef35565f54388afdf1839055eb5c8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e0c170f075b43ad9e2d39fcdbaa0a06": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_47a13a9b02ad4181b74cc1f5ddeb3f3f", "placeholder": "​", "style": "IPY_MODEL_7c01111484b14a5999d48393085b0264", "value": "model.safetensors: 100%"}}, "1f7ed3fc3a824e119cf559af8bc7c370": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_31c7337bfd504409992b7e04b713c243", "max": 51015, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9b5a45647bd74e8c878cf6ba43b27190", "value": 51015}}, "21d8ebf7cbfd4e2b9db111d163aad8b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2abdab3fdd4f4a70a8faaa7b0e39f811": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4d432e4349964027bae76c34f5734fcf", "IPY_MODEL_630c170c04494811b21992d6886a3015", "IPY_MODEL_cb9a92b76a1d4b189e7ed783bde524c9"], "layout": "IPY_MODEL_ccc49354213c4c1394855986a0e07483"}}, "2ad28d8e556b4e17b0859aa3683d31c6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2e9f87f895344785855cca39bb64d5e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f026e4c1ff34aafbf7c5deff6c1e66f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3122a97298124d0ab0e6297960c132e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31c7337bfd504409992b7e04b713c243": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "36250ac65fcb4756a4368f98902a1617": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_705a1673de934cce8418f7893fbf780a", "IPY_MODEL_1f7ed3fc3a824e119cf559af8bc7c370", "IPY_MODEL_cafc86be3bd94bc79cb07bc94461ec5d"], "layout": "IPY_MODEL_0c190d903a38402ab452de0a0100a8eb"}}, "3f5a8b6d049244959c1558c56483f643": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "447aa145c09c4c6f8bd9ce3ea8b5cd44": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "47a13a9b02ad4181b74cc1f5ddeb3f3f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "493f6a1af5e94b5eb88a4935361a88c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4afb54f2995447e68ae3a8129325ffdc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4d432e4349964027bae76c34f5734fcf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7bf239e457f04e15b4b0e458f0dd1e19", "placeholder": "​", "style": "IPY_MODEL_f5c6558ef929438eb117159de0285a58", "value": "generation_config.json: 100%"}}, "4f0e06a2e2204dad9f29a1697edd4218": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5200dae3ba2346c1bca27530b82b38b4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5512e17f3864445a875e640937f296ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "58413234f68b484693f0719166942214": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "60a80b3316c14ce79945e06cb190be39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2e9f87f895344785855cca39bb64d5e2", "placeholder": "​", "style": "IPY_MODEL_5512e17f3864445a875e640937f296ef", "value": " 9.09M/9.09M [00:00&lt;00:00, 15.6MB/s]"}}, "630c170c04494811b21992d6886a3015": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_447aa145c09c4c6f8bd9ce3ea8b5cd44", "max": 131, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9c82690bd07346d1ab875bdf5ed80154", "value": 131}}, "66527058da914c73b48221011a2b605a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "705a1673de934cce8418f7893fbf780a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b3362bda7942432692049558d0cdf14e", "placeholder": "​", "style": "IPY_MODEL_4afb54f2995447e68ae3a8129325ffdc", "value": "tokenizer_config.json: 100%"}}, "73c3f04df8044f34ab45b92c174e121e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f5a8b6d049244959c1558c56483f643", "max": 449, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_11fea1d7e4a8426d98165fa5ca7fc650", "value": 449}}, "78676eb3a75f45ed860b488a42fb9bc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a4ef35565f54388afdf1839055eb5c8", "placeholder": "​", "style": "IPY_MODEL_07ae7e9f6861403ea8064a98f4063754", "value": "config.json: 100%"}}, "7bf239e457f04e15b4b0e458f0dd1e19": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c01111484b14a5999d48393085b0264": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7ca2facea2414549ab2a0a3fd07a0723": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_78676eb3a75f45ed860b488a42fb9bc5", "IPY_MODEL_b69b75dd3b5745f4a7f869f7ba6593d4", "IPY_MODEL_ea10da0d677849c28bfdfc9206ac1b73"], "layout": "IPY_MODEL_493f6a1af5e94b5eb88a4935361a88c3"}}, "84b3814386ee4d04b9817f0da2108725": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8fecfa968d55466ba8f42730d32bd9b4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_66527058da914c73b48221011a2b605a", "placeholder": "​", "style": "IPY_MODEL_58413234f68b484693f0719166942214", "value": "tokenizer.json: 100%"}}, "932920c0ee834e3e8ffe53ebf7833fa7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cb65c93ecef84c2c947c67481326691c", "IPY_MODEL_73c3f04df8044f34ab45b92c174e121e", "IPY_MODEL_13b8be4a5af14bfdbbce24d185187906"], "layout": "IPY_MODEL_21d8ebf7cbfd4e2b9db111d163aad8b8"}}, "948972fa1a2e400097b590ad47ff6f3d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9b5a45647bd74e8c878cf6ba43b27190": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9b818c8e8bac40edb97abf8a266ab28e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9c82690bd07346d1ab875bdf5ed80154": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9caec4d2cb6e4a72b7155912787d3139": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "acc52213c033476885ee30fb92f55e23": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3362bda7942432692049558d0cdf14e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b69b75dd3b5745f4a7f869f7ba6593d4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fc2ce36bb32f4c648ccb06d65a4e2aea", "max": 1149, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bf44ec9413b24dda932f575ebe32b222", "value": 1149}}, "b9c9010af82b4678af43a20546f3ad49": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf44ec9413b24dda932f575ebe32b222": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c7a3b0cba7ee49ba929e0038d9c03e98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cafc86be3bd94bc79cb07bc94461ec5d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b9c9010af82b4678af43a20546f3ad49", "placeholder": "​", "style": "IPY_MODEL_9b818c8e8bac40edb97abf8a266ab28e", "value": " 51.0k/51.0k [00:00&lt;00:00, 623kB/s]"}}, "cb65c93ecef84c2c947c67481326691c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3122a97298124d0ab0e6297960c132e2", "placeholder": "​", "style": "IPY_MODEL_cc829a40832849f586b077f6d8e5795b", "value": "special_tokens_map.json: 100%"}}, "cb9a92b76a1d4b189e7ed783bde524c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5200dae3ba2346c1bca27530b82b38b4", "placeholder": "​", "style": "IPY_MODEL_c7a3b0cba7ee49ba929e0038d9c03e98", "value": " 131/131 [00:00&lt;00:00, 5.52kB/s]"}}, "cc829a40832849f586b077f6d8e5795b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ccc49354213c4c1394855986a0e07483": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9c5466214ed4f06876a89c64c29048f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1e0c170f075b43ad9e2d39fcdbaa0a06", "IPY_MODEL_00abd0bd091f4258b7f61d80ecb3118e", "IPY_MODEL_0f2c8641a38043d2a218ec0809e7a7d3"], "layout": "IPY_MODEL_948972fa1a2e400097b590ad47ff6f3d"}}, "dd0e8e4cb46945df8dbec4fefd720358": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_84b3814386ee4d04b9817f0da2108725", "max": 9085698, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_01ede422dc5f4d4da7f913880db4984d", "value": 9085698}}, "e32220b1d3f14386834bcd3736ac9888": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ea10da0d677849c28bfdfc9206ac1b73": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ad28d8e556b4e17b0859aa3683d31c6", "placeholder": "​", "style": "IPY_MODEL_e32220b1d3f14386834bcd3736ac9888", "value": " 1.15k/1.15k [00:00&lt;00:00, 49.9kB/s]"}}, "ebaa8d22074d495ea675f51dc2d5a4d6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8fecfa968d55466ba8f42730d32bd9b4", "IPY_MODEL_dd0e8e4cb46945df8dbec4fefd720358", "IPY_MODEL_60a80b3316c14ce79945e06cb190be39"], "layout": "IPY_MODEL_4f0e06a2e2204dad9f29a1697edd4218"}}, "f5c6558ef929438eb117159de0285a58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fb1a288281fc446788fdd0f432b4732c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc2ce36bb32f4c648ccb06d65a4e2aea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc3715b03c5249438965886892ba07bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}