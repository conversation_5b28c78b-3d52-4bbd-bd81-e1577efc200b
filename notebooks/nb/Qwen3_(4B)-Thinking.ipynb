{"cells": [{"cell_type": "markdown", "metadata": {"id": "-0Mk20PzzJVA"}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {"id": "_EgGYCCMzJVD"}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {"id": "YJEixMxNzJVE"}, "source": ["Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {"id": "647sszMXzJVF"}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "GxmRuYCpzJVH"}, "outputs": [], "source": ["%%capture\n", "import os\n", "if \"COLAB_\" not in \"\".join(os.environ.keys()):\n", "    !pip install unsloth\n", "else:\n", "    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n", "    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n", "    !pip install sentencepiece protobuf \"datasets>=3.4.1,<4.0.0\" \"huggingface_hub>=0.34.0\" hf_transfer\n", "    !pip install --no-deps unsloth"]}, {"cell_type": "markdown", "metadata": {"id": "TGMWlrRdzwgf"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>\n", "\n", "`FastModel` supports loading nearly any model now! This includes Vision and Text models!"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 564, "referenced_widgets": ["635aa35135204b63b335e6ed2d25cd58", "050513d95c324075b7645fa7bad4cf46", "ea2f9cac80a14cb2b44f03bdead1f607", "440796985da04f64b93fa7bbeece0405", "17508557050f494d9cb1f8c82ebcec29", "a3837c92520e46548459859699833a26", "4d0d848cdef34e25a28a202d9f955dcc", "04893108f40c4669b625a96d24e3fdd8", "a08529dd72c04791aa2c5fc2aa959ea8", "5fe9f01020894965bd1a905f614f6640", "fdc7e93081c2473cbb85c00eb15dd8c9", "5696bb993a014fb1be8e86fabed24d9a", "276f4ed59c9c426684370f37ddf473ff", "17d7a4a2bd1e496285447bb459062ebd", "176731ee50d745d1acc1196233ceabdd", "47fdaec63514405b9da598c49035db7a", "537a54d6928f48138e3d8410f29f833a", "b826457480d046d9ade5b6c06edee2d6", "c6967fc3d503403baf92e9f21b020c4d", "c58f077e52d54a64851b4fb999576469", "749ea0dd9af14715a437ccf73b601952", "f0d1adfa6dc54925a94143f963bf0c6c", "cb718b64404d4c33b64c7e1b8ad10650", "cad9cff364a14ccda5d4103e861fb52b", "f427b8e6cdab46cc83e4ac390a86737e", "cdf1776551c04900bc17398881a03dce", "a6fdbcf3a0ff43da9bc405109acb8669", "7fef0d9e8654445282d3af3ce5039752", "bac0d05a338d45929200e28ddeccd951", "70903d52b5814b8ca3d57960571d7b59", "f9ea4edf34f04470ac33ff34349c3e0b", "073aed78e30649109c4ddde90ca5282b", "343fa387fa384f758ef31baac9d6d4c9", "6e9ef1fa47b44c9facf1c6ed797b95f9", "5549688dcfc34b74a31b0fcfb04cc8f4", "8adfa15c86154ae08ac3a0963bbad75b", "fe4a07a4ccad42be929068c838626c6e", "f5fe59b9cc574308a30049fc086aa7b8", "780aa6fda2274519a21b8e2fd1232d1a", "0c8041c2222446f2a9af3f719b8c123d", "ffe0f3db0da14f23a56b27c4e3d71541", "d855f19a2b5e4c34872e302b94600bb9", "435cf57034564cb7b9dfca4007ccfb00", "3ffc4b7dc8a8491da7457cfbd7d954fb", "d2fddc5f9bc940209c9c978afedf3b15", "e0e9784325ca423bb82160a0e5cec64b", "e2b8f75ff4ef40519fb5253d761f5ca4", "d2ad0349a7b945259da747b0e87b55f8", "0aee85df420241b8adb8981bc2b34c18", "9063e882934c4013834b6cea28d94b33", "ca882dc990e946ffade4746f631032ae", "034ffe5a4c1741aea0f301f34d26f556", "be792d91f24f4440be10ca69d68e9779", "aaa44024960843f79b733651f20bf51d", "bd6edc2fc570405db15658911357a00b", "7a67f0fd9576438ab386ea1d7c491d08", "a69a9cac8d5c45e28b36cec7c52098c8", "dd815b5e1f124314b3cf1767280197aa", "8f9835b0135f43379c958fc3245e403d", "d051914a9ad4427c99243f68e3ffd2d3", "b5c466b4a6ab406eb6d214f4bd878af4", "c46b62f3caba48d28ab5c8463bea332d", "1bc423dcab9b4951a158d9697ead4e2c", "50ec1e2bc2c64c969971d3897c1f816f", "b3664df00bec42cd83cc4c9f9e65dcac", "6ff59e0ef18d4e7590d0ca21638e6f9b", "1c08a939cfc5421c8b60cef99827ba9e", "1330cc79dd404420b35f894327e2d6e8", "1c51f037d3de454fba7a2618764cd1de", "183d8ee00ec84b4e814ec1cd76dd791c", "f8a545d126a34e139584995f354c24f1", "2120e08c38d24aa0b008110043fefd02", "68799cd5e3c046cb852705315a6e34d5", "33c909d9e3314dd78b55ac095635ba53", "e10b24dfcc234dc4887b58f7a65b8b39", "ee7d96909a384441b0611a7cfce7e528", "eb3ae807c95040868a318c778e8af8be", "a302fd0a011d425b995efb15c4c93c0b", "582ed73db30c482880c094cb0b5a7c85", "477fadc32ae844eab8c718a9b8d15fd7", "4ef4b46f5ec3456d83b7846ec6012513", "7e44e8b2cbce4897bd9dfe1b727f0a6e", "0c9e8deb4cc3407ab71f57456b22999a", "4d42d007d3bf46c59b1e0a2d4871d386", "b213141e6e54493cba2aa02ac23cb0f9", "cc6e6e2415654fdc981a323960500cd6", "27dac615fca34f79a79f2a0d25dabef7", "9c763732c6494f7fac2d05282c68c5f9", "607c3299d5ad4c9281bad5e4d7f7eea8", "6c05bbbb3d564534a25d1e3c0f58c39b", "ab7d3e4276454c3f8d30b64eaa9bfadc", "d5899be3cd18490b8be50e91667ca744", "9d051c5781874219969f749051640ddb", "deab123cf6ee494296aa9c5878f87c49", "bcfbc9734084412c8f54204bad538be3", "c3acb83f8c324e73b1a7d68f063d7892", "2512cc5d743444d98afba7539c07940f", "a8d1bc6678114de9a9b3c58e24552347", "7c50d7b2ae02480daac56cd6e89c5060", "54e53c126b7c4b24ae761fe5efa2fc88", "001e41f3c6174a7c9e402274991271e6", "d34c8ec78d6044bf8634898f88ead48f", "57cc71fbe3b44e8fb193e7fd766c16e5", "a3b3c82882b84254b6d3f8e8058d0f4b", "615071e22c364826a6358725a1cecce8", "68b11d23e2394cb186890ac5f5871b87", "3fbdb74f73da4d74a1a195377ec68a0d", "71fc3a35c7d642a6a5b769e9c37ba4c2", "df18273d629d4afe83cf193005e53118", "f69cb7e301f74e3f8563e37824bb08b4", "56a3cbc01d9946f58a5c0361ab483035", "07e69f0bc9c64b49bece04e69125a56d", "b475db3294ae425f98a94922748e28e0", "f318bb164df94f1c92967b3099cc89b6", "d08579a243f046d1a3a53799874d7675", "a833af05c94a4831a7329a0430904fa3", "0fc28e2957d446a98d8b2a85068c3523", "437a03ff1f094d709f971969579dc2c3", "7b62d9ad15f642c0a1e4939975a93379", "312c0889cb4147edb01cabc3027ed225", "d251b5d0b0bc4334a212074e4fa682ba", "73051a753f194ca8a8db318650b8c283", "967bb6e658b04444812fb78f2b06e3c6", "54696872ef4e44478e186483c3d784df", "d90cc22afc234cfb8e70e4be8cbd8e25", "00dedb764dcf4393b0dabcba385d132c", "290431bcea9c4e9ba94ecb5f692a0171", "b3ba17e6bf9c44faaf2498b09d06a128", "f2263f0df7884ee38ceefab975a00231", "9de0460d76994700863cc7b95451d0d1", "b24007321ba3474abb115c7e25412c82", "4d7d95bfd83b4284bdd2f9b0733fa926"]}, "id": "-Xbb0cuLzwgf", "outputId": "3e19f218-7f37-493e-c381-e8388d396c04"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.8.2: Fast Qwen3 patching. Transformers: 4.54.1.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: QLoRA and full finetuning all not selected. Switching to 16bit LoRA.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors.index.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "635aa35135204b63b335e6ed2d25cd58"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/4.97G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5696bb993a014fb1be8e86fabed24d9a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/3.08G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "cb718b64404d4c33b64c7e1b8ad10650"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6e9ef1fa47b44c9facf1c6ed797b95f9"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["generation_config.json:   0%|          | 0.00/238 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d2fddc5f9bc940209c9c978afedf3b15"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7a67f0fd9576438ab386ea1d7c491d08"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.json: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1c08a939cfc5421c8b60cef99827ba9e"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["merges.txt: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a302fd0a011d425b995efb15c4c93c0b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "607c3299d5ad4c9281bad5e4d7f7eea8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["added_tokens.json:   0%|          | 0.00/707 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "54e53c126b7c4b24ae761fe5efa2fc88"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/614 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "56a3cbc01d9946f58a5c0361ab483035"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["chat_template.jinja: 0.00B [00:00, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "73051a753f194ca8a8db318650b8c283"}}, "metadata": {}}], "source": ["from unsloth import FastModel\n", "import torch\n", "\n", "fourbit_models = [\n", "    \"unsloth/Qwen3-4B-Instruct-2507-unsloth-bnb-4bit\", # Qwen 14B 2x faster\n", "    \"unsloth/Qwen3-4B-Thinking-2507-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-8B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-14B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-32B-unsloth-bnb-4bit\",\n", "\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/Phi-4\",\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/orpheus-3b-0.1-ft-unsloth-bnb-4bit\" # [NEW] We support TTS models!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastModel.from_pretrained(\n", "    model_name = \"unsloth/Qwen3-4B-Thinking-2507\",\n", "    max_seq_length = 2048, # Choose any for long context!\n", "    load_in_4bit = False,  # 4 bit quantization to reduce memory\n", "    load_in_8bit = False, # [NEW!] A bit more accurate, uses 2x memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update a small amount of parameters!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "481ac32e-2d19-4e39-b3e4-b6c895728a4f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Unsloth: Making `model.base_model.model.model` require gradients\n"]}], "source": ["model = FastModel.get_peft_model(\n", "    model,\n", "    r = 32, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 32,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the `Qwen-3` format for conversation style finetunes. We use the [Open Math Reasoning]() dataset which was used to win the [AIMO](https://www.kaggle.com/competitions/ai-mathematical-olympiad-progress-prize-2/leaderboard) (AI Mathematical Olympiad - Progress Prize 2) challenge! We sample 10% of verifiable reasoning traces that used DeepSeek R1, and whicht got > 95% accuracy. Qwen-3 renders multi turn conversations like below:\n", "\n", "```\n", "<|im_start|>user\n", "Hello!<|im_end|>\n", "<|im_start|>assistant\n", "Hey there!<|im_end|>\n", "\n", "```\n", "We use our `get_chat_template` function to get the correct chat template. We support `zephyr, chatml, mistral, llama, alpaca, vicuna, vicuna_old, phi3, llama3, phi4, qwen2.5, gemma3` and more."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "y1lMI6w7PUmq"}, "outputs": [], "source": ["from unsloth.chat_templates import get_chat_template\n", "tokenizer = get_chat_template(\n", "    tokenizer,\n", "    chat_template = \"qwen3-thinking\",\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "Mkq4RvEq7FQr", "colab": {"base_uri": "https://localhost:8080/", "height": 113, "referenced_widgets": ["3ece66bde3ba44a8bf3b074addeecef7", "16971744872c48c5a9cac75d600f4f7d", "d97c8c73fa704e1e942e6d35a0b1486d", "3cc677db19664fc18c66f1dc4af79663", "d9fc2c3aa8584b15a419e3b503d3cee8", "f7bdec0110f2459aa511de96d91ae758", "86c09b80b5ab49abb0b2df9f16f18354", "522f7960fa3f4600b7498dba100b2ee2", "76f63b94b35b488baca933782168495a", "102ad5f665be41d7819280ec10267f3c", "4b890797eff046c9a4b041eecb39cd16", "3c1f5511bcf54527ac1ba9b1ab621a63", "49ce971fef214698a7e574d3434eb5c4", "c21d077eccb74320b2d775ec564ecfe7", "67682cb1c9604820bdff8e7635b1fc69", "be7ade0b423e4041afca47e9b8885034", "ca98fa7a25ab4126bdff4b8ef6d079d7", "97ae3a140ba346f4ab00108cf4724e82", "c22fb6adc1284dc1a8faf261f7721ccd", "16da517598e347828a535d636d82f0e9", "5ece47b73673483496a0ee0cf6acd3c0", "fecfe673f3e74e28a8629c5f4142886a", "4d35377c209149009dace45ce9ac9d95", "9fe44f7a368b4fbba1380f3389e61d79", "a5ede770956f4ce891f3815459b20694", "e00cb54c2c3745158e7a1bb67c43741c", "a001d9502f2b4e21b58c6fdf88953d1d", "795490276c7a4c6c95728fa059ae9a6d", "a90cec6283e6435181989cd7e3d23649", "6eccca21ebde465ba5b1da3e703a9c8a", "3cd3c11af7cb43b4ab4db74ff5547eec", "5f6750df4fa643ddab570d729dcb19df", "1b2f5f598e5943e2a79c93f720435dca"]}, "outputId": "24591c8a-fb8d-4adb-8325-8c9ab397bc2a"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/603 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3ece66bde3ba44a8bf3b074addeecef7"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["data/cot-00000-of-00001.parquet:   0%|          | 0.00/106M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3c1f5511bcf54527ac1ba9b1ab621a63"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Generating cot split:   0%|          | 0/19252 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4d35377c209149009dace45ce9ac9d95"}}, "metadata": {}}], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"unsloth/OpenMathReasoning-mini\", split = \"cot\")"]}, {"cell_type": "markdown", "metadata": {"id": "XNviDOtIPUmq"}, "source": ["We now convert the reasoning dataset into conversational format:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["532faaa33b964e598857946bd41adde8", "44c374a356954794aa12238ea58fdd9f", "28763b3e16c04be4be75d8425898dfb5", "efd1ea95c79a4f47ba4c2c06f9353f6d", "3d1bc8b69b8c4982adb430d2bda56777", "f84d2da9e4fe4623bba549b859fd463a", "411aac5aaa5b45989215dc75709411a2", "322026b66f954d4695bfbac7866f8d24", "d2a6f3f07d4b4e75b15c4abb85397251", "0ee29972612448db9ca57d36deef3c20", "f7132a5b6c3647179599ee2da89a8c40"]}, "id": "bv-1hctMPUmr", "outputId": "5c03a992-38f4-41a3-f4d0-2108a69ec896"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Map:   0%|          | 0/19252 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "532faaa33b964e598857946bd41adde8"}}, "metadata": {}}], "source": ["def generate_conversation(examples):\n", "    problems  = examples[\"problem\"]\n", "    solutions = examples[\"generated_solution\"]\n", "    conversations = []\n", "    for problem, solution in zip(problems, solutions):\n", "        conversations.append([\n", "            {\"role\" : \"user\",      \"content\" : problem},\n", "            {\"role\" : \"assistant\", \"content\" : solution},\n", "        ])\n", "    return { \"conversations\": conversations, }\n", "\n", "dataset = dataset.map(generate_conversation, batched = True)"]}, {"cell_type": "markdown", "metadata": {"id": "8Xs0LXio7rfd"}, "source": ["We now have to apply the chat template for `Qwen-3` onto the conversations, and save it to `text`."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["ff08345c14a3411bac9e13d2c2aa8988", "1dd1cc3b1ff64c349dfaaa392ae90de4", "05ea5e52e51e40198d2d8a59bc9863c0", "47de5f17910b45b3acced52f4cfe76f3", "e427d3231bd646f0aa3c385577ffc0cc", "e41051e246ea4ed58abedacc8de1c06f", "5fa13cf4d8994158b95f9339d1c767db", "8d4876643d51428c8c32ba2bcf7fc543", "fb131a2471bc4c4680ddf4fec1222560", "5fe585403bb847eeabe2301c1f191ae5", "fc697c13cace4ff9ab4380e834161836"]}, "id": "ZBD2G4s_PUmr", "outputId": "bde6af11-3fd7-409e-ef29-ac3e995d6872"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Map:   0%|          | 0/19252 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ff08345c14a3411bac9e13d2c2aa8988"}}, "metadata": {}}], "source": ["def formatting_prompts_func(examples):\n", "   convos = examples[\"conversations\"]\n", "   texts = [tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]\n", "   return { \"text\" : texts, }\n", "\n", "dataset = dataset.map(formatting_prompts_func, batched = True)"]}, {"cell_type": "markdown", "metadata": {"id": "ndDUB23CGAC5"}, "source": ["Let's see how the chat template did!\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 127}, "id": "fgevylZFPUms", "outputId": "acdeda56-dbf8-4ae3-bfda-4a144046d26c"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<|im_start|>user\\nOn a wall, there are two clocks with the same shape (radius) and the same speed, but they may not show the same hour. The minimum distance between the edges of their hands is \\\\( m \\\\), and the maximum distance is \\\\( M \\\\). What is the distance between their centers?<|im_end|>\\n<|im_start|>assistant\\n<think>\\nOkay, so I have this problem here about two clocks on a wall. Both clocks have the same radius and the same speed, but they might show different times. The minimum distance between the edges of their hands is m, and the maximum is M. I need to find the distance between their centers. Hmm, let\\'s break this down.\\n\\nFirst, let me visualize the situation. Both clocks are circular with the same radius, let\\'s say radius r. The hands of each clock are moving at the same speed, so their minute and hour hands move at the same rates. However, they might not be showing the same time, which means their hands could be pointing in different directions. The edges of their hands—so the tips of the hands—are what we\\'re concerned with here. The minimum distance between these tips is m, and the maximum is M. We need to find the distance between the centers of the two clocks.\\n\\nWait, but how are the clocks arranged on the wall? Are they overlapping? If the distance between centers is such that when the hands are pointing towards each other, the tips are closest, and when pointing away, they\\'re farthest. But maybe the clocks are placed at a certain distance apart so that the hands\\' tips have varying distances based on their positions.\\n\\nLet me think. Let\\'s model each clock as a circle with radius r. The centers of the two clocks are separated by a distance d, which we need to find. The problem states that the minimum and maximum distances between the edges (tips) of their hands are m and M, respectively. So, the hands are moving, but since they have the same speed, maybe their angles relative to each other are fixed? Or do the hands move independently?\\n\\nWait, the problem says they \"may not show the same hour,\" which probably means that their hands can be at any angle relative to each other. But since both clocks are operating at the same speed, if they start at different times, their hands will maintain a constant angle difference. Wait, but the minute and hour hands move at different speeds. Wait, the problem says \"the same speed,\" but in reality, the hour and minute hands move at different speeds. Hmm, maybe in this problem, each clock is simplified such that there\\'s a single hand moving at a constant speed? Wait, but in standard clocks, the hour and minute hands have different speeds. Maybe the problem is referring to each clock having just one hand? The problem says \"hands,\" plural, so perhaps both clocks have hour and minute hands, but they might not show the same time. But the problem mentions the distance between the edges of their hands. Wait, maybe it\\'s the tips of the hands? So perhaps each clock has an hour hand and a minute hand, but the problem is considering the distance between the tips of the hands from one clock to the other?\\n\\nWait, the problem says \"the edges of their hands\" – maybe the hands refer to the clock hands, so each clock has hands (like hour, minute, etc.), but the problem is talking about the distance between the edges (tips) of the hands from each clock. Wait, but if each clock has multiple hands, then the distance between edges would vary depending on which hands you\\'re comparing. Hmm, maybe the problem is simplified such that each clock has only one hand? Or perhaps the problem is referring to the distance between the tips of the corresponding hands? For example, the tip of the hour hand of one clock to the tip of the hour hand of the other, and similarly for the minute hand? But the problem states \"the edges of their hands,\" so maybe considering all possible pairs of hands between the two clocks? But that seems complicated. Wait, maybe the problem is just considering the distance between the tips of the hands of the same type? For example, the hour hands of each clock? Or maybe it\\'s considering the distance between any two hands from different clocks? The problem is a bit ambiguous here.\\n\\nWait, let me read the problem again: \"the minimum distance between the edges of their hands is m, and the maximum distance is M.\" The key here is probably that the clocks have the same radius and same speed, but may not show the same hour. So perhaps each clock has a single hand (maybe the hour hand?), and the tip of the hour hand on each clock moves in a circle of radius r. The problem is then to find the distance between the centers of the two clocks, given that the minimum and maximum distances between the tips of their hands are m and M. That seems plausible.\\n\\nAlternatively, if each clock has both hour and minute hands, but the problem is considering the minimum and maximum distances between any two hands from the two clocks. However, that would complicate things because there could be multiple distances to consider. But since the problem states \"the edges of their hands,\" maybe it\\'s considering the distances between the tips of all hands, but given that they have the same speed, perhaps the configuration repeats periodically. But this seems too vague.\\n\\nAlternatively, maybe the problem is referring to two clocks with only one hand each, both moving at the same angular speed. But if they have the same angular speed, then if they start at the same time, the angle between them remains constant. However, the problem says \"they may not show the same hour,\" which might mean that the angle between their hands can vary, but since they have the same speed, the angle difference is fixed. Wait, that\\'s a contradiction. If two hands move at the same angular speed, then once set at a certain angle difference, that difference remains constant. Therefore, the minimum and maximum distances between the hands would be fixed based on the angle difference and the distance between centers.\\n\\nWait, but in that case, if the angle difference is fixed, then the distance between the tips would vary sinusoidally as the hands rotate, but with the same phase. Wait, no. If both hands are moving at the same angular speed, then if there\\'s an initial angle difference, they maintain that angle difference as they rotate. Therefore, the distance between the tips would be constant? Wait, that doesn\\'t make sense.\\n\\nWait, let me clarify. Suppose both hands are moving at the same angular speed ω. If they start at angles θ₁ and θ₂, then their angles at time t are θ₁ + ωt and θ₂ + ωt. The angle between them is (θ₂ - θ₁), which is constant. So the distance between the tips would also be constant? But that can\\'t be right. Wait, no, the distance between two points moving in circles with the same angular velocity but different initial angles—if the angle difference is fixed, then the distance between them would vary depending on their position around the circle. Wait, but if the angle between them is fixed, then as they rotate, the distance between their tips would be the same as two points on two circles separated by a fixed angle. So, for example, if the two hands are always 180 degrees apart, their tips would be on opposite sides of their respective circles, so the distance between them would be d + 2r or d - 2r, depending on the direction. Wait, but that\\'s only if the centers are aligned with the line connecting the tips. Hmm.\\n\\nWait, maybe I need to model this mathematically. Let\\'s assume each clock has a single hand of length r. The centers of the two clocks are separated by distance d. The hands are rotating with the same angular speed ω. Let θ₁(t) and θ₂(t) be the angles of the hands of the first and second clocks at time t. Since they have the same speed, θ₂(t) = θ₁(t) + φ, where φ is a fixed phase difference. Then, the positions of the tips of the hands are:\\n\\nFor the first clock: (x₁, y₁) = (d/2 + r cos θ₁(t), r sin θ₁(t))\\nFor the second clock: (x₂, y₂) = (-d/2 + r cos θ₂(t), r sin θ₂(t))\\n\\nWait, assuming the centers are along the x-axis separated by distance d. So center of first clock is at (d/2, 0), second at (-d/2, 0). Then the tips of their hands would be:\\n\\nFirst tip: (d/2 + r cos θ₁, r sin θ₁)\\nSecond tip: (-d/2 + r cos θ₂, r sin θ₂)\\n\\nThen the distance squared between the tips is:\\n\\n[d/2 + r cos θ₁ - (-d/2 + r cos θ₂)]² + [r sin θ₁ - r sin θ₂]²\\n\\nSimplify:\\n\\n[d + r cos θ₁ - r cos θ₂]² + [r sin θ₁ - r sin θ₂]^2\\n\\nExpanding:\\n\\nd² + 2d r (cos θ₁ - cos θ₂) + r² (cos θ₁ - cos θ₂)^2 + r² (sin θ₁ - sin θ₂)^2\\n\\nSimplify the terms:\\n\\nr² [(cos θ₁ - cos θ₂)^2 + (sin θ₁ - sin θ₂)^2] + 2d r (cos θ₁ - cos θ₂) + d²\\n\\nThe term in the brackets can be simplified using the identity:\\n\\n(cos A - cos B)^2 + (sin A - sin B)^2 = 2 - 2 cos(A - B)\\n\\nSo that becomes:\\n\\nr² [2 - 2 cos(θ₁ - θ₂)] + 2d r (cos θ₁ - cos θ₂) + d²\\n\\nWhich simplifies to:\\n\\n2 r² (1 - cos φ) + 2d r (cos θ₁ - cos θ₂) + d², where φ = θ₁ - θ₂.\\n\\nBut since θ₂ = θ₁ + φ (since same angular speed), then θ₁ - θ₂ = -φ. Wait, but if they have the same speed, θ₂(t) = θ₁(t) + φ, so φ is a constant phase difference.\\n\\nTherefore, θ₁ - θ₂ = -φ, so cos(θ₁ - θ₂) = cos(-φ) = cos φ.\\n\\nSo the first term is 2 r² (1 - cos φ).\\n\\nThe second term is 2d r [cos θ₁ - cos(θ₁ + φ)].\\n\\nSo the entire expression becomes:\\n\\n2 r² (1 - cos φ) + 2d r [cos θ₁ - cos(θ₁ + φ)] + d²\\n\\nHmm, so this expression depends on θ₁, which is changing over time. Therefore, the distance between the tips varies with θ₁. Therefore, the maximum and minimum distances occur at different values of θ₁.\\n\\nBut φ is fixed, so as θ₁ increases, the angle θ₁ + φ increases as well, and the term [cos θ₁ - cos(θ₁ + φ)] will vary sinusoidally. Therefore, the distance squared is a function that depends on θ₁, and we need to find its maximum and minimum.\\n\\nGiven that, we can model the distance squared as:\\n\\nD² = d² + 2 r² (1 - cos φ) + 2 d r [cos θ₁ - cos(θ₁ + φ)]\\n\\nLet me denote θ = θ₁ for simplicity.\\n\\nThen,\\n\\nD² = d² + 2 r² (1 - cos φ) + 2 d r [cos θ - cos(θ + φ)]\\n\\nUsing the trigonometric identity cos(θ + φ) = cos θ cos φ - sin θ sin φ.\\n\\nSo,\\n\\ncos θ - cos(θ + φ) = cos θ - (cos θ cos φ - sin θ sin φ) = cos θ (1 - cos φ) + sin θ sin φ\\n\\nTherefore,\\n\\nD² = d² + 2 r² (1 - cos φ) + 2 d r [cos θ (1 - cos φ) + sin θ sin φ]\\n\\nFactor out (1 - cos φ):\\n\\n= d² + 2 r² (1 - cos φ) + 2 d r (1 - cos φ) cos θ + 2 d r sin θ sin φ\\n\\nLet me group the terms with cos θ and sin θ:\\n\\n= d² + 2 r² (1 - cos φ) + [2 d r (1 - cos φ)] cos θ + [2 d r sin φ] sin θ\\n\\nLet me denote A = 2 d r (1 - cos φ) and B = 2 d r sin φ\\n\\nSo,\\n\\nD² = d² + 2 r² (1 - cos φ) + A cos θ + B sin θ\\n\\nNow, A cos θ + B sin θ can be written as C cos(θ - δ), where C = √(A² + B²) and tan δ = B/A.\\n\\nCompute C:\\n\\nC = √[ (2 d r (1 - cos φ))² + (2 d r sin φ)^2 ]\\n\\n= 2 d r √[ (1 - cos φ)^2 + sin² φ ]\\n\\nExpand (1 - cos φ)^2 + sin² φ:\\n\\n= 1 - 2 cos φ + cos² φ + sin² φ\\n\\n= 2(1 - cos φ)\\n\\nSo,\\n\\nC = 2 d r √[2(1 - cos φ)] = 2 d r √[2 * 2 sin²(φ/2)] (since 1 - cos φ = 2 sin²(φ/2))\\n\\n= 2 d r √[4 sin²(φ/2)] = 2 d r * 2 |sin(φ/2)| = 4 d r |sin(φ/2)|\\n\\nSince φ is an angle between the two hands, we can assume it\\'s between 0 and 2π, so sin(φ/2) is non-negative if φ is between 0 and π, and negative otherwise. But since we take absolute value, C = 4 d r |sin(φ/2)|. But since distance can\\'t be negative, we can ignore the absolute value and write C = 4 d r sin(φ/2) assuming φ is between 0 and 2π, so sin(φ/2) is positive for 0 < φ < 2π.\\n\\nTherefore, A cos θ + B sin θ = 4 d r sin(φ/2) cos(θ - δ)\\n\\nSo, the distance squared is:\\n\\nD² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2) cos(θ - δ)\\n\\nNow, the maximum and minimum values of D² occur when cos(θ - δ) is 1 and -1, respectively.\\n\\nTherefore,\\n\\nMaximum D² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2)\\n\\nMinimum D² = d² + 2 r² (1 - cos φ) - 4 d r sin(φ/2)\\n\\nBut the problem states that the minimum distance is m and the maximum is M. So,\\n\\nM² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2)\\n\\nm² = d² + 2 r² (1 - cos φ) - 4 d r sin(φ/2)\\n\\nIf we subtract these two equations:\\n\\nM² - m² = 8 d r sin(φ/2)\\n\\nSo,\\n\\nd r sin(φ/2) = (M² - m²)/8\\n\\nNow, let\\'s consider adding the two equations:\\n\\nM² + m² = 2 d² + 4 r² (1 - cos φ)\\n\\nSo,\\n\\n2 d² + 4 r² (1 - cos φ) = M² + m²\\n\\nBut 1 - cos φ = 2 sin²(φ/2), so:\\n\\n2 d² + 8 r² sin²(φ/2) = M² + m²\\n\\nBut from the previous equation, d r sin(φ/2) = (M² - m²)/8, so sin(φ/2) = (M² - m²)/(8 d r)\\n\\nLet me denote sin(φ/2) = (M² - m²)/(8 d r)\\n\\nPlugging this into the equation:\\n\\n2 d² + 8 r² [(M² - m²)/(8 d r)]² = M² + m²\\n\\nSimplify:\\n\\n2 d² + 8 r² * ( (M² - m²)^2 ) / (64 d² r² ) ) = M² + m²\\n\\nSimplify the second term:\\n\\n8 r² * ( (M² - m²)^2 ) / (64 d² r² ) = (8 / 64) * (M² - m²)^2 / d² = (1/8) * (M² - m²)^2 / d²\\n\\nSo the equation becomes:\\n\\n2 d² + (1/8)(M² - m²)^2 / d² = M² + m²\\n\\nMultiply both sides by 8 d²:\\n\\n16 d^4 + (M² - m²)^2 = 8 d² (M² + m²)\\n\\nBring all terms to left side:\\n\\n16 d^4 - 8 d² (M² + m²) + (M² - m²)^2 = 0\\n\\nLet me let x = d². Then the equation becomes:\\n\\n16 x² - 8 x (M² + m²) + (M² - m²)^2 = 0\\n\\nThis is a quadratic equation in x. Let\\'s solve for x:\\n\\n16 x² - 8 (M² + m²) x + (M² - m²)^2 = 0\\n\\nQuadratic formula:\\n\\nx = [8 (M² + m²) ± √[64 (M² + m²)^2 - 4 * 16 * (M² - m²)^2 ] ] / (2 * 16)\\n\\nSimplify the discriminant:\\n\\n64 (M² + m²)^2 - 64 (M² - m²)^2 = 64 [ (M² + m²)^2 - (M² - m²)^2 ]\\n\\nUsing difference of squares:\\n\\n= 64 [ (M² + m² + M² - m²)(M² + m² - (M² - m²)) ]\\n\\n= 64 [ (2 M²)(2 m²) ] = 64 * 4 M² m² = 256 M² m²\\n\\nTherefore, the square root becomes √(256 M² m²) = 16 M m\\n\\nSo,\\n\\nx = [8 (M² + m²) ± 16 M m ] / 32\\n\\nFactor numerator:\\n\\n= [8 (M² + m² ± 2 M m ) ] / 32\\n\\n= [ (M ± m)^2 * 8 ] / 32\\n\\n= (M ± m)^2 / 4\\n\\nTherefore, x = (M + m)^2 / 4 or x = (M - m)^2 / 4\\n\\nSince x = d², then d = (M + m)/2 or d = (M - m)/2\\n\\nBut distance can\\'t be negative, so d is positive. Now, which of these solutions is valid?\\n\\nIf we consider the problem where the centers are separated by d, the distance between the centers must be such that the maximum distance M is greater than the minimum distance m. Also, when the two hands are aligned in the same direction (angle difference 0), the distance between the tips would be |d - 2r|, and when opposite directions (angle difference π), the distance would be d + 2r. Wait, but that might not be the case here.\\n\\nWait, but in our model, the hands have a fixed angle difference φ. But in reality, if the two hands are moving at the same speed, then the angle difference is fixed. Therefore, the minimum and maximum distances would be based on the position of the hands as they rotate. Wait, but in our previous calculation, the angle difference φ is fixed, but in reality, the hands could have any angle difference if they can be set to different times. Wait, but the problem states that \"they may not show the same hour,\" which might imply that the angle difference can be arbitrary. However, the problem also mentions the same speed, so the angle difference remains fixed once set. Therefore, maybe the minimum and maximum distances given are considering the varying angle difference.\\n\\nWait, perhaps the problem is not considering a fixed angle difference φ, but rather, since the clocks can show different hours (i.e., different angles), the angle difference can vary, and hence the minimum and maximum distances m and M can be achieved for different angle differences.\\n\\nWait, but if the angle difference can vary, then for a given distance d between centers, the minimum and maximum possible distances between the tips would be functions of d and r.\\n\\nWait, that\\'s a different interpretation. The problem might be stating that given two clocks with the same radius and same speed (so their hands move at the same rate), but they can be set to different times, which would correspond to different angles between their hands. Then, among all possible angle differences, the minimum distance between the tips is m, and the maximum is M. The question is to find the distance d between the centers of the clocks.\\n\\nThat interpretation makes more sense. So regardless of the angle difference (which can be adjusted by setting different times), the minimum and maximum distances between the hands\\' tips are m and M. So the problem is to find d given m, M, and r.\\n\\nWait, but we need to express d in terms of m, M, and r? Wait, but the problem doesn\\'t mention r. Wait, the problem says \"the same shape (radius)\" so the radius is the same for both clocks, but it\\'s not given as a parameter. Therefore, perhaps r cancels out, or maybe it\\'s given as part of the problem but not mentioned here?\\n\\nWait, looking back at the original problem: \"On a wall, there are two clocks with the same shape (radius) and the same speed, but they may not show the same hour. The minimum distance between the edges of their hands is m, and the maximum distance is M. What is the distance between their centers?\"\\n\\nThe problem says \"the same shape (radius)\" but the radius isn\\'t given as a numerical value, so perhaps the answer is expressed in terms of m, M, and the radius r. However, since the problem statement in Chinese (assuming this is a translation) might have omitted the radius, but in the original problem, maybe it\\'s not needed because it\\'s solvable without it. Wait, but in the equations we derived earlier, we had variables r, d, m, M, φ. However, perhaps there\\'s a way to eliminate r and φ.\\n\\nWait, but let\\'s re-express the problem under this interpretation: The clocks have radius r. The distance between centers is d. The hands can be set to any angles (since they can show different hours). The minimum distance between the tips is m, maximum is M. Find d in terms of m, M, and r.\\n\\nIn this case, when the hands are aligned towards each other (angle difference π), the distance between the tips would be d - 2r (if the centers are closer than 2r) or 2r - d (if d < 2r). Wait, no. If the hands are pointing directly towards each other, the distance between tips would be d - 2r if d > 2r, but if d < 2r, the tips would overlap more, but the minimum distance can\\'t be negative. Hmm, actually, when the hands are pointing towards each other, the distance between the tips is |d - 2r|, and when pointing in the same direction, the distance is d. Wait, no. Wait, if the two hands are pointing towards each other, meaning one is pointing to 3 o\\'clock and the other to 9 o\\'clock, then the tips are in opposite directions relative to their centers. So the distance between tips would be the distance between centers plus the sum of the radii if aligned away from each other, or minus if aligned towards each other.\\n\\nWait, no. Let\\'s model this:\\n\\nAssume the two clocks are separated by distance d. Each has a radius r. If both hands are pointing towards each other, the tips are along the line connecting the centers. So the first tip is at distance r from center A towards center B, and the second tip is at distance r from center B towards center A. Therefore, the distance between the tips is d - r - r = d - 2r. But if d < 2r, then this would be negative, but distance can\\'t be negative, so the minimum distance would be 2r - d. But wait, if d < 2r, then the closest the tips can be is when they are on the same side, overlapping? No, if the hands are pointing towards each other, the distance between tips is d - 2r. But since d can be less than 2r, this might be negative, which is not possible. So actually, the minimum distance between the tips would be |d - 2r|. But the minimum distance is given as m. Similarly, when the hands are pointing in the same direction, the distance between the tips is d. Wait, no, if they are pointing in the same direction, then the tips are both on the same side relative to their centers. So the first tip is at (d/2 + r, 0), and the second tip is at (-d/2 + r, 0). The distance between them is |(d/2 + r) - (-d/2 + r)| = |d|. So the distance is d. If the hands are pointing in the same direction, the distance between tips is d. If they are pointing in opposite directions, the distance is |d - 2r| or |d + 2r| depending on the direction. Wait, if the two tips are on opposite sides, then each is r away from their centers in opposite directions. So the distance between them is d + 2r. But in that case, the maximum possible distance is d + 2r, and the minimum is |d - 2r|. Therefore, depending on whether d > 2r or not.\\n\\nBut according to this model, M = d + 2r and m = |d - 2r|.\\n\\nThen, solving for d:\\n\\nIf d >= 2r, then m = d - 2r and M = d + 2r. Then, adding these: M + m = 2d => d = (M + m)/2\\n\\nSubtracting: M - m = 4r => r = (M - m)/4\\n\\nBut since we don\\'t have r given, perhaps we can express d in terms of M and m.\\n\\nAlternatively, if d < 2r, then m = 2r - d and M = d + 2r. Then, adding: M + m = 4r => r = (M + m)/4\\n\\nSubtracting: M - m = 2d => d = (M - m)/2\\n\\nBut since the problem states \"they may not show the same hour,\" which probably allows the hands to be in any position, including opposite directions. But according to this, the maximum and minimum distances would be d + 2r and |d - 2r|. Therefore, if we assume that, then the answer would be d = (M + m)/2, but only if we can determine r in terms of M and m.\\n\\nWait, but according to the above, if d >= 2r:\\n\\nd = (M + m)/2, and r = (M - m)/4\\n\\nSimilarly, if d < 2r:\\n\\nd = (M - m)/2, and r = (M + m)/4\\n\\nBut the problem doesn\\'t specify whether d is greater than 2r or not. So we need to reconcile this.\\n\\nBut since the problem is asking for the distance between the centers in terms of m and M, and it\\'s possible that d can be expressed as:\\n\\nIf we take the two equations:\\n\\nCase 1: d >= 2r:\\n\\nM = d + 2r\\n\\nm = d - 2r\\n\\nThen adding: M + m = 2d => d = (M + m)/2\\n\\nBut r = (M - m)/4\\n\\nCase 2: d < 2r:\\n\\nM = d + 2r\\n\\nm = 2r - d\\n\\nAdding: M + m = 4r => r = (M + m)/4\\n\\nSubtracting: M - m = 2d => d = (M - m)/2\\n\\nTherefore, depending on the value of d, we have two possibilities. However, since the problem states that the clocks have the same radius, but doesn\\'t provide r, we need to express d in terms of M and m without r. But in both cases, d is either (M + m)/2 or (M - m)/2, but in the first case, r = (M - m)/4, and in the second case, r = (M + m)/4. However, the problem does not provide r, but since the clocks have the same radius, perhaps it\\'s a parameter we can eliminate.\\n\\nWait, but since we have two expressions for d in terms of M and m, but different ones based on the relationship between d and 2r. However, in the problem, both M and m are given, and we need to find d.\\n\\nBut how can we resolve this ambiguity? The answer would depend on whether d is greater than 2r or not. But since we don\\'t have information about r, maybe we need to derive d in terms of M and m without r.\\n\\nAlternatively, maybe the problem assumes that the maximum distance M occurs when the hands are opposite each other (distance d + 2r) and minimum when aligned towards each other (distance |d - 2r|). Then, solving these two equations:\\n\\nM = d + 2r\\n\\nm = |d - 2r|\\n\\nSo if we suppose d >= 2r, then m = d - 2r, and we can solve:\\n\\nFrom M = d + 2r and m = d - 2r,\\n\\nAdding: M + m = 2d => d = (M + m)/2\\n\\nSubtracting: M - m = 4r => r = (M - m)/4\\n\\nBut we need to express d in terms of M and m only. Since r is a given parameter (the radius of the clocks), but the problem doesn\\'t mention it, perhaps there is a misunderstanding here.\\n\\nWait, the problem statement mentions that the clocks have the same radius, but it doesn\\'t give the radius as a known value. This suggests that the answer might not depend on r, which contradicts our earlier analysis. Therefore, perhaps my initial interpretation is wrong.\\n\\nAlternative approach: Maybe the problem is not about the distance between the tips of the hands from different clocks, but the distance between the edges of the hands on the same clock? No, the problem says \"between the edges of their hands,\" where \"their\" refers to the two clocks. So it\\'s the distance between a hand of one clock and a hand of the other clock.\\n\\nWait, but each clock has multiple hands (hour, minute, etc.), so the edges (tips) of their hands would refer to multiple points. The problem says \"the minimum distance between the edges of their hands is m, and the maximum distance is M.\" So perhaps considering all pairs of tips (one from each clock), the minimum distance is m and maximum is M. But since the hands are moving, these extrema occur at certain configurations.\\n\\nBut this seems complicated, but maybe if we consider only the hour hands, as the problem mentions \"they may not show the same hour,\" which refers to the hour hands. So maybe each clock only has an hour hand, which moves at a constant speed. Since they have the same speed, but different hours, the angle between them is fixed. Wait, but the problem says \"may not show the same hour,\" so the angle difference is fixed. Then, the distance between the tips would depend on their relative angle.\\n\\nWait, but if they have the same angular speed, then once they are set to different hours, the angle between their hands remains constant. For example, if one is at 12 and the other at 1, the angle between them is 30 degrees, and as they move, this angle remains 30 degrees. Therefore, the distance between the tips would vary as the hands rotate, but the relative angle remains fixed. Hence, the distance between the tips would vary sinusoidally as the hands rotate, but the maximum and minimum distances can be calculated based on that fixed angle.\\n\\nBut in this case, since the angle difference is fixed, the maximum and minimum distances would be fixed as well. But the problem states that the minimum and maximum distances are m and M. Therefore, this suggests that the angle difference is not fixed, but can vary, which would only happen if the clocks have different speeds. But the problem says they have the same speed. Therefore, this seems contradictory.\\n\\nAlternatively, maybe the problem is considering both the hour and minute hands. If the clocks show different times, their hour and minute hands would have different angles. However, since both clocks operate at the same speed, the relative angles between their hands would be fixed. For example, if one clock is at 12:00 and the other at 1:05, the angles of their hands would change over time, but the relative differences might not be fixed. Wait, this is getting too complicated.\\n\\nPerhaps I need to revisit the problem and see if there\\'s another approach.\\n\\nThe problem states two clocks with the same radius and speed, possibly showing different hours. The minimum and maximum distances between the edges of their hands are m and M. Find the distance between centers.\\n\\nAssuming each clock has a single hand (hour hand), the distance between the tips depends on the angle between the hands. Since the clocks may show different hours, the angle difference can be any multiple of 30 degrees (since each hour is 30 degrees). However, if they have the same speed, the angle difference remains constant. Therefore, the distance between the tips would be constant. But the problem states there\\'s a minimum and maximum distance, which implies varying distances. Therefore, my previous assumption must be wrong.\\n\\nWait, but in reality, the hour and minute hands move at different speeds. If the problem is referring to the minute hands of each clock, which move at the same speed (since the clocks have the same speed), then their angle difference would remain constant if they are set to different times. For example, if one minute hand is at 12 and the other at 1 (5 minutes apart), the angle between them is 30 degrees, and since both move at 6 degrees per minute, the angle remains 30 degrees. Therefore, the distance between the tips would be a constant value. But the problem states varying distances, so this contradicts.\\n\\nAlternatively, perhaps the problem is referring to the hour and minute hands on the same clock? No, the problem says \"two clocks\", so between the two clocks.\\n\\nWait, maybe the problem is considering the distance between the tips of any hands on the two clocks. For example, the hour hand of one clock and the minute hand of the other. But since the problem states the clocks \"may not show the same hour,\" but they have the same speed. This is getting too convoluted.\\n\\nLet me think differently. Suppose we model each clock as a circle with radius r, centers separated by d. The hands are moving at the same angular speed, so once set to a certain time, their hands maintain a fixed angular difference. Therefore, the tips of the hands are two points moving around circles of radius r, separated by centers d, with a fixed angular difference φ. The distance between the two points will then vary depending on their position around the circle, even with a fixed φ. For example, if the two hands are at a fixed angle φ apart, but rotating around their respective circles, the distance between them will vary sinusoidally.\\n\\nWait, this makes sense. Even with a fixed angular difference, the actual distance between the tips depends on their position. For instance, if the two hands are always 180 degrees apart in angle (φ = π), then as they rotate, the distance between the tips will vary between |d - 2r| and d + 2r. If they are at 0 degrees apart (aligned), then the distance between the tips would vary between d and d, which doesn\\'t make sense. Wait, no. If φ = 0, meaning the hands are always pointing in the same direction, then the tips are always on the same side relative to their centers. So the distance between them would be d - 2r if they are pointing towards each other or d + 2r if pointing away. But with φ = 0, the hands are always aligned, so as they rotate, the distance between the tips would be a constant value. Wait, no. If φ = 0, then both hands are rotating in sync, so the angle of each hand is the same. Therefore, the positions are:\\n\\nFirst tip: (d/2 + r cos θ, r sin θ)\\n\\nSecond tip: (-d/2 + r cos θ, r sin θ)\\n\\nDistance squared between them:\\n\\n[d/2 + r cos θ - (-d/2 + r cos θ)]² + [r sin θ - r sin θ]^2\\n\\n= [d]^2 + 0 = d²\\n\\nSo the distance is always d. But in that case, the minimum and maximum distance would both be d. But the problem states different values m and M, so φ cannot be zero. Therefore, the minimum and maximum distances occur when the hands have a certain angular difference.\\n\\nTherefore, in this case, when there\\'s a fixed angular difference φ between the hands, the distance between the tips varies as they rotate. The maximum and minimum distances occur when the two points are aligned in certain ways relative to the line connecting the centers.\\n\\nThis brings us back to the initial equations we derived earlier.\\n\\nWe had:\\n\\nD² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2) cos(θ - δ)\\n\\nTherefore, the maximum and minimum of D² are:\\n\\nD_max² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2)\\n\\nD_min² = d² + 2 r² (1 - cos φ) - 4 d r sin(φ/2)\\n\\nBut since the problem states that the minimum distance is m and maximum is M, we can write:\\n\\nM² = d² + 2 r² (1 - cos φ) + 4 d r sin(φ/2)\\n\\nm² = d² + 2 r² (1 - cos φ) - 4 d r sin(φ/2)\\n\\nSubtracting these two equations:\\n\\nM² - m² = 8 d r sin(φ/2)\\n\\nAdding them:\\n\\nM² + m² = 2 d² + 4 r² (1 - cos φ)\\n\\nNow, we need to eliminate φ and r to solve for d in terms of M and m.\\n\\nFrom the subtraction equation:\\n\\nsin(φ/2) = (M² - m²)/(8 d r)\\n\\nFrom the addition equation:\\n\\n1 - cos φ = (M² + m² - 2 d²)/(4 r²)\\n\\nRecall that 1 - cos φ = 2 sin²(φ/2)\\n\\nSo,\\n\\n2 sin²(φ/2) = (M² + m² - 2 d²)/(4 r²)\\n\\nSubstitute sin(φ/2) from the subtraction equation:\\n\\n2 [(M² - m²)/(8 d r)]² = (M² + m² - 2 d²)/(4 r²)\\n\\nSimplify the left side:\\n\\n2 * ( (M² - m²)^2 ) / (64 d² r² ) ) = (M² + m² - 2 d²)/(4 r²)\\n\\nMultiply both sides by 4 r² to eliminate denominators:\\n\\n2 * (M² - m²)^2 / (16 d² ) ) = M² + m² - 2 d²\\n\\nSimplify the left side:\\n\\n(M² - m²)^2 / (8 d² ) = M² + m² - 2 d²\\n\\nMultiply both sides by 8 d²:\\n\\n(M² - m²)^2 = 8 d² (M² + m² - 2 d²)\\n\\nExpand the right side:\\n\\n= 8 d² (M² + m²) - 16 d^4\\n\\nBring all terms to left side:\\n\\n(M² - m²)^2 - 8 d² (M² + m²) + 16 d^4 = 0\\n\\nLet me write this as:\\n\\n16 d^4 - 8 d² (M² + m²) + (M² - m²)^2 = 0\\n\\nThis is the same quartic equation as before. Let me substitute x = d²:\\n\\n16 x² - 8 x (M² + m²) + (M² - m²)^2 = 0\\n\\nThis is a quadratic in x. Using the quadratic formula:\\n\\nx = [8(M² + m²) ± √(64(M² + m²)^2 - 64(M² - m²)^2)] / 32\\n\\nSimplify the discriminant:\\n\\n64[(M² + m²)^2 - (M² - m²)^2] = 64[ (M² + m² + M² - m²)(M² + m² - (M² - m²)) ]\\n\\n= 64[ (2 M²)(2 m²) ] = 256 M² m²\\n\\nSo,\\n\\nx = [8(M² + m²) ± 16 M m] / 32\\n\\nFactor out 8:\\n\\n= [8(M² + m² ± 2 M m)] / 32\\n\\n= [ (M ± m)^2 ] / 4\\n\\nTherefore,\\n\\nx = (M + m)^2 / 4 or x = (M - m)^2 / 4\\n\\nTherefore, d² = (M + m)^2 / 4 or d² = (M - m)^2 / 4\\n\\nThus, d = (M + m)/2 or d = |M - m|/2\\n\\nSince distance can\\'t be negative, both solutions are positive. However, we need to consider which solution makes sense physically.\\n\\nIf d = (M + m)/2, then from the previous equations, r can be found as r = (M - m)/4.\\n\\nIf d = (M - m)/2, then r = (M + m)/4.\\n\\nBut since the radius r must be positive, in the first case, M > m is required, which is given since M is the maximum distance. In the second case, M > m also holds, so both are possible.\\n\\nBut which solution is the correct one?\\n\\nLet\\'s test with an example. Suppose the two clocks are touching each other with centers separated by d = 2r. Then, the maximum distance between tips would be d + 2r = 4r, and the minimum distance would be 0. So m = 0, M = 4r, then according to d = (M + m)/2 = 2r, which matches. Alternatively, d = (M - m)/2 = (4r - 0)/2 = 2r, so both solutions give d = 2r. Wait, in this case, both solutions give the same result.\\n\\nAnother example: Suppose d = 3r. Then, maximum distance M = d + 2r = 5r, minimum m = d - 2r = r. According to the formula, d = (M + m)/2 = (5r + r)/2 = 3r, which is correct. Alternatively, d = (M - m)/2 = (5r - r)/2 = 2r, which is incorrect. Therefore, the correct solution in this case is d = (M + m)/2.\\n\\nAnother example: Suppose d = r. Then maximum distance M = d + 2r = 3r, minimum m = 2r - d = r. Then, d = (M + m)/2 = (3r + r)/2 = 2r, which is incorrect. But according to the other solution, d = (M - m)/2 = (3r - r)/2 = r, which is correct. So this suggests that when d < 2r, the correct solution is d = (M - m)/2, and when d >= 2r, it\\'s d = (M + m)/2.\\n\\nTherefore, the distance between the centers depends on whether d is greater than or equal to 2r or not. However, since the problem does not specify the relationship between d and r, we need to find an expression for d in terms of M and m without knowing r.\\n\\nBut this is only possible if both solutions can be expressed in terms of M and m. However, since the problem does not provide r, but the clocks have the same radius, there must be a way to express d solely in terms of M and m.\\n\\nFrom the examples:\\n\\n- If d >= 2r, then M = d + 2r and m = d - 2r. So, adding gives M + m = 2d => d = (M + m)/2\\n\\n- If d < 2r, then M = d + 2r and m = 2r - d. Adding gives M + m = 4r => r = (M + m)/4. Then, substituting into M = d + 2r: d = M - 2r = M - 2*(M + m)/4 = M - (M + m)/2 = (2M - M - m)/2 = (M - m)/2.\\n\\nSo, depending on whether d is greater than or less than 2r, the distance between centers can be either (M + m)/2 or (M - m)/2. However, since the problem does not specify any constraints, both solutions are mathematically possible. But in the context of the problem, since the clocks are on a wall and have the same radius, if the distance between centers is too small, the clocks would overlap. But the problem doesn\\'t mention overlapping, so we need to consider both possibilities.\\n\\nBut how to determine which one to choose? The problem states that the minimum distance is m and maximum is M. If d is (M + m)/2, then r = (M - m)/4. If d is (M - m)/2, then r = (M + m)/4.\\n\\nBut in the first case, if M is the maximum distance and m is the minimum, then (M + m)/2 is the average of the maximum and minimum, which often comes up in such problems. However, the second case gives a different result.\\n\\nBut let\\'s think geometrically. If the two circles (clocks) are separate, not overlapping, then the distance between centers is greater than 2r, so d > 2r. If they are overlapping, d < 2r. However, the problem doesn\\'t specify whether they overlap or not. Therefore, both solutions are possible. But given that m is the minimum distance between the edges of the hands, if the clocks overlap, the minimum distance could be zero (if the hands can point towards each other). But the problem states m as the minimum, so m could be zero or a positive value.\\n\\nBut since the problem doesn\\'t specify that the clocks overlap, we can\\'t assume that. Therefore, perhaps the answer is d = (M + m)/2, which corresponds to the case where the centers are far enough apart that the clocks don\\'t overlap. But without additional information, both solutions are possible.\\n\\nHowever, looking back at the quadratic equation solution, we have two possible solutions for d: (M + m)/2 and (M - m)/2. But the problem asks for \"the distance between their centers\", implying a unique answer. This suggests that perhaps the answer is \\\\boxed{\\\\dfrac{M + m}{2}}.\\n\\nBut why is that? Maybe because when you consider the problem from the perspective of the varying angle θ, the maximum and minimum distances occur when the two hands are aligned with or against the line connecting the centers. In that case, regardless of the phase difference φ, the distance between the tips will have maximum and minimum values based on the alignment with the centers.\\n\\nAlternatively, considering the tips as points on two circles of radius r separated by d, the maximum and minimum distances between any two points on the circles are d + 2r and |d - 2r|. Therefore, M = d + 2r and m = |d - 2r|. Assuming d > 2r, m = d - 2r, so solving for d gives d = (M + m)/2. If d < 2r, then m = 2r - d, so M = d + 2r, which gives d = M - 2r = M - (M + m)/2 = (M - m)/2. But since the problem states \"the same shape (radius)\", it\\'s possible that the radius r is a given parameter, but since it\\'s not provided, we need to express the answer in terms of M and m only. Therefore, the answer could be either (M + m)/2 or (M - m)/2, but since in the problem statement, \"distance between their centers\" is likely to be the average of the maximum and minimum distances, which is (M + m)/2, this is the more plausible answer.\\n\\nHowever, since the problem doesn\\'t specify whether d is greater than 2r or not, and we need to give a unique answer, perhaps the answer is (M + m)/2, as the other solution requires d = (M - m)/2 and r = (M + m)/4, but without knowing r, both expressions are possible. But the problem states \"two clocks with the same shape (radius)\", so radius is a given, but since it\\'s not provided numerically, the answer should be expressed purely in terms of M and m. The fact that the problem doesn\\'t mention the radius suggests that the solution should not depend on r, which is possible if we consider the distance between the centers in terms of the maximum and minimum distances between the hands\\' tips. Therefore, combining the two equations:\\n\\nIf M = d + 2r and m = |d - 2r|, then solving these for d:\\n\\nIf d >= 2r, then m = d - 2r. Adding M = d + 2r:\\n\\nM + m = 2d => d = (M + m)/2\\n\\nIf d < 2r, then m = 2r - d. Adding M = d + 2r:\\n\\nM + m = 4r => r = (M + m)/4\\n\\nThen substituting back into M = d + 2r:\\n\\nM = d + 2*(M + m)/4 => M = d + (M + m)/2 => d = M - (M + m)/2 = (2M - M - m)/2 = (M - m)/2\\n\\nTherefore, there are two possible solutions for d based on the value of r. But since r is a positive quantity, in the second case, r = (M + m)/4, so M + m must be positive, which it is. However, without additional constraints, both solutions are possible. But the problem asks for the distance between their centers, and since both solutions are mathematically valid, but the problem likely expects the average of M and m as the answer. Considering that in most geometric problems where maximum and minimum distances are given, the average is often the solution, especially if the radius is not provided. Therefore, I think the expected answer is \\\\boxed{\\\\dfrac{M + m}{2}}.\\n</think>\\n\\nTo solve the problem of finding the distance \\\\( d \\\\) between the centers of two clocks with the same radius \\\\( r \\\\) and the same speed, given the minimum distance \\\\( m \\\\) and maximum distance \\\\( M \\\\) between the edges of their hands, we proceed as follows:\\n\\n1. **Identify the relationships for maximum and minimum distances:**\\n   - The maximum distance \\\\( M \\\\) occurs when the hands are pointing directly away from each other:\\n     \\\\[\\n     M = d + 2r\\n     \\\\]\\n   - The minimum distance \\\\( m \\\\) occurs when the hands are pointing directly towards each other:\\n     \\\\[\\n     m = |d - 2r|\\n     \\\\]\\n\\n2. **Consider the two cases for \\\\( d \\\\):**\\n   - **Case 1: \\\\( d \\\\geq 2r \\\\)**\\n     \\\\[\\n     m = d - 2r\\n     \\\\]\\n     Adding the equations for \\\\( M \\\\) and \\\\( m \\\\):\\n     \\\\[\\n     M + m = (d + 2r) + (d - 2r) = 2d\\n     \\\\]\\n     Solving for \\\\( d \\\\):\\n     \\\\[\\n     d = \\\\frac{M + m}{2}\\n     \\\\]\\n\\n   - **Case 2: \\\\( d < 2r \\\\)**\\n     \\\\[\\n     m = 2r - d\\n     \\\\]\\n     Adding the equations for \\\\( M \\\\) and \\\\( m \\\\):\\n     \\\\[\\n     M + m = (d + 2r) + (2r - d) = 4r\\n     \\\\]\\n     Solving for \\\\( r \\\\):\\n     \\\\[\\n     r = \\\\frac{M + m}{4}\\n     \\\\]\\n     Substituting \\\\( r \\\\) back into the equation for \\\\( M \\\\):\\n     \\\\[\\n     M = d + 2r \\\\implies M = d + 2 \\\\left( \\\\frac{M + m}{4} \\\\right) \\\\implies M = d + \\\\frac{M + m}{2}\\n     \\\\]\\n     Solving for \\\\( d \\\\):\\n     \\\\[\\n     d = M - \\\\frac{M + m}{2} = \\\\frac{M - m}{2}\\n     \\\\]\\n\\n3. **Conclusion:**\\n   - Since the problem does not specify whether \\\\( d \\\\) is greater than or less than \\\\( 2r \\\\), both solutions are mathematically valid. However, the problem likely expects the average of \\\\( M \\\\) and \\\\( m \\\\) as the answer, which is consistent with the geometric interpretation of maximum and minimum distances.\\n\\nThus, the distance between the centers of the two clocks is:\\n\\\\[\\n\\\\boxed{\\\\frac{M + m}{2}}\\n\\\\]<|im_end|>\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 8}], "source": ["dataset[100]['text']"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "95_Nn-89DhsL", "colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["fdba778561bc4e87a9dbc5242799f597", "36a3431b09034b268b15d965d4801ee9", "a09e9b98dd874b259adc5b3389c5bde3", "236f4fcfb59f40aaaf4bc6d914cf21b2", "fa52d97db98c4d5f9aa3db946336754e", "60558064c9bf47d39380aa4f894f1aa0", "691e1f0d1f5f4c27be887797c9acac77", "be811034cfc940ecb37481c2ece15ad3", "3d69cb2b674a46b6867df25f241ed3ce", "b5c1f9eb08ec4509b0e706d312fd0010", "a563d89ebe2645eb99d242a3c237e571"]}, "outputId": "37f94f4b-104c-44ca-a1d7-ad3d6f2bac6b"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Unsloth: Tokenizing [\"text\"] (num_proc=2):   0%|          | 0/19252 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fdba778561bc4e87a9dbc5242799f597"}}, "metadata": {}}], "source": ["from trl import SFTTrainer, SFTConfig\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    eval_dataset = None, # Can set up evaluation!\n", "    args = SFTConfig(\n", "        dataset_text_field = \"text\",\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4, # Use GA to mimic batch size!\n", "        warmup_steps = 5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps = 60,\n", "        learning_rate = 2e-4, # Reduce to 2e-5 for long training runs\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "C_sGp5XlG6dq"}, "source": ["We also use <PERSON><PERSON><PERSON><PERSON>'s `train_on_completions` method to only train on the assistant outputs and ignore the loss on the user's inputs. This helps increase accuracy of finetunes!"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "juQiExuBG5Bt", "colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["f3296bd384d0421a847e1bba3d4e3a31", "810556e085fa4ee6a2acb3eac154d527", "dcb2718078614d5e841c58e5526a9734", "62953029af9245288f6683ff87ff2344", "d81983a822ef48b6a4ff99f18d0ab42e", "c8df180b52db4aa7bf5cf53a4ba1c305", "195a50b2fff64414bbaebe9303166e42", "f85d205b9ea34f5a92d933139a86cf0c", "3267df82d60b409d83a25a671d8b6164", "92c52665a22b4a07a25400f913db38a2", "669e5cafe438406ca066092311188d5b"]}, "outputId": "570b14ce-2d05-40be-b4bf-301e4aaba154"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Map (num_proc=2):   0%|          | 0/19252 [00:00<?, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f3296bd384d0421a847e1bba3d4e3a31"}}, "metadata": {}}], "source": ["from unsloth.chat_templates import train_on_responses_only\n", "trainer = train_on_responses_only(\n", "    trainer,\n", "    instruction_part = \"<|im_start|>user\\n\",\n", "    response_part = \"<|im_start|>assistant\\n\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Dv1NBUozV78l"}, "source": ["Let's verify masking the instruction part is done! Let's print the 100th row again."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "LtsMVtlkUhja", "colab": {"base_uri": "https://localhost:8080/", "height": 127}, "outputId": "fa23f04a-37fd-4592-857b-d7db1794e9fb"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<|im_start|>user\\nOn a wall, there are two clocks with the same shape (radius) and the same speed, but they may not show the same hour. The minimum distance between the edges of their hands is \\\\( m \\\\), and the maximum distance is \\\\( M \\\\). What is the distance between their centers?<|im_end|>\\n<|im_start|>assistant\\n<think>\\nOkay, so I have this problem here about two clocks on a wall. Both clocks have the same radius and the same speed, but they might show different times. The minimum distance between the edges of their hands is m, and the maximum is M. I need to find the distance between their centers. Hmm, let\\'s break this down.\\n\\nFirst, let me visualize the situation. Both clocks are circular with the same radius, let\\'s say radius r. The hands of each clock are moving at the same speed, so their minute and hour hands move at the same rates. However, they might not be showing the same time, which means their hands could be pointing in different directions. The edges of their hands—so the tips of the hands—are what we\\'re concerned with here. The minimum distance between these tips is m, and the maximum is M. We need to find the distance between the centers of the two clocks.\\n\\nWait, but how are the clocks arranged on the wall? Are they overlapping? If the distance between centers is such that when the hands are pointing towards each other, the tips are closest, and when pointing away, they\\'re farthest. But maybe the clocks are placed at a certain distance apart so that the hands\\' tips have varying distances based on their positions.\\n\\nLet me think. Let\\'s model each clock as a circle with radius r. The centers of the two clocks are separated by a distance d, which we need to find. The problem states that the minimum and maximum distances between the edges (tips) of their hands are m and M, respectively. So, the hands are moving, but since they have the same speed, maybe their angles relative to each other are fixed? Or do the hands move independently?\\n\\nWait, the problem says they \"may not show the same hour,\" which probably means that their hands can be at any angle relative to each other. But since both clocks are operating at the same speed, if they start at different times, their hands will maintain a constant angle difference. Wait, but the minute and hour hands move at different speeds. Wait, the problem says \"the same speed,\" but in reality, the hour and minute hands move at different speeds. Hmm, maybe in this problem, each clock is simplified such that there\\'s a single hand moving at a constant speed? Wait, but in standard clocks, the hour and minute hands have different speeds. Maybe the problem is referring to each clock having just one hand? The problem says \"hands,\" plural, so perhaps both clocks have hour and minute hands, but they might not show the same time. But the problem mentions the distance between the edges of their hands. Wait, maybe it\\'s the tips of the hands? So perhaps each clock has an hour hand and a minute hand, but the problem is considering the distance between the tips of the hands from one clock to the other?\\n\\nWait, the problem says \"the edges of their hands\" – maybe the hands refer to the clock hands, so each clock has hands (like hour, minute, etc.), but the problem is talking about the distance between the edges (tips) of the hands from each clock. Wait, but if each clock has multiple hands, then the distance between edges would vary depending on which hands you\\'re comparing. Hmm, maybe the problem is simplified such that each clock has only one hand? Or perhaps the problem is referring to the distance between the tips of the corresponding hands? For example, the tip of the hour hand of one clock to the tip of the hour hand of the other, and similarly for the minute hand? But the problem states \"the edges of their hands,\" so maybe considering all possible pairs of hands between the two clocks? But that seems complicated. Wait, maybe the problem is just considering the distance between the tips of the hands of the same type? For example, the hour hands of each clock? Or maybe it\\'s considering the distance between any two hands from different clocks? The problem is a bit ambiguous here.\\n\\nWait, let me read the problem again: \"the minimum distance between the edges of their hands is m, and the maximum distance is M.\" The key here is probably that the clocks have the same radius and same speed, but may not show the same hour. So perhaps each clock has a single hand (maybe the hour hand?), and the tip of the hour hand on each clock moves in a circle of radius r. The problem is then to find the distance between the centers of the two clocks, given that the minimum and maximum distances between the tips of their hands are m and M. That seems plausible.\\n\\nAlternatively, if each clock has both hour and minute hands, but the problem is considering the minimum and maximum distances between any two hands from the two clocks. However, that would complicate things because there could be multiple distances to consider. But since the problem states \"the edges of their hands,\" maybe it\\'s considering the distances between the tips of all hands, but given that they have the same speed, perhaps the configuration repeats periodically. But this seems too vague.\\n\\nAlternatively, maybe the problem is referring to two clocks with only one hand each, both moving at the same angular speed. But if they have the same angular speed, then if they start at the same time, the angle between them remains constant. However, the problem says \"they may not show the same hour,\" which might mean that the angle between their hands can vary, but since they have the same speed, the angle difference is fixed. Wait, that\\'s a contradiction. If two hands move at the same angular speed, then once set at a certain angle difference, that difference remains constant. Therefore, the minimum and maximum distances between the hands would be fixed based on the angle difference and the distance between centers.\\n\\nWait, but in that case, if the angle difference is fixed, then the distance between the tips would vary sinusoidally as the hands rotate, but with the same phase. Wait, no. If both hands are moving at the same angular speed, then if there\\'s an initial angle difference, they maintain that angle difference as they rotate. Therefore, the distance between the tips would be constant? Wait, that doesn\\'t make sense.\\n\\nWait, let me clarify. Suppose both hands are moving at the same angular speed ω. If they start at angles θ₁ and θ₂, then their angles at time t are θ₁ + ωt and θ₂ + ωt. The angle between them is (θ₂ - θ₁), which is constant. So the distance between the tips would also be constant? But that can\\'t be right. Wait, no, the distance between two points moving in circles with the same angular velocity but different initial angles—if the angle difference is fixed, then the distance between them would vary depending on their position around the circle. Wait, but if the angle between them is fixed, then as they rotate, the distance between their tips would be the same as two points on two circles separated by a fixed angle. So, for example, if the two hands are always 180 degrees apart, their tips would be on opposite sides of their respective circles, so the distance between them would be d + 2r or d - 2r, depending on the direction. Wait, but that\\'s only if the centers are aligned with the line connecting the tips. Hmm.\\n\\nWait, maybe I need to model this mathematically. Let\\'s assume each clock has a single hand of length r. The centers of the two clocks are separated by distance d. The hands are rotating with the same angular speed ω. Let θ₁(t) and θ₂(t) be the angles of the hands of the first and second clocks at time t. Since they have the same speed, θ₂(t) = θ₁(t) + φ, where φ is a fixed phase difference. Then, the positions of the tips of the hands are:\\n\\nFor the first clock: (x₁, y₁) = (d/2 + r cos θ₁(t), r sin θ₁(t))\\nFor the second clock: (x₂, y₂) = (-d/2 + r cos θ₂(t), r sin θ₂(t))\\n\\nWait, assuming the centers are along the x-axis separated by distance d. So center of first clock is at (d/2, 0), second at (-d/2, 0). Then the tips of their hands would be:\\n\\nFirst tip: (d/2 + r cos θ₁, r sin θ₁)\\nSecond tip: (-d/2 + r cos θ₂, r sin θ₂)\\n\\nThen the distance squared between the tips is:\\n\\n[d/2 + r cos θ₁ - (-d/2 + r cos θ₂)]² + [r sin θ₁ - r sin θ₂]²\\n\\nSimplify:\\n\\n[d + r cos θ₁ - r cos θ₂]² + [r sin θ₁ - r sin θ₂]^2\\n\\nExpanding:\\n\\nd² + 2d r (cos θ₁ - cos θ₂) + r² (cos θ₁ - cos θ₂)^2 + r² (sin θ₁ - sin θ₂)^2\\n\\nSimplify the terms:\\n\\nr² [(cos θ₁ - cos θ₂)^2 + (sin θ₁ - sin θ₂)^2] + 2d r (cos θ₁ - cos θ₂) + d²\\n\\nThe term in the brackets can be simplified using the identity:\\n\\n(cos A - cos B)^2'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}], "source": ["tokenizer.decode(trainer.train_dataset[100][\"input_ids\"])"]}, {"cell_type": "markdown", "metadata": {"id": "4Kyjy__m9KY3"}, "source": ["Now let's print the masked out example - you should see only the answer is present:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "_rD6fl8EUxnG", "colab": {"base_uri": "https://localhost:8080/", "height": 127}, "outputId": "1aa2823e-a718-4b2e-d26e-ea5dcec2ff33"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'                                                                    <think>\\nOkay, so I have this problem here about two clocks on a wall. Both clocks have the same radius and the same speed, but they might show different times. The minimum distance between the edges of their hands is m, and the maximum is M. I need to find the distance between their centers. Hmm, let\\'s break this down.\\n\\n<PERSON>irst, let me visualize the situation. Both clocks are circular with the same radius, let\\'s say radius r. The hands of each clock are moving at the same speed, so their minute and hour hands move at the same rates. However, they might not be showing the same time, which means their hands could be pointing in different directions. The edges of their hands—so the tips of the hands—are what we\\'re concerned with here. The minimum distance between these tips is m, and the maximum is M. We need to find the distance between the centers of the two clocks.\\n\\nWait, but how are the clocks arranged on the wall? Are they overlapping? If the distance between centers is such that when the hands are pointing towards each other, the tips are closest, and when pointing away, they\\'re farthest. But maybe the clocks are placed at a certain distance apart so that the hands\\' tips have varying distances based on their positions.\\n\\nLet me think. Let\\'s model each clock as a circle with radius r. The centers of the two clocks are separated by a distance d, which we need to find. The problem states that the minimum and maximum distances between the edges (tips) of their hands are m and M, respectively. So, the hands are moving, but since they have the same speed, maybe their angles relative to each other are fixed? Or do the hands move independently?\\n\\nWait, the problem says they \"may not show the same hour,\" which probably means that their hands can be at any angle relative to each other. But since both clocks are operating at the same speed, if they start at different times, their hands will maintain a constant angle difference. Wait, but the minute and hour hands move at different speeds. Wait, the problem says \"the same speed,\" but in reality, the hour and minute hands move at different speeds. Hmm, maybe in this problem, each clock is simplified such that there\\'s a single hand moving at a constant speed? Wait, but in standard clocks, the hour and minute hands have different speeds. Maybe the problem is referring to each clock having just one hand? The problem says \"hands,\" plural, so perhaps both clocks have hour and minute hands, but they might not show the same time. But the problem mentions the distance between the edges of their hands. Wait, maybe it\\'s the tips of the hands? So perhaps each clock has an hour hand and a minute hand, but the problem is considering the distance between the tips of the hands from one clock to the other?\\n\\nWait, the problem says \"the edges of their hands\" – maybe the hands refer to the clock hands, so each clock has hands (like hour, minute, etc.), but the problem is talking about the distance between the edges (tips) of the hands from each clock. Wait, but if each clock has multiple hands, then the distance between edges would vary depending on which hands you\\'re comparing. Hmm, maybe the problem is simplified such that each clock has only one hand? Or perhaps the problem is referring to the distance between the tips of the corresponding hands? For example, the tip of the hour hand of one clock to the tip of the hour hand of the other, and similarly for the minute hand? But the problem states \"the edges of their hands,\" so maybe considering all possible pairs of hands between the two clocks? But that seems complicated. Wait, maybe the problem is just considering the distance between the tips of the hands of the same type? For example, the hour hands of each clock? Or maybe it\\'s considering the distance between any two hands from different clocks? The problem is a bit ambiguous here.\\n\\nWait, let me read the problem again: \"the minimum distance between the edges of their hands is m, and the maximum distance is M.\" The key here is probably that the clocks have the same radius and same speed, but may not show the same hour. So perhaps each clock has a single hand (maybe the hour hand?), and the tip of the hour hand on each clock moves in a circle of radius r. The problem is then to find the distance between the centers of the two clocks, given that the minimum and maximum distances between the tips of their hands are m and M. That seems plausible.\\n\\nAlternatively, if each clock has both hour and minute hands, but the problem is considering the minimum and maximum distances between any two hands from the two clocks. However, that would complicate things because there could be multiple distances to consider. But since the problem states \"the edges of their hands,\" maybe it\\'s considering the distances between the tips of all hands, but given that they have the same speed, perhaps the configuration repeats periodically. But this seems too vague.\\n\\nAlternatively, maybe the problem is referring to two clocks with only one hand each, both moving at the same angular speed. But if they have the same angular speed, then if they start at the same time, the angle between them remains constant. However, the problem says \"they may not show the same hour,\" which might mean that the angle between their hands can vary, but since they have the same speed, the angle difference is fixed. Wait, that\\'s a contradiction. If two hands move at the same angular speed, then once set at a certain angle difference, that difference remains constant. Therefore, the minimum and maximum distances between the hands would be fixed based on the angle difference and the distance between centers.\\n\\nWait, but in that case, if the angle difference is fixed, then the distance between the tips would vary sinusoidally as the hands rotate, but with the same phase. Wait, no. If both hands are moving at the same angular speed, then if there\\'s an initial angle difference, they maintain that angle difference as they rotate. Therefore, the distance between the tips would be constant? Wait, that doesn\\'t make sense.\\n\\nWait, let me clarify. Suppose both hands are moving at the same angular speed ω. If they start at angles θ₁ and θ₂, then their angles at time t are θ₁ + ωt and θ₂ + ωt. The angle between them is (θ₂ - θ₁), which is constant. So the distance between the tips would also be constant? But that can\\'t be right. Wait, no, the distance between two points moving in circles with the same angular velocity but different initial angles—if the angle difference is fixed, then the distance between them would vary depending on their position around the circle. Wait, but if the angle between them is fixed, then as they rotate, the distance between their tips would be the same as two points on two circles separated by a fixed angle. So, for example, if the two hands are always 180 degrees apart, their tips would be on opposite sides of their respective circles, so the distance between them would be d + 2r or d - 2r, depending on the direction. Wait, but that\\'s only if the centers are aligned with the line connecting the tips. Hmm.\\n\\nWait, maybe I need to model this mathematically. Let\\'s assume each clock has a single hand of length r. The centers of the two clocks are separated by distance d. The hands are rotating with the same angular speed ω. Let θ₁(t) and θ₂(t) be the angles of the hands of the first and second clocks at time t. Since they have the same speed, θ₂(t) = θ₁(t) + φ, where φ is a fixed phase difference. Then, the positions of the tips of the hands are:\\n\\nFor the first clock: (x₁, y₁) = (d/2 + r cos θ₁(t), r sin θ₁(t))\\nFor the second clock: (x₂, y₂) = (-d/2 + r cos θ₂(t), r sin θ₂(t))\\n\\nWait, assuming the centers are along the x-axis separated by distance d. So center of first clock is at (d/2, 0), second at (-d/2, 0). Then the tips of their hands would be:\\n\\nFirst tip: (d/2 + r cos θ₁, r sin θ₁)\\nSecond tip: (-d/2 + r cos θ₂, r sin θ₂)\\n\\nThen the distance squared between the tips is:\\n\\n[d/2 + r cos θ₁ - (-d/2 + r cos θ₂)]² + [r sin θ₁ - r sin θ₂]²\\n\\nSimplify:\\n\\n[d + r cos θ₁ - r cos θ₂]² + [r sin θ₁ - r sin θ₂]^2\\n\\nExpanding:\\n\\nd² + 2d r (cos θ₁ - cos θ₂) + r² (cos θ₁ - cos θ₂)^2 + r² (sin θ₁ - sin θ₂)^2\\n\\nSimplify the terms:\\n\\nr² [(cos θ₁ - cos θ₂)^2 + (sin θ₁ - sin θ₂)^2] + 2d r (cos θ₁ - cos θ₂) + d²\\n\\nThe term in the brackets can be simplified using the identity:\\n\\n(cos A - cos B)^2'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 12}], "source": ["tokenizer.decode([tokenizer.pad_token_id if x == -100 else x for x in trainer.train_dataset[100][\"labels\"]]).replace(tokenizer.pad_token, \" \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "2ejIt2xSNKKp"}, "outputs": [], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "markdown", "metadata": {"id": "CNP1Uidk9mrz"}, "source": ["Let's train the model! To resume a training run, set `trainer.train(resume_from_checkpoint = True)`"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "yqxqAZ7KJ4oL", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "22b03ba9-07e4-4c09-8702-d68e03eed784"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 19,252 | Num Epochs = 1 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 66,060,288 of 4,088,528,384 (1.62% trained)\n", "`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 52:28, Epo<PERSON> 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.594600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.524100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.611000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.533700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.496300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.438300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.361300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.374700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.456400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.414000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.497900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.429800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.416200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.364800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.435700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.417300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.373200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.419800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.379100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.415100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.370600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.357900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.357200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.441000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.364600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.339800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.436500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.356000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.439100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.368400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.352400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.394500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.339400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.387000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.361200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.344800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.400900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.346200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.367000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.420500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.452400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.464600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.413800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.345900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.442000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.348000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.320700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.402900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.342400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.369400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.383900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.357300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.338600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.301200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.414000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.361200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.366200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.411400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.391200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.424300</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"]}, "metadata": {}}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "pCqnaKmlO1U9"}, "outputs": [], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model via Unsloth native inference! According to the `Qwen-3` team, the recommended settings for instruct inference are `temperature = 0.7, top_p = 0.8, top_k = 20`\n", "\n", "For reasoning chat based inference, `temperature = 0.6, top_p = 0.95, top_k = 20`"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "kR3gIAX-SM2q", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "2c018ab6-1203-46ca-c7cc-9a332b4896d1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<|im_start|>user\n", "Solve (x + 2)^2 = 0.<|im_end|>\n", "<|im_start|>assistant\n", "<think>\n", "<|im_start|>user\n", "Solve (x + 2)^2 = 0.<|im_end|>\n", "<|im_start|>assistant\n", "<think>\n", "Okay, let's see. I need to solve the equation (x + 2)^2 = 0. Hmm, so first, I remember that when you have something squared equals zero, the only way that can happen is if the base is zero. Because if you square any non-zero number, you get a positive number, right? So if (x + 2)^2 is zero, then x + 2 must be zero. \n", "\n", "So, setting x + 2 equal to zero, I can solve for x by subtracting 2 from both sides. That would give x = -2. But wait, since it's squared, does that mean there's only one solution? Because sometimes quadratic equations have two solutions, but in this case, since it's a perfect square, maybe it's a repeated root. \n", "\n", "Let me check. If I expand (x + 2)^2, that's x^2 + 4x + 4. So the equation becomes x^2 + 4x + 4 = 0. Then, factoring that, it's (x + 2)(x + 2) = 0, which is the same as (x + 2)^2 = 0. So, yeah, the only solution is x = -2, and it's a double root. \n", "\n", "But maybe I should use the quadratic formula to confirm. The quadratic formula is x = [-b ± sqrt(b^2 - 4ac)]/(2a). For the equation x^2 + 4x + 4 = 0, a = 1, b = 4, c = 4. Plugging those in, the discriminant is b^2 - 4ac = 16 - 16 = 0. So the discriminant is zero, which means there's exactly one real solution, which is x = [-4 ± 0]/2 = -4/2 = -2. So that confirms it. \n", "\n", "Therefore, the solution is x = -2. But since it's a quadratic equation, even though the discriminant is zero, it's still a single solution, but with multiplicity two. So the answer is x equals negative two. I think that's it. Let me just make sure I didn't make any mistakes. If I plug x = -2 back into the original equation, ( -2 + 2 )^2 = 0^2 = 0, which matches the equation. Yep, that works. So the solution is correct.\n", "</think>\n", "\n", "To solve the equation \\((x + 2)^2 = 0\\), we start by recognizing that the only way a square of a number can be zero is if the number itself is zero. Therefore, we set the expression inside the square equal to zero:\n", "\n", "\\[\n", "x + 2 = 0\n", "\\]\n", "\n", "Solving for \\(x\\), we subtract 2 from both sides:\n", "\n", "\\[\n", "x = -2\n", "\\]\n", "\n", "Since the equation \\((x + 2)^2 = 0\\) is a perfect square, the solution \\(x = -2\\) is a double root. This means the quadratic equation \\(x^2 + 4x + 4 = 0\\) has a repeated root at \\(x = -2\\).\n", "\n", "To confirm, we can use the quadratic formula \\(x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}\\) for the equation \\(x^2 + 4x + 4 = 0\\). Here, \\(a = 1\\), \\(b = 4\\), and \\(c = 4\\). The discriminant is:\n", "\n", "\\[\n", "b^2 - 4ac = 4^2 - 4 \\cdot 1 \\cdot 4 = 16 - 16 = 0\n", "\\]\n", "\n", "Since the discriminant is zero, there is exactly one real solution:\n", "\n", "\\[\n", "x = \\frac{-4 \\pm \\sqrt{0}}{2 \\cdot 1} = \\frac{-4}{2} = -2\n", "\\]\n", "\n", "Thus, the solution to the equation \\((x + 2)^2 = 0\\) is:\n", "\n", "\\[\n", "\\boxed{-2}\n", "\\]<|im_end|>\n"]}], "source": ["messages = [\n", "    {\"role\" : \"user\", \"content\" : \"Solve (x + 2)^2 = 0.\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = False,\n", "    add_generation_prompt = True, # Must add for generation\n", "    enable_thinking = False, # Disable thinking\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 450, # Increase for longer outputs!\n", "    temperature = 0.7, top_p = 0.8, top_k = 20, # For non thinking\n", "    streamer = TextStreamer(tokenizer, skip_prompt = False),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "upcOlWe7A1vc", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "6c6875a8-58fd-4827-a096-5262e0615262"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/chat_template.jinja',\n", " 'lora_model/vocab.json',\n", " 'lora_model/merges.txt',\n", " 'lora_model/added_tokens.json',\n", " 'lora_model/tokenizer.json')"]}, "metadata": {}, "execution_count": 15}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "MKX_XKs_BNZR"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = 2048,\n", "        load_in_4bit = True,\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "[**NEW**] To finetune and auto export to Ollama, try our [Ollama notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)"]}, {"cell_type": "markdown", "metadata": {"id": "Q974YEVPI7JS"}, "source": ["Likewise, if you want to instead push to GGUF to your Hugging Face account, set `if False` to `if True` and add your Hugging Face token and upload location!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZgcJIhJ0I_es"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False:\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "jzmjlc3gzJVs"}, "source": ["Now, use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "cloudspace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"635aa35135204b63b335e6ed2d25cd58": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_050513d95c324075b7645fa7bad4cf46", "IPY_MODEL_ea2f9cac80a14cb2b44f03bdead1f607", "IPY_MODEL_440796985da04f64b93fa7bbeece0405"], "layout": "IPY_MODEL_17508557050f494d9cb1f8c82ebcec29"}}, "050513d95c324075b7645fa7bad4cf46": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3837c92520e46548459859699833a26", "placeholder": "​", "style": "IPY_MODEL_4d0d848cdef34e25a28a202d9f955dcc", "value": "model.safetensors.index.json: "}}, "ea2f9cac80a14cb2b44f03bdead1f607": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_04893108f40c4669b625a96d24e3fdd8", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a08529dd72c04791aa2c5fc2aa959ea8", "value": 1}}, "440796985da04f64b93fa7bbeece0405": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5fe9f01020894965bd1a905f614f6640", "placeholder": "​", "style": "IPY_MODEL_fdc7e93081c2473cbb85c00eb15dd8c9", "value": " 32.9k/? [00:00&lt;00:00, 1.78MB/s]"}}, "17508557050f494d9cb1f8c82ebcec29": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a3837c92520e46548459859699833a26": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d0d848cdef34e25a28a202d9f955dcc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "04893108f40c4669b625a96d24e3fdd8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "a08529dd72c04791aa2c5fc2aa959ea8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5fe9f01020894965bd1a905f614f6640": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fdc7e93081c2473cbb85c00eb15dd8c9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5696bb993a014fb1be8e86fabed24d9a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_276f4ed59c9c426684370f37ddf473ff", "IPY_MODEL_17d7a4a2bd1e496285447bb459062ebd", "IPY_MODEL_176731ee50d745d1acc1196233ceabdd"], "layout": "IPY_MODEL_47fdaec63514405b9da598c49035db7a"}}, "276f4ed59c9c426684370f37ddf473ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_537a54d6928f48138e3d8410f29f833a", "placeholder": "​", "style": "IPY_MODEL_b826457480d046d9ade5b6c06edee2d6", "value": "model-00001-of-00002.safetensors: 100%"}}, "17d7a4a2bd1e496285447bb459062ebd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6967fc3d503403baf92e9f21b020c4d", "max": 4967215360, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c58f077e52d54a64851b4fb999576469", "value": 4967215360}}, "176731ee50d745d1acc1196233ceabdd": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_749ea0dd9af14715a437ccf73b601952", "placeholder": "​", "style": "IPY_MODEL_f0d1adfa6dc54925a94143f963bf0c6c", "value": " 4.97G/4.97G [00:46&lt;00:00, 64.2MB/s]"}}, "47fdaec63514405b9da598c49035db7a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "537a54d6928f48138e3d8410f29f833a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b826457480d046d9ade5b6c06edee2d6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c6967fc3d503403baf92e9f21b020c4d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c58f077e52d54a64851b4fb999576469": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "749ea0dd9af14715a437ccf73b601952": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0d1adfa6dc54925a94143f963bf0c6c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb718b64404d4c33b64c7e1b8ad10650": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cad9cff364a14ccda5d4103e861fb52b", "IPY_MODEL_f427b8e6cdab46cc83e4ac390a86737e", "IPY_MODEL_cdf1776551c04900bc17398881a03dce"], "layout": "IPY_MODEL_a6fdbcf3a0ff43da9bc405109acb8669"}}, "cad9cff364a14ccda5d4103e861fb52b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7fef0d9e8654445282d3af3ce5039752", "placeholder": "​", "style": "IPY_MODEL_bac0d05a338d45929200e28ddeccd951", "value": "model-00002-of-00002.safetensors: 100%"}}, "f427b8e6cdab46cc83e4ac390a86737e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70903d52b5814b8ca3d57960571d7b59", "max": 3077766632, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f9ea4edf34f04470ac33ff34349c3e0b", "value": 3077766632}}, "cdf1776551c04900bc17398881a03dce": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_073aed78e30649109c4ddde90ca5282b", "placeholder": "​", "style": "IPY_MODEL_343fa387fa384f758ef31baac9d6d4c9", "value": " 3.08G/3.08G [00:58&lt;00:00, 39.4MB/s]"}}, "a6fdbcf3a0ff43da9bc405109acb8669": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7fef0d9e8654445282d3af3ce5039752": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bac0d05a338d45929200e28ddeccd951": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "70903d52b5814b8ca3d57960571d7b59": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f9ea4edf34f04470ac33ff34349c3e0b": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "073aed78e30649109c4ddde90ca5282b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "343fa387fa384f758ef31baac9d6d4c9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6e9ef1fa47b44c9facf1c6ed797b95f9": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5549688dcfc34b74a31b0fcfb04cc8f4", "IPY_MODEL_8adfa15c86154ae08ac3a0963bbad75b", "IPY_MODEL_fe4a07a4ccad42be929068c838626c6e"], "layout": "IPY_MODEL_f5fe59b9cc574308a30049fc086aa7b8"}}, "5549688dcfc34b74a31b0fcfb04cc8f4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_780aa6fda2274519a21b8e2fd1232d1a", "placeholder": "​", "style": "IPY_MODEL_0c8041c2222446f2a9af3f719b8c123d", "value": "Loading checkpoint shards: 100%"}}, "8adfa15c86154ae08ac3a0963bbad75b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ffe0f3db0da14f23a56b27c4e3d71541", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d855f19a2b5e4c34872e302b94600bb9", "value": 2}}, "fe4a07a4ccad42be929068c838626c6e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_435cf57034564cb7b9dfca4007ccfb00", "placeholder": "​", "style": "IPY_MODEL_3ffc4b7dc8a8491da7457cfbd7d954fb", "value": " 2/2 [00:31&lt;00:00, 15.26s/it]"}}, "f5fe59b9cc574308a30049fc086aa7b8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "780aa6fda2274519a21b8e2fd1232d1a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0c8041c2222446f2a9af3f719b8c123d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ffe0f3db0da14f23a56b27c4e3d71541": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d855f19a2b5e4c34872e302b94600bb9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "435cf57034564cb7b9dfca4007ccfb00": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ffc4b7dc8a8491da7457cfbd7d954fb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d2fddc5f9bc940209c9c978afedf3b15": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e0e9784325ca423bb82160a0e5cec64b", "IPY_MODEL_e2b8f75ff4ef40519fb5253d761f5ca4", "IPY_MODEL_d2ad0349a7b945259da747b0e87b55f8"], "layout": "IPY_MODEL_0aee85df420241b8adb8981bc2b34c18"}}, "e0e9784325ca423bb82160a0e5cec64b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9063e882934c4013834b6cea28d94b33", "placeholder": "​", "style": "IPY_MODEL_ca882dc990e946ffade4746f631032ae", "value": "generation_config.json: 100%"}}, "e2b8f75ff4ef40519fb5253d761f5ca4": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_034ffe5a4c1741aea0f301f34d26f556", "max": 238, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_be792d91f24f4440be10ca69d68e9779", "value": 238}}, "d2ad0349a7b945259da747b0e87b55f8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aaa44024960843f79b733651f20bf51d", "placeholder": "​", "style": "IPY_MODEL_bd6edc2fc570405db15658911357a00b", "value": " 238/238 [00:00&lt;00:00, 27.9kB/s]"}}, "0aee85df420241b8adb8981bc2b34c18": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9063e882934c4013834b6cea28d94b33": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca882dc990e946ffade4746f631032ae": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "034ffe5a4c1741aea0f301f34d26f556": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "be792d91f24f4440be10ca69d68e9779": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aaa44024960843f79b733651f20bf51d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd6edc2fc570405db15658911357a00b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7a67f0fd9576438ab386ea1d7c491d08": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a69a9cac8d5c45e28b36cec7c52098c8", "IPY_MODEL_dd815b5e1f124314b3cf1767280197aa", "IPY_MODEL_8f9835b0135f43379c958fc3245e403d"], "layout": "IPY_MODEL_d051914a9ad4427c99243f68e3ffd2d3"}}, "a69a9cac8d5c45e28b36cec7c52098c8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5c466b4a6ab406eb6d214f4bd878af4", "placeholder": "​", "style": "IPY_MODEL_c46b62f3caba48d28ab5c8463bea332d", "value": "tokenizer_config.json: "}}, "dd815b5e1f124314b3cf1767280197aa": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1bc423dcab9b4951a158d9697ead4e2c", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_50ec1e2bc2c64c969971d3897c1f816f", "value": 1}}, "8f9835b0135f43379c958fc3245e403d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b3664df00bec42cd83cc4c9f9e65dcac", "placeholder": "​", "style": "IPY_MODEL_6ff59e0ef18d4e7590d0ca21638e6f9b", "value": " 9.66k/? [00:00&lt;00:00, 614kB/s]"}}, "d051914a9ad4427c99243f68e3ffd2d3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5c466b4a6ab406eb6d214f4bd878af4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c46b62f3caba48d28ab5c8463bea332d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1bc423dcab9b4951a158d9697ead4e2c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "50ec1e2bc2c64c969971d3897c1f816f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b3664df00bec42cd83cc4c9f9e65dcac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ff59e0ef18d4e7590d0ca21638e6f9b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1c08a939cfc5421c8b60cef99827ba9e": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1330cc79dd404420b35f894327e2d6e8", "IPY_MODEL_1c51f037d3de454fba7a2618764cd1de", "IPY_MODEL_183d8ee00ec84b4e814ec1cd76dd791c"], "layout": "IPY_MODEL_f8a545d126a34e139584995f354c24f1"}}, "1330cc79dd404420b35f894327e2d6e8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2120e08c38d24aa0b008110043fefd02", "placeholder": "​", "style": "IPY_MODEL_68799cd5e3c046cb852705315a6e34d5", "value": "vocab.json: "}}, "1c51f037d3de454fba7a2618764cd1de": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33c909d9e3314dd78b55ac095635ba53", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e10b24dfcc234dc4887b58f7a65b8b39", "value": 1}}, "183d8ee00ec84b4e814ec1cd76dd791c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ee7d96909a384441b0611a7cfce7e528", "placeholder": "​", "style": "IPY_MODEL_eb3ae807c95040868a318c778e8af8be", "value": " 2.78M/? [00:00&lt;00:00, 3.86MB/s]"}}, "f8a545d126a34e139584995f354c24f1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2120e08c38d24aa0b008110043fefd02": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68799cd5e3c046cb852705315a6e34d5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33c909d9e3314dd78b55ac095635ba53": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "e10b24dfcc234dc4887b58f7a65b8b39": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ee7d96909a384441b0611a7cfce7e528": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eb3ae807c95040868a318c778e8af8be": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a302fd0a011d425b995efb15c4c93c0b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_582ed73db30c482880c094cb0b5a7c85", "IPY_MODEL_477fadc32ae844eab8c718a9b8d15fd7", "IPY_MODEL_4ef4b46f5ec3456d83b7846ec6012513"], "layout": "IPY_MODEL_7e44e8b2cbce4897bd9dfe1b727f0a6e"}}, "582ed73db30c482880c094cb0b5a7c85": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c9e8deb4cc3407ab71f57456b22999a", "placeholder": "​", "style": "IPY_MODEL_4d42d007d3bf46c59b1e0a2d4871d386", "value": "merges.txt: "}}, "477fadc32ae844eab8c718a9b8d15fd7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b213141e6e54493cba2aa02ac23cb0f9", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cc6e6e2415654fdc981a323960500cd6", "value": 1}}, "4ef4b46f5ec3456d83b7846ec6012513": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_27dac615fca34f79a79f2a0d25dabef7", "placeholder": "​", "style": "IPY_MODEL_9c763732c6494f7fac2d05282c68c5f9", "value": " 1.67M/? [00:00&lt;00:00, 219kB/s]"}}, "7e44e8b2cbce4897bd9dfe1b727f0a6e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0c9e8deb4cc3407ab71f57456b22999a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d42d007d3bf46c59b1e0a2d4871d386": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b213141e6e54493cba2aa02ac23cb0f9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "cc6e6e2415654fdc981a323960500cd6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "27dac615fca34f79a79f2a0d25dabef7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c763732c6494f7fac2d05282c68c5f9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "607c3299d5ad4c9281bad5e4d7f7eea8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6c05bbbb3d564534a25d1e3c0f58c39b", "IPY_MODEL_ab7d3e4276454c3f8d30b64eaa9bfadc", "IPY_MODEL_d5899be3cd18490b8be50e91667ca744"], "layout": "IPY_MODEL_9d051c5781874219969f749051640ddb"}}, "6c05bbbb3d564534a25d1e3c0f58c39b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_deab123cf6ee494296aa9c5878f87c49", "placeholder": "​", "style": "IPY_MODEL_bcfbc9734084412c8f54204bad538be3", "value": "tokenizer.json: 100%"}}, "ab7d3e4276454c3f8d30b64eaa9bfadc": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3acb83f8c324e73b1a7d68f063d7892", "max": 11422654, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2512cc5d743444d98afba7539c07940f", "value": 11422654}}, "d5899be3cd18490b8be50e91667ca744": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a8d1bc6678114de9a9b3c58e24552347", "placeholder": "​", "style": "IPY_MODEL_7c50d7b2ae02480daac56cd6e89c5060", "value": " 11.4M/11.4M [00:00&lt;00:00, 20.6MB/s]"}}, "9d051c5781874219969f749051640ddb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "deab123cf6ee494296aa9c5878f87c49": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bcfbc9734084412c8f54204bad538be3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c3acb83f8c324e73b1a7d68f063d7892": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2512cc5d743444d98afba7539c07940f": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a8d1bc6678114de9a9b3c58e24552347": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c50d7b2ae02480daac56cd6e89c5060": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "54e53c126b7c4b24ae761fe5efa2fc88": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_001e41f3c6174a7c9e402274991271e6", "IPY_MODEL_d34c8ec78d6044bf8634898f88ead48f", "IPY_MODEL_57cc71fbe3b44e8fb193e7fd766c16e5"], "layout": "IPY_MODEL_a3b3c82882b84254b6d3f8e8058d0f4b"}}, "001e41f3c6174a7c9e402274991271e6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_615071e22c364826a6358725a1cecce8", "placeholder": "​", "style": "IPY_MODEL_68b11d23e2394cb186890ac5f5871b87", "value": "added_tokens.json: 100%"}}, "d34c8ec78d6044bf8634898f88ead48f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3fbdb74f73da4d74a1a195377ec68a0d", "max": 707, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_71fc3a35c7d642a6a5b769e9c37ba4c2", "value": 707}}, "57cc71fbe3b44e8fb193e7fd766c16e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df18273d629d4afe83cf193005e53118", "placeholder": "​", "style": "IPY_MODEL_f69cb7e301f74e3f8563e37824bb08b4", "value": " 707/707 [00:00&lt;00:00, 81.8kB/s]"}}, "a3b3c82882b84254b6d3f8e8058d0f4b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "615071e22c364826a6358725a1cecce8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68b11d23e2394cb186890ac5f5871b87": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3fbdb74f73da4d74a1a195377ec68a0d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "71fc3a35c7d642a6a5b769e9c37ba4c2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "df18273d629d4afe83cf193005e53118": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f69cb7e301f74e3f8563e37824bb08b4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "56a3cbc01d9946f58a5c0361ab483035": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_07e69f0bc9c64b49bece04e69125a56d", "IPY_MODEL_b475db3294ae425f98a94922748e28e0", "IPY_MODEL_f318bb164df94f1c92967b3099cc89b6"], "layout": "IPY_MODEL_d08579a243f046d1a3a53799874d7675"}}, "07e69f0bc9c64b49bece04e69125a56d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a833af05c94a4831a7329a0430904fa3", "placeholder": "​", "style": "IPY_MODEL_0fc28e2957d446a98d8b2a85068c3523", "value": "special_tokens_map.json: 100%"}}, "b475db3294ae425f98a94922748e28e0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_437a03ff1f094d709f971969579dc2c3", "max": 614, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7b62d9ad15f642c0a1e4939975a93379", "value": 614}}, "f318bb164df94f1c92967b3099cc89b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_312c0889cb4147edb01cabc3027ed225", "placeholder": "​", "style": "IPY_MODEL_d251b5d0b0bc4334a212074e4fa682ba", "value": " 614/614 [00:00&lt;00:00, 64.4kB/s]"}}, "d08579a243f046d1a3a53799874d7675": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a833af05c94a4831a7329a0430904fa3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0fc28e2957d446a98d8b2a85068c3523": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "437a03ff1f094d709f971969579dc2c3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b62d9ad15f642c0a1e4939975a93379": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "312c0889cb4147edb01cabc3027ed225": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d251b5d0b0bc4334a212074e4fa682ba": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "73051a753f194ca8a8db318650b8c283": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_967bb6e658b04444812fb78f2b06e3c6", "IPY_MODEL_54696872ef4e44478e186483c3d784df", "IPY_MODEL_d90cc22afc234cfb8e70e4be8cbd8e25"], "layout": "IPY_MODEL_00dedb764dcf4393b0dabcba385d132c"}}, "967bb6e658b04444812fb78f2b06e3c6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_290431bcea9c4e9ba94ecb5f692a0171", "placeholder": "​", "style": "IPY_MODEL_b3ba17e6bf9c44faaf2498b09d06a128", "value": "chat_template.jinja: "}}, "54696872ef4e44478e186483c3d784df": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f2263f0df7884ee38ceefab975a00231", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9de0460d76994700863cc7b95451d0d1", "value": 1}}, "d90cc22afc234cfb8e70e4be8cbd8e25": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b24007321ba3474abb115c7e25412c82", "placeholder": "​", "style": "IPY_MODEL_4d7d95bfd83b4284bdd2f9b0733fa926", "value": " 4.05k/? [00:00&lt;00:00, 243kB/s]"}}, "00dedb764dcf4393b0dabcba385d132c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "290431bcea9c4e9ba94ecb5f692a0171": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3ba17e6bf9c44faaf2498b09d06a128": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f2263f0df7884ee38ceefab975a00231": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "9de0460d76994700863cc7b95451d0d1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b24007321ba3474abb115c7e25412c82": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d7d95bfd83b4284bdd2f9b0733fa926": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3ece66bde3ba44a8bf3b074addeecef7": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_16971744872c48c5a9cac75d600f4f7d", "IPY_MODEL_d97c8c73fa704e1e942e6d35a0b1486d", "IPY_MODEL_3cc677db19664fc18c66f1dc4af79663"], "layout": "IPY_MODEL_d9fc2c3aa8584b15a419e3b503d3cee8"}}, "16971744872c48c5a9cac75d600f4f7d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f7bdec0110f2459aa511de96d91ae758", "placeholder": "​", "style": "IPY_MODEL_86c09b80b5ab49abb0b2df9f16f18354", "value": "README.md: 100%"}}, "d97c8c73fa704e1e942e6d35a0b1486d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_522f7960fa3f4600b7498dba100b2ee2", "max": 603, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_76f63b94b35b488baca933782168495a", "value": 603}}, "3cc677db19664fc18c66f1dc4af79663": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_102ad5f665be41d7819280ec10267f3c", "placeholder": "​", "style": "IPY_MODEL_4b890797eff046c9a4b041eecb39cd16", "value": " 603/603 [00:00&lt;00:00, 16.9kB/s]"}}, "d9fc2c3aa8584b15a419e3b503d3cee8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7bdec0110f2459aa511de96d91ae758": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "86c09b80b5ab49abb0b2df9f16f18354": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "522f7960fa3f4600b7498dba100b2ee2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "76f63b94b35b488baca933782168495a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "102ad5f665be41d7819280ec10267f3c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b890797eff046c9a4b041eecb39cd16": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3c1f5511bcf54527ac1ba9b1ab621a63": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_49ce971fef214698a7e574d3434eb5c4", "IPY_MODEL_c21d077eccb74320b2d775ec564ecfe7", "IPY_MODEL_67682cb1c9604820bdff8e7635b1fc69"], "layout": "IPY_MODEL_be7ade0b423e4041afca47e9b8885034"}}, "49ce971fef214698a7e574d3434eb5c4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca98fa7a25ab4126bdff4b8ef6d079d7", "placeholder": "​", "style": "IPY_MODEL_97ae3a140ba346f4ab00108cf4724e82", "value": "data/cot-00000-of-00001.parquet: 100%"}}, "c21d077eccb74320b2d775ec564ecfe7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c22fb6adc1284dc1a8faf261f7721ccd", "max": 105878062, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_16da517598e347828a535d636d82f0e9", "value": 105878062}}, "67682cb1c9604820bdff8e7635b1fc69": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5ece47b73673483496a0ee0cf6acd3c0", "placeholder": "​", "style": "IPY_MODEL_fecfe673f3e74e28a8629c5f4142886a", "value": " 106M/106M [00:01&lt;00:00, 107MB/s]"}}, "be7ade0b423e4041afca47e9b8885034": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca98fa7a25ab4126bdff4b8ef6d079d7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "97ae3a140ba346f4ab00108cf4724e82": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c22fb6adc1284dc1a8faf261f7721ccd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "16da517598e347828a535d636d82f0e9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5ece47b73673483496a0ee0cf6acd3c0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fecfe673f3e74e28a8629c5f4142886a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4d35377c209149009dace45ce9ac9d95": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9fe44f7a368b4fbba1380f3389e61d79", "IPY_MODEL_a5ede770956f4ce891f3815459b20694", "IPY_MODEL_e00cb54c2c3745158e7a1bb67c43741c"], "layout": "IPY_MODEL_a001d9502f2b4e21b58c6fdf88953d1d"}}, "9fe44f7a368b4fbba1380f3389e61d79": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_795490276c7a4c6c95728fa059ae9a6d", "placeholder": "​", "style": "IPY_MODEL_a90cec6283e6435181989cd7e3d23649", "value": "Generating cot split: 100%"}}, "a5ede770956f4ce891f3815459b20694": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6eccca21ebde465ba5b1da3e703a9c8a", "max": 19252, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3cd3c11af7cb43b4ab4db74ff5547eec", "value": 19252}}, "e00cb54c2c3745158e7a1bb67c43741c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f6750df4fa643ddab570d729dcb19df", "placeholder": "​", "style": "IPY_MODEL_1b2f5f598e5943e2a79c93f720435dca", "value": " 19252/19252 [00:03&lt;00:00, 12141.82 examples/s]"}}, "a001d9502f2b4e21b58c6fdf88953d1d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "795490276c7a4c6c95728fa059ae9a6d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a90cec6283e6435181989cd7e3d23649": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6eccca21ebde465ba5b1da3e703a9c8a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3cd3c11af7cb43b4ab4db74ff5547eec": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5f6750df4fa643ddab570d729dcb19df": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1b2f5f598e5943e2a79c93f720435dca": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "532faaa33b964e598857946bd41adde8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_44c374a356954794aa12238ea58fdd9f", "IPY_MODEL_28763b3e16c04be4be75d8425898dfb5", "IPY_MODEL_efd1ea95c79a4f47ba4c2c06f9353f6d"], "layout": "IPY_MODEL_3d1bc8b69b8c4982adb430d2bda56777"}}, "44c374a356954794aa12238ea58fdd9f": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f84d2da9e4fe4623bba549b859fd463a", "placeholder": "​", "style": "IPY_MODEL_411aac5aaa5b45989215dc75709411a2", "value": "Map: 100%"}}, "28763b3e16c04be4be75d8425898dfb5": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_322026b66f954d4695bfbac7866f8d24", "max": 19252, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d2a6f3f07d4b4e75b15c4abb85397251", "value": 19252}}, "efd1ea95c79a4f47ba4c2c06f9353f6d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0ee29972612448db9ca57d36deef3c20", "placeholder": "​", "style": "IPY_MODEL_f7132a5b6c3647179599ee2da89a8c40", "value": " 19252/19252 [00:10&lt;00:00, 2764.05 examples/s]"}}, "3d1bc8b69b8c4982adb430d2bda56777": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f84d2da9e4fe4623bba549b859fd463a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "411aac5aaa5b45989215dc75709411a2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "322026b66f954d4695bfbac7866f8d24": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d2a6f3f07d4b4e75b15c4abb85397251": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0ee29972612448db9ca57d36deef3c20": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7132a5b6c3647179599ee2da89a8c40": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff08345c14a3411bac9e13d2c2aa8988": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1dd1cc3b1ff64c349dfaaa392ae90de4", "IPY_MODEL_05ea5e52e51e40198d2d8a59bc9863c0", "IPY_MODEL_47de5f17910b45b3acced52f4cfe76f3"], "layout": "IPY_MODEL_e427d3231bd646f0aa3c385577ffc0cc"}}, "1dd1cc3b1ff64c349dfaaa392ae90de4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e41051e246ea4ed58abedacc8de1c06f", "placeholder": "​", "style": "IPY_MODEL_5fa13cf4d8994158b95f9339d1c767db", "value": "Map: 100%"}}, "05ea5e52e51e40198d2d8a59bc9863c0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8d4876643d51428c8c32ba2bcf7fc543", "max": 19252, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fb131a2471bc4c4680ddf4fec1222560", "value": 19252}}, "47de5f17910b45b3acced52f4cfe76f3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5fe585403bb847eeabe2301c1f191ae5", "placeholder": "​", "style": "IPY_MODEL_fc697c13cace4ff9ab4380e834161836", "value": " 19252/19252 [00:14&lt;00:00, 1252.82 examples/s]"}}, "e427d3231bd646f0aa3c385577ffc0cc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e41051e246ea4ed58abedacc8de1c06f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5fa13cf4d8994158b95f9339d1c767db": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d4876643d51428c8c32ba2bcf7fc543": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb131a2471bc4c4680ddf4fec1222560": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5fe585403bb847eeabe2301c1f191ae5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc697c13cace4ff9ab4380e834161836": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fdba778561bc4e87a9dbc5242799f597": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_36a3431b09034b268b15d965d4801ee9", "IPY_MODEL_a09e9b98dd874b259adc5b3389c5bde3", "IPY_MODEL_236f4fcfb59f40aaaf4bc6d914cf21b2"], "layout": "IPY_MODEL_fa52d97db98c4d5f9aa3db946336754e"}}, "36a3431b09034b268b15d965d4801ee9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_60558064c9bf47d39380aa4f894f1aa0", "placeholder": "​", "style": "IPY_MODEL_691e1f0d1f5f4c27be887797c9acac77", "value": "Unsloth: Tokenizing [&quot;text&quot;] (num_proc=2): 100%"}}, "a09e9b98dd874b259adc5b3389c5bde3": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_be811034cfc940ecb37481c2ece15ad3", "max": 19252, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3d69cb2b674a46b6867df25f241ed3ce", "value": 19252}}, "236f4fcfb59f40aaaf4bc6d914cf21b2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5c1f9eb08ec4509b0e706d312fd0010", "placeholder": "​", "style": "IPY_MODEL_a563d89ebe2645eb99d242a3c237e571", "value": " 19252/19252 [03:20&lt;00:00, 127.18 examples/s]"}}, "fa52d97db98c4d5f9aa3db946336754e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "60558064c9bf47d39380aa4f894f1aa0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "691e1f0d1f5f4c27be887797c9acac77": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "be811034cfc940ecb37481c2ece15ad3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d69cb2b674a46b6867df25f241ed3ce": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b5c1f9eb08ec4509b0e706d312fd0010": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a563d89ebe2645eb99d242a3c237e571": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f3296bd384d0421a847e1bba3d4e3a31": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_810556e085fa4ee6a2acb3eac154d527", "IPY_MODEL_dcb2718078614d5e841c58e5526a9734", "IPY_MODEL_62953029af9245288f6683ff87ff2344"], "layout": "IPY_MODEL_d81983a822ef48b6a4ff99f18d0ab42e"}}, "810556e085fa4ee6a2acb3eac154d527": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c8df180b52db4aa7bf5cf53a4ba1c305", "placeholder": "​", "style": "IPY_MODEL_195a50b2fff64414bbaebe9303166e42", "value": "Map (num_proc=2): 100%"}}, "dcb2718078614d5e841c58e5526a9734": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f85d205b9ea34f5a92d933139a86cf0c", "max": 19252, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3267df82d60b409d83a25a671d8b6164", "value": 19252}}, "62953029af9245288f6683ff87ff2344": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_92c52665a22b4a07a25400f913db38a2", "placeholder": "​", "style": "IPY_MODEL_669e5cafe438406ca066092311188d5b", "value": " 19252/19252 [00:36&lt;00:00, 552.52 examples/s]"}}, "d81983a822ef48b6a4ff99f18d0ab42e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c8df180b52db4aa7bf5cf53a4ba1c305": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "195a50b2fff64414bbaebe9303166e42": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f85d205b9ea34f5a92d933139a86cf0c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3267df82d60b409d83a25a671d8b6164": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "92c52665a22b4a07a25400f913db38a2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "669e5cafe438406ca066092311188d5b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}