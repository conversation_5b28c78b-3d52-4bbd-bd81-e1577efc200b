{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {"id": "v3ku5HNpXiSK"}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {"id": "KT9S7F4mXiSM"}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {"id": "h08vTNxRXiSM"}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 21307, "status": "ok", "timestamp": 1742164451233, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "jBcklMgcXiSM"}, "outputs": [], "source": "%%capture\nimport os\nos.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n\n!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth\n!pip install --upgrade transformers==4.53.2 \"huggingface_hub>=0.34.0\" \"datasets>=3.4.1,<4.0.0\"\n\n!pip install protobuf==3.20.3 # required\n!pip install --no-deps transformers-cfg"}, {"cell_type": "markdown", "metadata": {"id": "O_HFQBPSXiSN"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Too<PERSON> Calling\n", "\n", "<img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAuoAAAJXCAIAAABKUeZaAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAC6qADAAQAAAABAAACVwAAAAB6juXhAABAAElEQVR4AeydCdxN1dfHzfNMhJApU2YyKyRSocyUpGSIf5QKjVJJE1LmRESUMSmhwTzLmCHzLGSeh/d7n+Xdne70nDs9z73PXeej0z57r7323r99nrt+Z+21z0l88+bNRHooAoqAIqAIKAKKgCIQOQgkiZyuak8VAUVAEVAEFAFFQBFwIKD0Re8DRUARUAQUAUVAEYgwBJS+RNiEaXcVAUVAEVAEFAFFQOmL3gOKgCKgCCgCioAiEGEIKH2JsAnT7ioCioAioAgoAoqA0he9BxQBRUARUAQUAUUgwhBQ+hJhE6bdVQQUAUVAEVAEFAGlL3oPKAKKgCKgCCgCikCEIZAswvqr3VUEFAFFQBFQBDwjMGjQoIEDBzqVV65c2SnHelmlShXrpdu0dw3WKvYlrbXiPb18+XL/+hBrxWXLlsWq2VXJ5MmTvSOp9CVWVFVAEVAEFAFFIGIQcGssXa2jdTzeS62Smg4fBJS+hM9caE8UAUVAEVAEAkJg4/FExy46NNz/RI86T3QXXbs2ePMr7F7vrfT/NcTiP9hlQ4moioZzyfJufF0lKzi7uFzFJGfiyEETRwyEUyZM70uAZDnA6tb7zy3TtwpY055clGdvXLWKeU+XrVzRVeD49cuSWeKe8k6luZOlyZU0jVOmXioCioAikCARKFCqihOZKFDKjTU1Y/deKmJ1Et1iQqaWHwnvLMqLQjsEy0t1/4ryl/YGmlWnHQCt8l7SG084Co9d8CLyb5E374vbFcR/q2rKdwSCQ5sG+9Bw9S5PfNizjw8VVFQRUAQUAUUgBAj4beb9rhiCQYSRSm87j1yjn8Ko49oVewgsHjrenqBKKQKKgCKQcBDYtSGW5Z6EM9RoHYk374tg0mvTXFdw9q3a4JrpNse+pNvqvmbuW7XeS5W8FUt7KQ11Ud6KpYLVhE1V799dL1gtqh5FQBFQBBQBRSB8EIidvrjtq03zSV37km4b8j3zcd+raA1FQBFQBBQBRUARCCMEiCvt3t1byJG3xaMwGod2JQAENG43APC0qiKgCCgCikA4IqD0JRxnRfukCCgCioAiEAgCTpuPAlGldeMYAZubnpS+xPG8aHOKgCKgCCgCioAiECgCSl8CRVDrKwKKgCKgCCgCikAcI6D0JY4B1+YUAUVAEVAEFAFFIFAEPNKX4LxgLdDuaf0gIKBTGQQQVYUioAhEGgJ+v+U20gYapf31SF+iFA8dtiKgCCgCioAioAjENwKxPngrfYnvKdL2FQFFQBFQBBQBRcBHBJS++AiYiisCioAioAgoAopAfCOg9CW+Z0DbVwQUAUVAEVAEFAEfEVD64iNgKq4IKAKKgCIQrghkT5PI5kvPwnUE2i+7CCh9sYuUyikCioAioAhEEAK71y+PoN5qV31FQOmLr4ipvCKgCCgCioAioAjEMwJKX+J5ArR5RUARUAQUAUVAEfAVAaUvviKm8oqAIqAIKAKKgCIQzwgofYnnCdDmFQFFQBFQBBQBRcBXBJS++IqYyisCioAioAgoAopAPCOg9CWeJ0CbVwQUAUVAEQgFArs2LAuFWtUZJggofQmTidBuKAKKgCKgCCgCioBdBJS+2EVK5RQBRUARUAQUgVAjMGvk+wu+Ge7aypVLF0a++vT2dUtdi8jZvXnNvq3rTdG50ydNOuISWVLa6rJH+lK5cmVbCsJV6Pie/acPHwvX3mm/FAFFQBFQBBQBZwTgKJM/6X3q+BHngkSJdqxb9vvUMVcvXXQtImd8/x69G5fZv30j6T1b1nWslPWrd593K5lgMj3Sl0gf4dxPRg186MlIH0VQ+h/rZ8eD0ooqUQQUAUVAEQgQgUO7tqGhUKlKrnoO7NxC5p0lyrkWXb9+bdvqReT/Mnkk5+3rlnCeO/7TmzdvugpHUI534xWm9OXUoaOTevQ9sn2X30DfvH7j2F97/K6uFRUBRUARUAQUgThGYM0vM2mxSIUaru0unzP5rnLV0mbI7Fp08vB+yVy9YAYJoTIkbly/5iqcYHLClL78Pmrioi++2bF4ld9AX7no3sPmn8KvOveZ0vMd/+pqLUVAEVAEFAFFIFYEcJYsnjn+ngeapEqTzkn4xOH929cuqfpQK6d8uSTwhUSqtOlPHjnAytHyH6dI/o3r193KR0qm9yCWcKQvTOG6mT+Db9rMGf1G+dLZ8+lvy0r1X4Z+NfOtT27euOG3KiqePnR0088LnTQERbOTTr1UBBQBRUARiE4Edqxbemz/rioPu+EoS76fACYV6j7qFpk1v8wi/6k3h3L+7IWWRubyxfMmnfAS4UhfNv74q6z7pMnkP325eulyhhzZ4J7f9epPHMyPH7gJ5PZpOokFPv/P6YObtu37YzNrW0HU7FM3VFgRUAQUAUUgQSLw+7QvGVeZmg86jY7H73kTh5aqXi9z9lxORVyeO3UCnw3rTZXqN8MBc3jPdjLbvPIx5yN7d7jKJ5icsKMvVy5e+ubFfoJvqvRp/Qb62uUrOG+SJE3ac97Eu+vde/7kKf9Ubfzpt5l9Bx7Y5Aineilf5XerNn6/ZtMhjZ8OXLN//dFaioAioAgoAgkPgSuXLv723Rf3NmmfIlUap9HtWL+cVaFqj7RxypdLIT01Gj6RPEXK/w1yLBvVat4h/93lSRzatdVtlTDPzGRv43SycBvGrLcHnTp4pFLLhiu+mZU46b/s6uzfJxZ/OSVFmtQVmjTImDO7tdt4RNZM+zF7wXzlHq2PgBRdOHX69iIFSReoVLbLt/9xvexdt2nBkC/PHD1e/P4a93d7KkmypEYbPpWT+w+lzpA+bZZMZMKBhjXvbErRj7Z85e7OWbSQW8305K+la65evnxn+VJ5y5ZIkTqV1L1x7XriJIkTJ0kCi1o7Y+5dNe7JUTi/UasJRUARUAQUgShH4GDMxqJcBYq64rBrw0oyb7+zsGvRgR2bJn7wEvm4XjiXrlG/7+TlcJcrFy9wuW3N4pqPtiMRWcfGNcvtdDi86MuGHxb88vm4+55tU7h6RehLkiS3iMXhrTs/rN3i0jnHMt7UPgOavPdKna7tZHgrp3w/9pmXJT3lpXc7TxlGXS7PnTiVOmN6yad6zqIOKsOx/OvpxOFKevuilawxPdT7OS5J4GVZOu47aeWBHs807vtispQpmvbvde3K1S3zFyH8zLiBUtGcjeYLp8581/t9lJsiIm86ffN5/oqlyRnfpQ8LT+1GfTDgvuYsQmW7M89ba3+00iZTSxOKgCKgCCgCUYjA5QsOAzfpw5f3/vlH1px5zv5z/J9jh86f/qdum+fYF03Rx10aVaz7WOp0GU79fZhI3mTJkjfr/o64Xu5v1TlNBscjN0eh0o5N14ixnEQM79N9R7BWEFOS0E5hRF/+3rVvbIdX4BmN+/Xc9NNvIJ0sRXLBe8abH8MqYA84RWa/OwQGkzn37fhCHCtN3fvCBtoO73/m6N+Te74zsEHbfpvmZ82b++rFSylSOZwf+9dv6V+jSd/1c2/Ln3fn8rVwF+Q7ThySJW+u/tWbnD/xDzJ4R0a06rplwWKYU8kGtfCg/PjBsGK1qxW5t3Lt5xwvjzn/zynoC80Zh4qT5l+HjYe7QFk6jB+cs1ihTXN/h4d9WKdltxmj0YMwy0/jOvaCu2QvdCeRPUe27cxV4i7y9VAEFAFFQBFQBAqXq4qnZOH0sUtnT7Si8df65R//tA0ismvjKqe38V48f7bZ8/1WzZvW4KkXrFUkXb52Q3ZQb16+oGS1B1xLE0BOuNCXKxcuDm/ZBY5SrV3zP2b9vGKiY+/7iDbdWg96q1C1CgTzEr8CXyEz991F3ihZ94f+n3F5eOtfVHmoT9dCVR3rfBly3PZJ/cdhErhMyE+S3DG6ZClScIYxQF9WTHKohbughMRb634UWrrim5lwlxYfvX7vs63Jnz94DGdWeaAvJDiSJnOowkMj9IXlrUy5b7dqTpnWsWj11OgPpScsfpV+uM4LuSr8uWAJ9AVXEFX4h1OnZofWrxWvfWDTVqUvDmT1UAQUAUUgqAjsXm9r6SGobQZBWdKkyTr2/7LdG5/hdCHUIHXa9MQb4IO5dvXK7Xfe1e/blYTocpksRUqKyDxz8m9cLLflvnPYkqNum6/e6ImfvhpMzG9CpS//Bpe4HX/cZBJxMqHrayzE0BwbhfDB/PnrUtJQgXzlSxKkQhp/Blt+SKTLmpkVmX8OHCF9fNd+zovGTD4X40QpUKkMl3wrQF41CCXiMv1tWTjvXvkH51TpHLHAsBDOHMaltnneIi7hLlTkfTPS+qrJ3xvJVOkdu/CvxijEy9KnWC0cNlbNVy5cQiBDdsdWbTn+WrKahPTk5P6DpOFbjd7skeWOnFCfvWs23ZLT/ykCioAioAgoAjEIwE1uz1c4R96CGbJmT585G6EweYuUEmzSZcqaM38R+AqJTLflJJ+0F9gyZs2B26Z93/+EfnqRj7ii+Pe+EFkyvnOf1d/NKdvoAYl4zVO6OM6SD2q3IL41Zdo0J/cdEljZ8lPj6ZZnjx2HXrAEQ+aF02c4I4w/pmaHVn/+4iA9rOAkTpyYBNubOaeJeXnMyRi6c1fNSvOHfInmEnVr5ipemMjci2fOEsB7W/48SA5p9PT1a9dYJCr/2IP57ykDkcLrIy4fOBMCh7ftTJ4m9a/DJ7DCReSKVXPBKuUQGNepd81nWiVNlnTb78uJ3cHH06BXF/IhWzCn1oP7wqa5LFSl3NbfHF3VQxFQBBQBRUARCBECbGJy3ccUorbiXm3s9GXfqg15K95if6Ho36/Dvlr17Wx2AD36zktCO2iFl85xFn8M/IZ0u1ED/pg1D9cI6WK1qhLsQgK2wZmt0V93e/3ngaNJQ2Iee9cRyctiEw6b61evJU2ejJ0+WfPmksxnvx7y2/Dxm+ct5B85EBEIyv3/a0+8C6wI6sP6zkO9u8Jsfhs+gbfnCX3JXdKx2DSk8TOcOTpPHsoZ543RzDLTo/16zv/0S3pCEf6Vx955qdqTzdjExGXLgW8kT5kyTaYMpDmqPtF0ZJtu+G80elcA0bMioAgoAoqAIuATArHTF5/U+SGcNHlyKEWZR+631iXepULTBvkrOGhTkpjt0wTtInbx9NnkqVKyIUiEEydyeFnwo7y+cjZ7ktlqZNaD2g577+iO3XAXBLpOH80qjlShIWmLFSsjTNGLP3/NKhVeFqEUtEK0L+xHauUrezcbkVZMmlGkZuVq7ZpJ6IyT5rrPP80/tiCRb5iKVL+nRUNJyLlorSof7V9hbd1aqmlFQBFQBBQBRUAR8I5A/NOX2l3aunaRPUftxzheGsghZv7mDQf/MHuhY0oSCdWQInlTi+RzTpctC//k0uxgMqUkXNkDb+m1CuAKslbEK8M/qwBpq4AUOREXJ3lz6dq6KdKEIqAIKAKKgCIQtQikvbXnOBYAwiJ013sf5SV1vDfFVSxTrhxksqvZtUhzFAFFQBFQBBQBRSChIhAB9AV/BnGvOxY5XjvodLCLh5ydy9c55eulIqAIKAKKgCKgCCRgBCKAvoB+8bo1lo6fyqtcnGaCl/fDbBaPmeyUr5eKgCKgCCgCioAikIARiAz6cm+H1rwql69uOs0EsS/1X+qUNmZXs1ORXioCioAioAhEMwIFSlWJ5uEn+LHHf+iuHYj5jNHbG352K+kaTutWTDMVAUVAEVAEFAFFIMEgEBnelwQDtw5EEVAEFAFFQBFQBAJHQOlL4BiqBkVAEVAEFAFFQBEIDgLpYjZOV65865uDnpTGTl94666nypqvCCgCioAioAgoAopA3CMQO32J+z5pi4qAIqAIKAKKgCKgCHhBQOmLF3C0SBFQBBQBRSCSEMiR5t/e5i8dy+rDv6KaikAElL5E4KRplxUBRUARUAQUgehGQOlLdM+/jl4RUAQUAUVAEYhABJS+ROCk2etyrqQWL6q9KiqlCCgCioAioAhEBAJKXyJimrSTioAioAgoAoqAIvAvAkpf/sVCU4qAIqAIKAIJBoECpTR0N/Im0wRfV6kSyzcflL5E3uxqjxUBRUARUAQUgShHQOlLlN8AOnxFQBFQBBQBRSDyEFD6EnlzZrPHJnQ31lcv21SoYoqAIqAIRAoCBfSlL5EyVf72U+mLv8hpPUVAEVAEFAFFQBGIJwSUvsQT8NqsIqAIKAKKQMgQKFAqlsDPkLWsigNFYNmyZXZUxE5f8lYsZUeRyigCioAioAgoAoqAIhA3CMROX+KmH9qKIqAIKAKKgCIQOAK7Nth6dg+8IdUQCgSyp7arNZldQZVTBBQBRUARUAQiBIH54wcmPB6za/3yCIE/CN2MddOJ0pcgoByeKnInu/XRgFhvgvDsv/ZKEVAEFAE/ECDqRcx8VBl7P4CK9Cqx0xf7sS/7Vm2IdDgiq//2pyayxqW9VQQUAUXAbwTe69O96f3Bf9/umlU+ez7SJfd7EBFZMYiPynZUJb5586YnnPLly+epSPMjCIG9e/dGUG+1q4qAIqAIKAKKQKwIeAvd7dGjR6z1VSDMEdBJDPMJ0u4pAoqAIqAI+IGAN+8L6lZdPu6H0qNrNvlaa93yVT5VWbtipX35tct9ELavNg4k3TrQnD5k5VbGbWYcdFibUAQUAUVAEVAE4gCBWOhLHPQgIppYvtznVU+341JW4RYWzVQEFAFFQBFQBHxCQOmLT3CpsCKgCCgCioAioAjEPwLeYl/iv3faA0VAEVAEFAFFQBFQBFwQUPriAolmKAKKgCKgCCgCikB4I6D0JbznR3unCCgCioAioAgoAi4IKH1xgUQzFAFFQBFQBBQBRSC8EVD6Et7zo71TBBQBRUARUAQUARcElL64QKIZioAioAgoAoqAIhDeCCh9Ce/50d4pAoqAIqAIKAKKgAsCSl9cINEMRUARUAQUAUVAEQhvBJS+hPf8aO8UAUVAEVAEFAFFwAUBpS8ukGiGIqAIKAKKgCKgCIQ3Akpfwnt+tHeKgCKgCCgCioAi4IKA0hcXSDRDEVAEFAFFQBFQBMIbAaUv4T0/2jtFQBFQBBQBRUARcEEgSulLixYtXKDQDEVAEVAEFAFFIDIQGDRoUGR0NGS9jEb6sjzmCBmkqlgRUAQUAUVAEQgtAgMHDgxtA2GvPRrpS+XKlcN+XrSDioAioAgoAoqAIuARgWQeS7QgChDAD2VnlDbFULVs2TI7Cj3J2G/IkwbNDxyBeOf3VapUCXwUfmiI44HHcXN+AKJVFIFwRkDpSzjPTqB9I8QnjglBgL/IkydPDnTMWj9gBOLmnvHCdNUrHvAcelTAX6j+lXlERwsiCgGlLxE1XT52FjsUrJ+quDFpard8nOEIFvfiYvFSFA4DDpCjx+8QdNdC/OKvrQcRAaUvQQQzHFUF66c2WHq8Y9S9e3fvAlqqCCgCioAioAiAQDSG7urEKwKKgCKgCCgCikBEI6D0JaKnTzuvCCgCioAioAhEIwJKX6Jx1nXMioAioAgoAopARCMQvfQlbmJRI/rmiLjOb9iw4fLlyxHXbf86vG/fvqNHj0rdqBq4f3BpLYOA/vQZKDQR0QhEL32J6GnTzrtFoF27dv3793dblPAyhw4d2rJlSxlX0Afes2fPUaNGJTzQ7IwoWGP/9ttvn3rqKTstqowioAj4gYDSFz9A0yoeEbh48aLHstgK1qxZ89prrwWigRYOHToUWzsJpPz69eu7du0ygwnuwC9cuBC1z+jBGvulS5d++eWXmzdvmjnShCKgCAQRAaUvQQQzqlVduXLljTfeKFq06LFjx/wDom/fvuPHj9+/f79/1amFwfC7rmtFnsIZkWt+mORYB2tN+9S9bdu2FS9e/ODBg6619u7d65rpXw6OogkTJrjWhaq+9NJL0FbXInI2btz4559/mqJTp06ZdFASwRo7BKVcuXI//PCD216dPHnSbb5mKgKBIBC1TxdW0JS+WNFIUOk4u7+vXbs2bdq0mjVrjhs3DgT9iz45cuTI+vXrqZ4xY0a/p+H8+fNS/YsvvhgwYMCNGzf8VkVFuvTrr786aQiKZied/l2eO3cua9asUtcM3FdVKKHu2rVrnSqmTp06WO4cOApz8ffffzs1wSXtTpkyxRP3evvtt+vXr79161YkN2/eXLp06bfeestVif0cp7kL4thPnDjh+hLhVKlS0bd//vnHfg9VUhFQBOwjoK+ts4+VSrpBAKdLq1atVq9eTVnOnDkPHz7sRshG1ujRo0UqQ4YMNsTdiLCYQi70hQSWjzT24/nnn3cjajuL8Fge+hkUFO22mCNYmm13waMgVj979uwUm4F7FI2tAEcLZAWGkSJFisKFCydLlixt2rSxVbJbLitcZcuWda2wY8cOMu+++27XIgBfuXIl+RMnTgRzucG+/PLLN998M3HixK7yseZ4uiuCNXZuFUgMtwrdy58/f5qYg14xkFj7pgKKgCLgBwJKX/wATav8iwCec+xo8+bNX3jhhWHDhokD5t9ieymesE2gKM/99io5S129epUs6EvSpEnxBn322Wd+P/guWLCAFY0tW7agkId+aQm7Pn/+/AA1O3c6gGuIo7iazMCNMkwmkLL4gh319LpkwJk0adIff/xBrQ9jDqk+cuTIevXqYXzxyhiFgSR+/vlnqlesWNFVyffff1+hQgUZhVOp4cFUh74IlUEGFgK7chKO9RI/HKzCzF2wxg7O33zzze7du+nA77//zhKS9OTxxx9/9913hQIKuYy1hyqgCCgCviLg8w+Brw2Ep7yn3/Tw7G049yplypRm1V9WAcjxqcPYgJdffpnf+kqVKhHqaK3L4ywP3zhRGjZsmCNHDmuRMIx77rmnRo0a8BWKxNyKLSxfvjxP6lb5WbNmTZ8+HTOGr4glCWsRBobgj/Tp02fOnJl8aEH79u2NwEMPPYQ2SAz0hUxXzcRnrFq1iiUzZEqWLGnoF+NKEnNgLOfMmcN3fAoUKGDUuiZwfgwZMgRtxYoV69Gjh3TGiGHOMdtZsmSRwZJ/+vTpQoUKkbAOnLbYe2U+dJU3b95FixYZJdbEV1999cknn0gObjMQpv80feedd5IZ6yQuXLiQtRgkGzdu/Oijj4oe1zPsFtLw4IMPurpz8PfgU+nXr59rLXLgXpypxcBZOZo9e7aIeaEv0AhcKffdd59ImjOYMLo2bdp06tRJ7orBgwcHMnajmf6/+uqrcklXaQUGU6JEiYIFC5KZPHlyzl7oy86dOyHZuG3q1q37xBNPuEJkGtKEIqAIuCIQpfTFFQjNCRwBgglQgon1SdX7779P1AueG6dgHVYWGjVqJLb5nXfeYVNShw4dRDM2Q0JBP//8c1gF6dtvv10kYSEiQ3UhHFAW5KFBkv/bb7/hdahatSqXcI4PPviAS6nbuXPnXr16sYDy+uuv49JAki4RdioVzdlohkBgfdkfa4oIRsGoy0IJQaksPA0aNAirhomCSRBJ48lzsGnTJjxY0g08DRjsqVOnitqvv/6alTVZgoGuQTuEIRETKgtt1oGzWIYbgIGDVb58+eAlpm9OCXqFXc+dOzfYPv300wZbEZN+itOCeGqiXLH9efLkkdIxY8YQZ82IsLh8pop8nChO+uUSJxZj79Onj2spbJJMPD2uReTMmzePMz4M9Hft2tXIsC3IE7WCdIISMSi5cuUy8iSYYvogC20yd4GM3aq5VKlSHTt2hPCBIeSbO9laKhjChslcvHgx423WrJl5cIL6NGnSBABhjTBOWM5zzz1nra5pRUAR8I5AEu/FWqoI2EdA7KgnC+1WD24Jlo1at27doEEDBKwPoBgDFMIeoBdYRyyEuHngOvCV2rVr41CBZ2CQ8NXj6sCwoQHywRk2cP/998vemREjRsBduCRQlOYolT0sVMFmwwyaNm2Khe7WrRsUaunSpQg888wzUJkyZcqQdtrIbdXMozzcBcrCmV7xuWzoAt4IPBNU5MCVwpoathO/C+e//vpL8p3OZ86cEe7Cwg1P5JA2s10Ffwy2H5JB0fDhw2E2DEeq4+uS4FDrwI1ppwqWEgbj1Ja5ZF2JSFjaJefs2bMmXxLCkBg71AGQARwaAZuhFGoFd3nggQdwgMmSH5g4VTeXROaSdvWIoArM7733XienmlSEV+Gzgavh+uKWEOoGf6V0z549IuN6Zshk4u/hbX70dvv27VzixWH2aQgXkZk7v8fu1ChLbMwOdy8KmUSnUsEQ+jJ27Fh8P9999x0ES0KYAfbZZ5+F/+FuBAcSsornpCFEl07PCSFqRdUqAqFGIHrpi/4NB/3esu6FsaMc5gFLQLJ69eoYSJwT8BUeSfmJ52GUQBM4CgYMTwlmEjMGP0CYlRrO2CceW+EZbG9GD6ZUeIaQJyExEpRAOA6s4tNPP4Vn4NiHHwhVwkDiqMB9QmgF26bEfpiFMJoQVWYjlURjWDVjvRBDM4Y2U6ZMjz32mFhroS9QEKowCsaIBwVJ6zZgLs0hA6cnuCJoFIcNw6EUs/3RRx89/PDDDIEiGY7xyoCVLE9YB447Cj2sy2Av27Ztu27dOtOK24SMUQgQAhA70SY0iEU9Vm2efPJJyB/eAlnQoXtILlmyBG+B+E4eeeQRt8ohWCxjwZDMgpoRo2OAA9UzOdaEOLTAE7TF+8WSHwtzyHiigBSJAAlWZHB1wFZhqLjTyIEKE/tinTsy/Rg7tdwerOgJd6cUvsKiJwlpjhubcGOoJLcBMjNmzKAIRo4MjBaEa9WqRYL5cqtZMxUBRcATAtFLXzwhovl+I4C5kp9s0YAt9LLwz8oLD6Ai2aVLF9Z3+BHnkqdkeIY8pPJsKiaTBSmiCrDKCMjzN0+0GCcuZRkIefENSPwNGiiSF4pALDDzsrBFphgtEqwNccbG87yOjZEYEcye4Svp0qVDQMw5BhULRItWzVKULVs2xORYsWLF/ycTyctUoF+YKJYzoFCyM9wImIT4nGiXnpApETMkoAicQQnrCGnDKnMJSugRSaEd1oGDPyNCmEYhHPADFoZwZlDR7WG8LJSiE74IFKRlyQPuAkeB3jFBZAqenHG94AuhLSYLwieYuOqHVpIpATpOpUIWcVo45XPJQhVrRiSAjjOeG0w+vjfZoCTk1bUWORK1g2bW17jE00ZEMh4XzrKcZJ07BPwYu9t2yeQeM/QFfwyUmkwJqQYf2C0uFtkEJzHIRIXTGZgZi54sNc6cOdPVQeWpLc1XBBQBQUBjX/ROCBoC+M/FH4BG/BAEf+A5wB/g2gCvtsPljneBt6qzsMIyzV133YXVwW7hIMF+G1c8Glgbgp1ALyT0VYwxvhOYDTGPEqaKMZCVFHlpHuaERsVfgmFgVQgTQsQD6wuYGdZKCFmQhRWUY2ZwxdEQDAlTjb9EDKcE8WCDsXOwJQJKoD5WzbKb5sUXXyTuEobBwhO9IvpVDBVkC15CWAPDoTMIEwDhCgU5skqFhcZOI4Yqeohhoz+U4mfCr4CFY/h4VmQZAs8TRbyZhrN14HPnzmVxh0UKaAc8hm7jv+EQQoCw00Hf6CRV4EA0gUuAhQxkhCmyZsfiF5c0h5hwDhLsBuKFfsyg0QaxM7zQZAq7eu+999APgcAdxbIOtBV3jvBaqBVRvdBEZo0WuXkw/OJ6AVKzhV5iiRBjEmFUQApEphWTAHnSwrRYCmTdDe8aUykLZBRZ545LP8Zu2nJKcKvQFhT5wIED9B+nIAJy+wEXPRFaT2eENDNl4qEhLseocouhKdWEIqAIOCPAI1cUHvyisRKRsAdODCOmKC7HCFFgHUFaxIrQOss0bjuAZaWUB1NrKVaWTHgPmTw0k4YNEJ5CggOzjbWmiBzWLAjtxFyRzxmHAfn4Qkjz9R/RyRSzu4Q07ISokTp16sSoccizKoELB2tKgkysIxYRtw2WlWUsei4acHJIFTnTopNmLtFMdREgeJPAFJSIGJ23DhD6RdN0RkqdzjyOC2JGFbtjoBQ4OSQHEsP7iKnFGgR6KGKkFKHQOnCZdASgUFAxrLhUxzQ6tWgu4Toiw5mwG+khy1VcslxlxGBCgMOlTA2jxiqzqEcTpEHSSJoEquB2Rrk1AXPlVrHmSJoOABQKrU0bhcCLGETB5DglAAcBbiHw4T7hNmOWrTLmrpBMX8duVWVNg4MZC+DLTLFARiYRWkZSoIPW4JiUGxI+CtTMMn87CENbjXCIErSS4H/6QgRdWKmN+5/3sBq+dMbhMY7CQ+lLKCad5RKWNkQzv9Q8WRJo4rYhHOwEgjgV8eTKvBCyQD7mnN9ZgkJIQwhkYUXkcZwIScI64omRTDnTHAGbkmb5w1qLTEwaD/1WedI894vBlnxkqGhkcO9jZohdpT8m01Uz1ojDCHhKeOEQpoqrDI4oHuuNAAkZ1/Hjx1mJkHwzcPr/008/QQJATw7SuGSs1Z3S1MXVQfwyrzCBCZlSp0YZNb+YUoqzyhh+OA0chek2FZ0SuLsgK/BFOozLgeAVAybcgtYx9uKYIV8Mv5MGc4kTDo6Ib8PkOCVoC5+cU6b10mnu/Bi7VZtJ0y5+I1g1gVB4mEy+E4bks7woIPPHAm5wHaYJusagfvzxR1MxdAmaU/oSOnjjTLPSF6BOzH/ODpkouG7RogXv4eCJNmGPlfUR2X0TccOEhbAwxNYeojGcOs+yCD/9EgLpVKSXBgFoEDaV9Rf/3lFr9HhJwKKggxK/7EUsuEXYfjgBL0AOrtr40gaADMfti/tC1CV+E3irUIL/6QsRen6oZZsetQjf9qOulyo8QmDFIvTn3cu4fCpyrMrroQiEGwKynxaXhmvHiF+RmA/XIs0xCBCJglEMHXehIXYnxTF3oVHikBIMd2E4BPHEJXeR28O8e0Yu9RxSBHg9AQd/iWwxEyoT0uaiSrnSl6ia7ogZLL/pxDziIHXt8R133EH0gOzrcS3VHEVAEVAEwgcBVsChL/SHqG3hMZAY5TFBmSClL0GBUZUEHwGCGYnBZAXESTXbT8jhJWBO+XqpCCgCikC4IcDvGCtHBGnAXTjoXsz/Hf4YSIzsRAu3PkdKf6KXvrh9so+UaYuGfsruJGJRnQZbtGhRfhHkpSxORXqpCCgCikB4IgCJMTxGegiPYUVJeEx49jnMexW99MX+xMgdxk0WzkfCY/Gs0LOH1nzDyDpf7DXllSHWHE0rAoqAIhARCAiJMYtK9Bkeg3HBGcMREUMIk04qfYl9InjWj10oXiXoYfh3Ml4RCqPGiTtm64e8tcxTt9jUw6cfEePlubxNzpNY5OazwVheTxfrEICLHfhAwYvyYv0GQqzaVEARCBME+MU2zhjoC73izKE8xv4ERe/GaTCSF7baByviJCN343TEQW2/w7xcmBfJ8M433gLsthbL5LzQVj4eKQLcqAlptwgD5AX/vIAE75pbBEwm74PhvTLW+CdeFxT3251MfxJAgt+EBHY7JYBJkSHgesGJbvWjw2agOG4HqBungSWZW2gSfCYvfdHYlwQ/y3YGiCktX748Lz+VDwXYqRKgjHwJ2ct3iHjvHNwFBsN3oHgU4xuNWPoAG3WtzmvW4AG8mde1KNQ5vNiNJuQTV97b4tXDcBc+WsTHm6jFi4/NVym8V3Qq5TtKfJ6J6vJVaqfSqL2My6UKPvodtTgzcDvecZiKkBXmBeJCLc4c1OWQomjG0HXsUUpfXIHQnKhFgHUcuGyc0RdZAXH71R6ZAr4oRIJPJUt4MiHMoZgaFmVc33nFihXvxuVjVfKdplC0i07ersuZjfGx6v/+++/5UiPvYkYyT548vOqXt/fy6UpeEyxfffKkwWkgvBoOGsQLna30hc9E8CVqvifFR8496UnA+YT0WR/0E/BII31oMR6Z3+AxhMvAYyJ9OEHsv9KXIIKpqiIVATwBkBheJ4O3g88gh3R5gq/5AJP5HqErZHwZkaWiOHibGaPmxYCMmlf08i44DnHG8EFB+eqka9+CkiOf1fT0nWrTBJ8IYFL4qKfJIbFw4UKCZvgUOT6q7NmzW4tMmlfZuh0IdI0PQ6KWDyjyAU4mgtc6U4uvY1arVs1Uj5IEtjAuvS9RTpWCMnzlLk5/m0pfnADRy/hHAGvKfmn5SG/oekMrfOVHyARfAZTPO9Mc36D29H3moHRGIjnktcJuFfJefCiU26KgZC5YsGDNmjXiAjE+DMw5n9rmM5P4NrwsbAWlA/hC0MM3qL1rI34ZAZwuVrFmzZoREEPQDMzGE33Bs2UGwlgmTZok38pmEYpDtI0cOZJPFPH1H0oDf4kz39besGEDn3OqWrWqfBfd2uewTcflekRcthW2gNvpGERHgmCMsKwcKXcxgJiE0hcDhSbiAQEiMWfPnk0gJ2s3xudBWAafUWSnCTSCL/CVKVOGb0fjF8FPwCoPX3ZkQYHMLFmy2O8x3+rjJXisjGC0+E4eFVevXk1wiWhgIYNAWhhMiRIlChYsGKtaesJb+emAWQOCDLHgwoG9nDNnDsFVTmaMD0/SCgKinDUL11bwDQwaNIhvNLp+LYEYna1bt0IyaJeKGG++kk3IDi/R5xK4CArhzGf/6BXhrkY5zgbChMGZUbN/hz6wBGNKgZ3QH0gMmskkLQ4JI0DFVatWwSSQoc/SHKWxjtdoMAn5XCI74YWXWDvJpxOhTXiD+NAVC0N0cvHixRJZDxpGAwk6wPd6zCd7PHXDDIToGW4e0ZAzZ05mmYEABbccmXwom0NKOcOriDpiyNBHxIhyNUWCMJd8fhLyxBcu6SSXRNUwCyYGOdTc1/RHEwkMAWUt/kwoP4tRePDUxYJ6gh84IZ/hPMbp06fTQzmwKFAT6W27du0aNGjAZ6UpIi1sZsSIEf8ve+v/mDeRx0LzRI6BYTuuZPK0jQYIigjgz4C1mOpdunTBguIFgR59/fXXNE0rNoGaMGECj0GiChvGY7dUxKCiBNqBVaaUM/2RIr7GzGeryaQh9hyR8PTNZMJiRLMIo/Djjz+WT3ADDpmExYhOdlNzCbHgkiGjmeF07txZqsMGRAzyR5FkcsZUkz9q1Ci+pM39T46IWc/mk92MhU8im7ok+DCydAZ5L+O1apM0JAOyJaqYCPlatflINaOgiH62bNmSBDyGWnhHRJ4zX5lmyoYNGwa/oQgOynSLZi/dkIHANnjuF22cpZY5E/7CipJc4pRigKZREkwW94mUUkT19evXiwDIkA9EAi8TCgvnfqNvRnl4Juh/eHYsOnslb3+xelZIk8nhHRD5QfAuk+BLEyX4EbodoNIXt7DEZSaGn59+zPyKFSv46RfLsX//fvrwwgsviJHAIS9dWrJkieRgqs+ePcvfdseOHclhHhHAidKhQweCIUSGzyGJUcHmSXUMNkU8hWN9iUsljWWSIs4QHbHrJsdTAnpE3TZt2tAWLhbS0gHksaNYWTHMwm94iCcfaiVDo5OsXAiLIrLEUxMMWQgQ9EIStIJlJRaVhPlRk+HjokAPmFAkxEiaZm82+fh7BAd6C5fq1q0bpabd9957j1qGfkk+dIdMwmO5ZGik6TwThEuJDVAARQ4LbZR6Gq/Rb00IbrSOb4moFJRwCMeiAzTBSHFo0UkSwu1gPIIwQwAKwZBauJ0QgNGKfk/dsA4ESXgGdeGC1l6RBlU0k4DgOvqUNy9chyaojk+FSxgqkTQIIEa7TDGZnOkV65t4hiSHIkMZnZoIt0v6H25dis7+8LfMX4RxOZDmMH/gsWKi9AWIope+RMOfcTiPUR5kzQMxNpLeYtu4KeVJHbZh/oahHZR+9NFHJocEVg02QIJNOlgRY+HEbcNvATYGK4iAsApxh2CN4B9og1iINuRFD5c8bXt6gMY+UQtKJDo///xzLjG3okQ+cUAOnYc/kSD8gqLRo0eTxllCGoPXqFEjLk3HpK7TGZ4hNpV8OrNjxw76DBpUJEqDTEPmVq5cyaXxS2FrcSfQJSga+TNmzKAKbygmzYESDklz5l1wlMJLJId1KxIshZApv6GilmURU4W1FUqx6+R4Gq8RNgnWjBgv1IfqZKIcJRz4sbgUWsYla0b0nIS0LtWpCJclDXQsGgIsaaaAKRMBT92wDgRJa8+5ZNTC2+gDLcJCyCQBi6Uh0cxZWBfCZFIqB1RSgBWqzQQJ0aH0tddeo5Omengm6Gd4dixKesXtzU+TYS0kfGItBiWlL0BxayXeiqamFYE4QEB27U6cOJGFAJojWIGzBFFmypSJtPWPXKIlrKGaSPKIbPrJszsPzcQrkAMxgjSwY4X1FDLJkVetEKRJmugT4itJSBEJmpNwWtJ9+vThyZ6E6wFpIPPZZ58l3oX1DtY4uGQ5g+ZIyBewCSXhPbkEpRJmIfksXlAqu7JxArE8RMwEzXl5gSybkugbJImKbM8pVKgQfZZAZpqDhBEVRBGHQCevUcEswQVTpkwJCaB7lEpwhix/cIkSDke1mEN2ZeN14Iq9POx1okXZDURgL5lSlC1bNpHnDMU0aU/jNQImgYFnvASd0B9mDS+RFBGmQ4IIYhplMYvtP5A2eJ513imS0RH5xJ5nifZFEmxlyjx1wzoQWpGQHRkRv3q1a9eGuJAvo0MbqHJJPDUNkeAADdktAucTnMmEqVC3YsWKpFn84kxDsBbuDd7Tg9OLW4vN2FShSA9FwCDAvSQfnzH71bnPham7EhpTK9YEL6+LVSYBC2jobgKe3LAemoRkYt0xtzxDS/CjmBOJTuWp1wygePHipHFs8ChPaCrP1kRCYA5Z4DAyrB9hmTBFLCgQASo2mHBXSI8wFcwMm2uIx8QhQS3z0UcUsiDCAzqPzhhyFgKMTmtCtsCgnAhWrCyRuRgzHDl8+xrahPeC/tBDoQhYOFZ2qC4cgmUjTCCrPNTC38BPGFyqQoUKVv0mLTG/EDLrphvcSwjAjTCrkJjx48fjrsBq4jfCjUQRKyOZM2cmQfcghQyZ6GYu33nnHbwF9AfWxbobth9/BvmMmjOuHUz72LFjJShYiCNNUyRGmiAPGqIuMa34k3CHyJ5qT+OlotMh1IE4J5RAU+jYgAEDmBdihNn2xSZtmR28QaYiY5R7gM9zWkmqCLDBmwSsCGw9dcM6EISZFIQ3b96MH4W5o0XYHvlCiGFIBD4zHT/88APkgzGCAJhwxgEDQcHxhjDUhBuVBCwKbXDE+vXrExoMVcVtw9TjFsJJxp3J7QpdQ1IPRUC2EQkVBg1YCwuUVo6uEPmPgHFGRVVC1vUT/JDD2VGMeaB7RDtKWCtp6AXshEmZO3cul5ytEyTPKOTLwY+ChGggg1HkwR3+wRoTVlwWiThjVNgajQBVWJYi8ELq8tuB7THKJTJGijBdsihgSk0Cy0cPRQwSI2JEy1KFIkw7xs8Iw8bIpw/WaFyMHyQGGUKGKTXCTgmCZmiF6GOnfKKMGSbkQyJY0SZrKBh4E4JDFd6qgnIJjMW3gU2VPnOGA2FuRS08z+ST4I20ks+SnFm2Gz58OC2KGHVZTiKeRsQ8jVdKnc4SqYMeOgaPoRSvCWlmh6gUuQHggtBKEGZ2kIRpIQZLIy0TanSK2xzvCzleumEdCJKygChjYQlPdMKhyZEQIriUxA6LDCyWR1tpFDoFu4I0mz6wjil0WW4qKPgrr7zCPQnVozptGclwS9C9cOtSwu6P2Gbuag5xtwRlvPJXwDko2iJUica+ROjE2ep2OP9UYavonlgpjDoWwgwJNoDlcDJaUgpHQdi1yDUHeXz4qCJBQzxVkyDiAQ2iypwxhCwY4cbgUZsHcZPvNoHBc9JgNqc4yZsRIU+QCi8FsQoYHmDNNGlhOebSbYIh4x5wW2SaNqWuORRB+6AOrObAcoykbG82lyRgGBzWHLdpt00YSdw8UFWJMpFM5kLYKlQGHw9shmmCLcEbWFcSGabPbdNOU2BaIWG64TQQPChwC8KD4EysIpkqqJKbRHIoAnxCiIyA2wRVJJCIUeBOk6gd+s8Bd7ESHbfV4zGTHsZj61HYtLwtN+gDV/oCpFH6yUaemXhgdX1puv9erLCsyUJJ2I4RXwKBJngOvLx/Nlig4szHV0GEb7AUqp6gIwAbgEnEwbuGg95zUQjvgdPIWmGImgiK2nD+TQjKAKNEiXyykfXohPQxV1/n7t9QPl9rqnyYIxDmUV0EYQAgz9ZxACNPnBJgEQdtaRP+IUBkTORyF4ZMiE/4cxf/pkZrKQLhiYDSl/CclyD0KsxZuewiYUEhCEONTQXBsASj6H6Q2HDSckVAEVAEIgaBKKUvYW7aI+b2CaCjbHXhaZUlpAB02K3KK/wJEyEAxW4FlVMEFAFFQBEIbwSilL6E96RERe/YGcv72WT7bqgHLK+LZY9uqBtS/YqAIqAIKAJxg0BUv/eF6BB1w8TNfea2FXYdu80Peiav7pD3ygRdsypUBBQBRUARiBcE1PsSL7Bro4qAIqAIKAKKgCLgPwJKX/zHTmsqAoqAIqAIKAKKQLwgoPQlXmDXRhUBRUARUAQUAUXAfwSiOvbFf9gip2aLFi0ip7Pa06AhwGaroOlSRf4iwKtR/a2q9RQBRSAWBJS+xAJQRBfzyt0gvrwuiKqsqIbiJz5EXbV2Ow7SgcSVWz9mGdKuBtLJYHUsYUy3FQ07qHphqHaqW5vTdMQhkPDueT+mQOmLH6BFUpUg/pAFUZUVQT5/aL0M/3Sc/XAE0pAX2xZchEPBPn3tYY8ePXytEoi8338IflcMpLdaVxFIqAgofUmoM6vjChUCcWaE4qyhQJCKOPYZyGC1riKgCIQPAhq6Gz5zoT1RBBQBRUARUAQUAVsIKH2xBZMKKQKKgCKgCCgCikD4IKD0JXzmQnuiCCgCioAioAgoArYQUPpiCyYVUgQUAUVAEVAEFIHwQUDpS/jMhfZEEVAEFAFFQBFQBGwhENX0JZCNqbbQVSFFQBFQBBQBRUARCAECUUpfImJLagimW1UqAoqAIqAIKAIJAYEopS8JYep0DIqAIqAIKAKKQLQioPQlWmdex60IKAKKgCKgCEQsAvrW3YidOu24IhAnCIQiRCwUOuMEjMhoJB4Xx+Ox6ciYG+1l8BBQ+hI8LFWTImAPgViNd6wC0o6v3xuyqdbeIOJZKlLMZELCPJ6nPKb5UMx70D8QFngnA9cQDpMV6j4ofQk1wqpfEfgPAtizFi1a2P958vLb6qWIJu038Z/+ebgIrjYPjWh2fCJw7IK31pctX+6tOK7KXOmg927b6dfG43akHDK7NiyzIzrzF1tiblVlT52IMU6ePFn/4tziY81U+mJFQ9OKQMgR4LepdccePeL2O9tHLwY6ro0nAtXgR/3ALZMfjQZe5ahXHhC4/vjRkLty/LT731ZLNonPbtRJ1P2/3Qn+1f15Ek0cOYhfCaUvsYKr9CVWiFRAEQgyAljl+fuDrFPVKQKKgCIQVQjozqOomm4drCKgCCgCikD4IhC4ozR8xxbsnil9CTaiqk8RUAQUAUVAEVAEQoyA0pcQA6zqFQFFQBFQBBQBRSDYCEQ1ffF132mwwVd9ikACR+CXrz9d8/O3oRjklqU//zC8Xyg0q05FIN4RUNtkZwqimr7YAUhlFIEwQeDKxfOXzp0Jk87Y6cbNmzfnjfsYBmNH2FeZtfO+Wzxt9Onjh32tqPKKgCKQMBBQ+pIw5lFHETEI+Ppcdfni+Z++eP+tRsXfbFS872MlB3eqf2TXnz6N9urli1M/fmnvljWutbauWDDr8zdc88m5fu3qxoU/XLtyWUpRYtJu5V0zz544SubJw/tciwLPORwDwvlTJwNXZTTAt95tXo5RmxxNhA6B7z7q6eneC12jqjkhIaD0JSHNpo4loSFw7eqVz557+PfJw9Jmylr5kbZ5i5WFu4x97akb16/ZH+q+LWtXz51y7fIl1yq/fTP00F+bXfPJ2bt59cR3ukz95CUpnfz+8+80K3vi0B63wm4zD+3cQn7K1GndlnrPvHr50vdD31o7b6pbMbxQwoqSJgvyqx/OnTqxa73/7xxz21vNdIvAmeNHtq381W2RZioCdhCIXvqiLwWyc3+oTCgQyF/a7qu3ju/fefzAruz5Cncf+XOjbv06DZpWtFJtVkyO7N5mv2NH9+5AOFfhu52qXL92DTJ0590VnfLl8uCOTST++GXm2ZPHbty4/tfaxfiB/lw+362w20wqkg/xclvqPfPEwd1LZ3z57Ycv7Fy3xFXy7D9/S2aqtOldSwPMgRidP33i0F+bIHZXLiXIN9AFiFDQqgP1hbOnuAkP7tgImwma3khWFKGvaowXyIP87BIvY9BGFYGEisDtBYo1/t+7WXLmTZ4yNWNMnDjJ7fmLbV3xy9UrblwpnkDY8Pv3+UpUSJ0uo5PAno0rYCSFy9d0ypfLY/scpIcDbwTdQJL0lYs+mPPLF89RJVP2XA4tPh602PzlgSvnTDrtzqpJZ1CZLvNtdhTjNNr357okSZOWuvdhMHStgjdr1Y/fHD+wm6Ltq39/p1k5kan08OPg7yofaw5rbTJlsUqKAB04snvrwe0bs92RP38pu+zWpnInsVjRcJL36dLOwFmy3Lt5zeFdDudcvyalRT8cvccoH8ix/V6FdLz2u2Ff0tf1ZfuaE5ik0pcENqE6nISGABbUDIngDIwrl5lz3GEyvSdO/32IZaCGXd1s0tm4aA4rO3fefY9bDXs2raIUorB99cJ/jh4UmZs3brgVtmay4EWUDH4RiZUpWLa6KeVRe9G3I/5atyR/yXuqNHwy8+15TNH632b9sWA6+is+2KpE9frkl73/Mf4ZARI3b964ePZ0mgyZr1+9wmWB0pWTJksuAg7+8dPkdfOnZbk9D4hB18gnc+G3I5dMG82SkIjdWaJCxtvc0Kk9m1bP+PRVkWHUpWo1zFesXM6CJW7LU1AyOeMk2L1pFYPKU7R07sIlvbCTVXMm/TCi36tT1vzy9ZBNi3/MnD13054fZch2u9vhXzjzz4+j+6/+abI0BFV9adwi0yhgrpk75dw/x4tXq5ezQDGT770/xC2BDGdaT5shS6FyjimIFY0daxYumfYFkqVrNy5b51FrW05pVIHYvj/XMBd5ipbNka9wkqQOU3J07/ZBHeriKcR1t2jqqGTJU9Rv/0rBstWs1RnRuNfbm5ySNR/KV7z8HUVKQ19MJv7FXX8s++fogVyF7s5TtEzajFlMkWvi+MHd6TJmTZUugxTNHPIatxba4mC8tChQk/j7wK6/1iwq90BT62qpfUhdx6U5sSKg9CVWiFRAEQgXBH6d9BmLGuUfaJYhaw6bfVq3YDqSJarVc5JnWWTF7AlVG7dzGz6CSWDRqnab/21ZNm/z4h9N3UsXHA4VTwdrTD9/+SGROgjgPsFskyh6T22RP3vy75E9m6P2rgr3Lvpu1JFdW9u/P4EiSMmMwa+unDNRxLat+u2ZDyYVLFMVonDmxFHMueTDb6YP7AWdypgtp1jEEtUelCIM1Tf9/0fILawFg8Ge6l4TV8CfaGXumAGYkwef6Y0hxNa65S4ouaNIqZrNOmbNfeecEe/kL1Xpse7vi2Y5Xzx3mk3a1h3g6TJlfaLvF4QiWcVMGnn6+cOId0AYmRSp01DkafgEFUFJMbc1mnTImitfhmw5jR4CgIY+3xjaRM788QNxHTV7eSCcwHt/oDuEDfWZvJrYWAlDfmHMr7fdUcA7Gkumj5k9rC9op0iddsqA7rBAoYCmMyZxbN9fUz/uiTfL5MCAW736GfekrLUtm/UVA0dV1px5Eydx9nXR/4c6vX796tXtq3/btX5569eGGj0kmMol08bMGfUfj1eTFz+sUK+5VcykcfZ8/NR91Zt0eKjja2RC1pd/Pz7jbTnBMw7GS4vvt65Us3ln7pnPuz7CJatgUFXpnn1IRV7PviLgfG/5Wl/lFQFFwFcEcjgWgnw7+Fn/fuib88Z+xHPqI8/1tVkZZrB81njogivd2bxkLkpK12rsVtWauY6XtcAh7mnQGkvMv7pP8ro8IQAAQABJREFUvggPOLZ3u1t5ySQQGO6S7Y4C97d9oVCZahIokzzVrdHOHPIq3OXZjyY/9d5XWKN9f65lUFRcOGUE3KVY5ftfnbK229A55Fw8e4rzih++Hvb8re5h5755rxuZtVt3K1OnsTCqFP+vecXsr7HTcJROA6c2ePY1eiuBvcZBwsIZrppchUqgwe2RIlWaBzv0YbBZc+e/6LI7fen0L+EuUJaOn3z7+tT1rGpBMugbVMmtNgn3wYQ36vZO58EzHn9zJK4XT8NPliKlKMmUPTfLRjAYo3PFDxPgLnWe6N7l0xkV6rfY8PvsOSPfodR7f24mSgQC7FYDE+YCeYkf8oIGUT5wl+JVH+gxekHbt0ZRRSKfTE+siWkDX4G7gGfPsb+/MmEZuB0/sHNI5wfhmmnSZ0KSgeO8eeGLXxl4gdJVrHUlXf2xZ+5t0fmOImW4hH9YBXZvXCncpemLH741Y/NT7467u8aD7JtbMH6QVcyk/96/kzSUSHK2LJtPAgcP5zgYrzS6Z9PKCW89SxpivWnRHFykpH2CVPTo2VcElL74ipjKKwIBIcC3ZH2tz9M2/valM8byQPx0/wlW77R3VZgZ/PBlat8iAVbhdfOm8muLZ96aKWmeoZfNHIu1JqoXiy6rS6VrNcx9V0l8P67ykoMXfeHkYTg5un4+u87jzxcuX0PyZSMPFYUwYVYx/OyEqtywraw4LJs5jp607P0pLcIw3v1x5901GlAXu86iz6XzZ0kvnuqwqd2GzanbrifGD/PMpbAH7B8uFi6hO2NffXLKBz3ogzAVfEtP9vsyY/Zc0wb1GtihLuZcCBPCng7iY0Q5AlhBYnhJQG44t+j1KVBgoVnSevbjKeR4oi8SkcNkmYU/L8Nv9ernLO2dPnboi15tvuzT1urVYAGFVmBsLNA81uP9wuVqgBULSd77c/HMKWohSW+fH/4TXhBcAuR4QWPBhEEIwHImvfvc4I4OR13pWg5fgtsjVZp05Ld5Y0TWXHcS2ITXqvXrw5gpWkmb8VaY9sNd3nLr1bMqTBqz3nT1/3fmyyt8hJLi9itfr3nKNOnuqnhfm9eHQ5U2LfnJWtekWUwkLdN9/vTJuV843GaypBgH44WpMHC8fXSeO+3BDr25eU4dc6y0+gSpGY4k/PiJcNIQJZdKX6JkonWYEYkAv8i44tk7zfoCT9ItXhmUMq3DeFgPfjFZZbDmmPSBrX+QxqNgciTBKs+OtYtuz18kceLETkVczvrsDXRWeuQJ6AVG6PE3R7D6gK26s0RFfqz/ObLftQo5504dpxYsB3bFLpKZn70uYpsXOdaeZKtU81cG4WDAuj/c6Q3cOSKQOkOmpMmTm2Up4TQUpc+cjfOBbes5E1RRpOJ99AFKMfMzxzIBreCNgLucOXGMdjF4BM0kTZ7ivpZdeGRHQA42aj0/Yi7ciEv2gUNiPG0UF3nYCa8HlPSMwX1YfyF9JcZDkC6mP1K0e8MKEjGP2ZLxn7NESRevUtfA62X4eA6qNGzb+5tV9dq/TJQS3O6rN54mIAaNjJoz8cuciTguUKYqCRiV9/7gBUEM1vLEWyOhgIXKVieMiRwOT2js27IG10uDjq/RmTJ1Hu029AdDRKSi9Xz54gXAt+75MvufIRxI4u0TN4y1lmtahMX7gnPr/daVIZdXL19GMn2W7Eaee4mIZnPplBDfFWud5POORO4EEvzViFiox3vhzK2GIKC0la9ERdo9FLNlzydIpbd69hUBjX3xFTGVVwTiCAGiJT5+6l75RaZJVl4+aFsdy1HknloYG/7hHscYj3yxWfKUqXDjm2UI078b16+THv/m0yWqP4i1YCczwQFJkyav82QP8oky4XGfIBVMyOm/D2P2oAhl728iQR4V6rUQPQROSuwkESe/TBxCrcqPPGGaMAnx1RNqgwPj92+GQnQe6zGA3TTsf8a6JEvhcO/v37qu4XNvmyqYK8gKjbLk1L/VPaXva5gpR+7LF85fvnCWZ/p0mR02TIwxARnY4IVThv+5fAG+etYmSlSrP+HtjjCYgmUckaHE6HT9bLZ54o9x4N88dfTgggmD723ZpXStRoSIIvzD8Lcnvde1x6h5hiSZzkiCkcIUL184R9woOODpIV/2ln/74YsMnNHt/GMpMcLghpPJqbpcIkPifAwFkRwvw8cpdXjn5qqPtr+v5XO82mfpzLEsEf489iN2PJ2PiTgmFnXr8vlZcuXDoYK2lKnTee/PuVOOXeXNXvqYWB8S+MxYm2M6rl664AkN4MWFAKfE2SYd5iyzYy5NolDZakzB5P7/K1r5foBa/+ssLvEzFa1UR+gabRlhLwm5qY7t3cGdg2eRaBUmJVfB4lT5aXR/orPTZMxydM827g3u+RYvD3SrSsKZud/oP0wXfjz/q0+gU4wFnh3q8fInSa9YY4WAksAXRVe5UYk99wlSt0PTzFgRUPoSK0QqoAjEFwI3hbtUbNAKzwebpbHHu9YvxQzzD6vf7t1xPK8jE/PvnCt9IUxkw8LZ/KwTjmAdA1uaeYvMzCGvY6r5Z4qQzFOsbEyYSEnXDc95i5Xj13nd/KnYKuNXMHV54CZMYdOiH4lyddibXoNZtIItEUm6Z+NK1gLY740BxiAVr1oPIrV7w3IWSp4ZMPGBdj0xtHAFgnPRRl2YQfJUaTLF7K7KmO12MjGrsz57nR06pGs271TvqZdIEAbEegeBzE1e+GDqJy+PfLEpoTxQH6JqaIuQEcgHavlHYA27nGS3OSaHN/aydQgNrgdvx4GBvdXYESVDT6o++hQJ7BMRHuyZIuyDS5a6GnR4teKDLa0eCKsqiTS6cc0R2SNHyZoPexo+rhqs7OKpo+Gj+DyEfDBfDbu+zbQyBPbUYJLRg/uNiBAYHv+89KdG02ez5S4gS37UKla57pzU7+DDwB/jCQ0WaIZ0aTD0f41gUVDhg9s38JofnHbEEt0agOV/NZt3FG7HTUg2swBpKH3fI3JLwEJwoVjEPSaZZcrG9L5Fhdu+/QWXDJa7mkCc2cNv0dxqjz3NTiJrSJBVIyyBlyHhJGPBiN6yYMe7nrlPcKGlTp8p1OMl1rtWq66lazcyXaK33OFc+gSpqe5rgmWmaH6BWWKJM/IVtQQg36KF4+Fy8uTJCWAsOoQIQiBfvnyvfT45bWFb7/Yg6oUfYqf9Mnhljuz+E+vOdhLSy2eNI0DSaXuqFRBWIvAEsC6APcY9zzN9yrTp2XpNGqt2/fo1YimgPoQR4HVnD06SJA7ngduDR2GCV1hcIL7ErQDbUuhzjnx3GdOOa4enaqwL6z6YYQJQJK4WklSsSl38DSbuUlwm1veyMHZ4jNhF3DBUxAabMGTcA+iUQBDe2rL6p28kcIS+4SHAirPcQ9AJH12SsBs6DAOoWL9F9aYdPI2RuJ/vP3/zwI6NeGuQtK5iUJ2hcXZ9g44rFITcEq4r/g8p9TR8kPlz2c8Aa6Je2Kx0b4susJneD+RjLaxe+1fAkC3lmGqnhmz2h7VCGa8XNHZvXLHo25ESbQ3I+UtWqtr4KS/BzoBPWBJz59Qrpon87HkLOXXV7SWjXjt/qiNC/MFWwmaMGNQNBwx3kSc/mZF0TcDyYXjkx8F4nVq33sO+QmpUMe+QEu+2adCgQQMHDuzRo0f37t1NxWhLKH1R+hJt93w8j9cn+hLPfXXXPE/ebEz1ZP7d1XDOw+hilqw0xVnC32tsHgYVQuCkAKNy6fwZ2JsfttBJVeCXnoYPIaD/qdJmMJ4tPnRFgHP9p3sF3qhVgxc02KwO1xFGaK0S0en4Ha8fkCp9sXm/RfXikQZ427xLVCy4CGRPk+hWdGhw9caJNvtvzPPUHTveC091vedDUNwKQAhC16jbFr1keuoJ1MqpKHPOvBL+4kWbH0Ve0HBdf/RDf7hVid/x+grprg0+70wMN8DjrD+68yjOoNaGFAFFQBHwAQEWB3esWYQ7xIc6KqoIRA0CSl+iZqp1oIqAIhBRCBDSxAtFCHyOqF5rZxWBOEJA6UscAa3NKAKKgCLgEwIlazYgijZZilQ+1VJhRSBKEFD6EiUTrcNUBHxGgI0/7DH2uZq7CkFU5U69b3kHtm8goNK3OvEhzT5qPt/o6ctK8dEjbVMRCCMElL6E0WRoVxSBsEKA/cafdqovL44LsGNBVBVgT6g+7rV28gqZwFX5oYEXrPFqHD8qahVFINwQYPuL6xFnnYzqnUdxhrI2pAhEIgK8EoZu8zIY86oVv0cRRFV+98FakffpWS/jMr123neQuepNn+H1d3HZrralCNhEAEYikpJYtszx7S1zmFKT4yUhb9WrUqUKMkF/RU300hcA9WkavMyQFikCCRIB3mvHuHhhmoyOd5umSJ3G+tZ/+6N2UmW/IpKBtOu2IU+fiHIrHPRMWY87f+pkZNEX3mC78NsRfAMyHN6dE/RJ8Vth0G/O3etD/iJdY/ic2InJt4+G23f+WvVIWs68Zw/NQXzVXvTSF/szpJKKQHARyJEmkeNbKWF/8JUc+mjen8srek8c3pvouf/0m68ub/jt+/rP9JZP2PynzHLhpMpSEnvSbbuxV/MswdvhnN6w4lk2yCWXzp2Rlw6bzzMFuYGQqeP9znyfiO9d8zbhkDUSeYqDfnOGAgJ5Ra99zYaUiNeEiibHKR2rToiLcBdcOJKAxHBAYqgboD9G6Uus+KuAIhClCFyNiW819AUUML0Xzp468/dhFoPSZ74NS8bnAPnO0ckj+575YJKXF+m6qvIJU9d2fapuFZbXqKRKl9GaGWfps//8LW1ZUY2z1gNviK9vXr504fL5s3wB9LY8BQNXmAA0BPHmDBEaVvJBE+ZS2Im5NIkgdgOdotYwFeFSMBhpxeT70ajSFz9A0yqKQFggwCIInzL2QhoC7KWs+GBot65YsHfzmsO7tqCwX5PSopaP8/UYNZ9P+6ZKkx76wgeIzNeLXNs1qlyLvOR4addLLe9F1686VsRSp/8PfTlxaA+fHOJL0aXufTh0eNIujh/pXrrMt3nvZ6hL+Y6SfCQ81ob4ftPmpT8zF0iOeqmlkX918hq+KmUu3Sb4EgJfHT+4fWO2O/LnL2XrO1+ueux31bVu6HLs3Jx8fHvfljW8Ovnumg3iy+EHAhCIvXv3hg4KnzTDVzisJIZLnzQYYaUvBgpNKALhiAAGYM+m1fv+XMP3//IULZsjX2ETfPBu83J8eZjv6vEZRX4o+e4dH7z1dQx8EQYDkz1vYVnOQM9Po/vzPWps2+Xz59B248aNca+3N2r5lmG+4uX5zjD0hUw+J9nkxQ9NKQmsHd8i5mO8SJqv54gq03NcOHzA+a91S/KXvIfvCfM5aKsGSfONQy/tigxvddv1xzI+w8RnGvMULWNdwPKE25VLDgIhtgSZhd+OXDJtNKMWhXeWqOD0gUzJt57x3/BRQFid9YuM8DP51tLfB3b9tWZRuQeaWr9gcPPmDb6IiTyfIUQVn6t0/TCTtQnSrErwZU2rEhFgtW7JNMfHmUvXbly2zqOS6b111ypH924f1KFu95E/H9yxadHUUfDO+u1fMV/9BNUkSZKlzZRFvmw187M39my69eo8dnEXuac2UDP7hru4nU0+FMr2rtU/3fqoHC+wYRO49NbL2RVbL129fOEcdNAaV+7lZvbSqClyOxBKDby71jtutnJ1m5Lj/eb8c9m8n8YMYLlNlKdIk7b0fQ1NQ5oQyhKzjuRww/jHYJS+6I2kCIQvAnzAeerHPc23iOnonXff0+rVz+Qnm99u7MqPo94jv3CFmokSJ3Y7ElwLmbLnZgPR4qmjSlR/EPLhWPFZOKfZy5/wReU9G1eM7Nmibd/RfP+Z6jv/WEKQZsUHW+YrUYHPHBJeim17qNPrOC22r/5t1/rlrV8b6tQKCwpZbs8j1OSPX2ZMfv95EeDTzU/2GyPP3KJK8omiGNmz+fEDu+6qcO+i70Yd2bW1/fsTnHRy6b1dmMeSaWPmjHrXWhEiVaFec3K84Hb5goO+yNoNrc8dMwCK8OAzvSFA0Avv3IW3xcwd88GqHyeJE+XeFp3N9xTfb12pZvPO+UtV+rzrI+g/uGNj054fSd/W/zZr+sBeVAFMoQglqj0oRW7PEBcCQnesXUTHHnmu7215CuEr4gMCCC+ZPmb2sL5QAb7zPGVAd2Bnmsj30rrbKnxYm1rLZn21YvYEtGXNmTdxEsdLNFb88PXiqaOZGtLcae37fwWLva/Vc3s2cjOcW/79V4906QtzpdQcnmaT22D76t9hOTWadMiaK1+G2LZZecLWbVcRZuqXzRxHN6o0epKJOLp7210V7/NyMxO1g3eNeecz1/z5lK/X/MC29dMH92nY9W3+ItDjaSDw0U/a1+o0aNrhnZtnDnkdSfxz5eo28fJHsXfLmq/efAbJ6k068D1t7qtcBYtzaefIX7ryqPEDZcHFjnzkylgZjGOFqbLPzjmlL5E7+9rzhI/AtIGvwF14WH+sx4CkyVJs+P17nBZDOj/YbdiP/ASny5T1j19mZrujQOdB06yeACdcxvR+ovpjz0Ayls748uie7VUfbT99UG9keAFJpYcfP7TTsSRknAGbFs3hUt7qhsVNnio1l1TnzLMp9MXVk//xU/c1f3lg2fsfo2jG4D6Yw2YvfYwxmPXZ6xCjl8cv4SuPRhV6Zg55FQP57EeTYTZTP35p48If4CLGMYOAOby0u3vjSuEuTV/88O4aDfZuXr3qp2/QdvrYoTpPdPeCm5hDWecyqyc4YwDZbR9MZ+jk+Lc6YJKxl8Uq38+7/H+ZOKRw+ZrYJ5GBSopfBJoCjHApPhYIYt+81w0iUrt1NwKGls/6CuEUMagazdbEqWOHPu/6MN6g4lUfyFWwBP6P6YN709vnhsw69NdmuAv5rV79/OyJox+0rY7vROgLGty27qlKmvSZqAJ3wX/T5MWPxPH268QhP4/9qHC5GnAytpt93a/TwikjALNIxfv4t3XFL9CXS+fPWntL2tNsmk8VQp1jXTbygq1rV/HQjOrZkvHeXqAYHr7rVy6v+H7Cr5M+e2vGZi8384IJg86ePFb2/iaMkW4XKld99MutuC2XTh8j9MXTQGS86+ZPAy5mFtcUy0bQFy83JzxJagFsvhLl43HZSLoRtmcrg5k8+Zajzn5vHYxbD0VAEQhPBFKlSUfH2rwxImuuOzNlz1WzWcfWrw/DtvFwT7480Tbu9o4X7oIYjpO186fCXUhDXya90wXeg0FlTYGci+dOc8YScN75x1LicEkkTZ6CM6/KNT/EjsykjqcdCcIlwe84Zw5UHdvncJIf3bsDe1ClYVse3LErbd4YTubS6Y52japDf23irSfksOA17PnGq+dOqdywrXfe4LZdYQC12/yPx2jCSHnybvP6cCjIpiU/odwLbnAsBMRgV23c7sl+X2bMnmvaoF4DO9QVIkWp2wMDBndp2LUfW8dhLfu3/oEYVTizZsGkQAfBBIUPdugNDqeOHaQIjxfnbsPm1G3XE4NHPpeCPAnXAwKEKiSfeGsU1KF4tXosxuXIdxeSGGDOO9ctmfTuc4M71iNdupbD0+OldU9VeJ8vFTke7vKWQIGPAbtO6E+798aVqFYPskspt02MlOMkYlevXJIcXES062U24VhgBZv8olebL/u0tXoQjU6T8IKta1e3Ll8Ad4G3dRk8HVIIsHyl2XFLp0nn5Wa+cf36gW0bZgx2EHeOCW93Yi6g/ttW/gof8jKQSzF/IHAX7vOOA7+7p0HrfVvWihLObm/OPEXKsDAH0cTTM6BNFTacy7ybWpowCMBg8LvE7E/y+VPb0U5fZCuXgVITikBYIXD54gV+NGWlQzrGr63pIT/ZpAuUcbwSyvuBnxxh/gnn6DRoOotNsA1qiR+C3RMs58/6/A3Rw0oTCX7xJWZFMjEPJMT8s8D0fuvKPDSTg/knJoDESbZVOxYgJkr1vDE++TMnjpBpVB3ZvY3L5q8M4omc8FUif+s++SI5Xg637V69fJkqLH6ZihhUQkTl0gtuhPIgY179UrRS7edHzG3Z+1MyJ77TBRKDx8LotCa2rfqNS8gZZhtjxuIOl38smI6n6sIZB1wcGGwU5itRkfShHZs4E7qB6wL2SSjPzM9eI4cJJTZIYOTS6cASk1P+gaackZGVwTvvdigkCBSL2KDja0xZmTqPdhv6g5h2L617qiKQsngnvg2Uw4o412j6LP6ePZtW/TbpMy65K7hzSHCwXMVZfFd/79/Zv3Wlg9s3eJlNOglWvb9ZVa/9y/jGoKpfvfE0ATExypxPXrB17eq+Px3soXStRjjPmAsYMPohlGR6uZkp5eaHGsriF+NiwbRq46cgFoTWehkIPhvpLnvr8CNCkR16/jkumW5vTopy3FkEAsqSU95i5ZjE/i0rsoYoVfTshIBsom7RooVTfqyXungUK0QqoAgEHwFe/XLUEX4Qy1GobDUeNCf3/1/RyvcTqLj+11lcsuJTtFIdasqKD/58O95plp+IJeTn+/E3RxKLkLtwKRwtOEXyl6yEqqmfvAwXwVy17z+exSYiYHgE5yV1/FLzbCohnBIYSzQiZmPpjLGENYjXJNNtuXh4RcnFc2c4szD0Ydvq+FR2rHEYeAnwNKrYKkXm/q3rrK+/o2kvDhi37UowAVHGBMOmyZjl6J5tPOnCDFq87IgE9ILb4ZjFMrFJ/xzZv2DC4HtbdsEW4i6CVfww/O1J73XtMWqea38ADc1jej1+/fpVloRwVGCZZg9/+8/l82/PX5QiVpEw2CTwk9ETSECJ6vWx+iQWThn+Z4zPgMWaEtXqT3i7I22Vf6AZwk4HGpiLwZ3q4xiQGBQECpevwRlVOHggfDgATC2gu3DmFJeeWndbRUbHzWD0SPT05AHd77irpKxIPt61H46TNT9/J/ZePHyEmFyvflU8ecQJseMMDW5nEx8bwSKsVN7X8rnKj7RdOnPsvLEf4eBp/L//hCtJB7xgy7wgY+1qmVqNmGuWJn//ZigxVeLYKFKpNmJebmZpiGAUvFVwFzxbBHsRpEI+oetebsvzpx2U64F2PQWHnAUcUSyE0hev6nCAub05IVVTBjxf/oHmLFER1wW74j5hDRGvDEur1NLDikBM3IvDAcN2JFlOspZ6SSt98QKOFikC8YxAzeYd2emAqwNrR1f4AcVvUfq+Rwiq4JJnes7sWObs5WDP6h1FSvFjLfSCn1SEy9RuzI6b0yeO4oR/tHt/TMtdFWs9M2AiZgxvgayM4EXACLFQxc8uVWSBCXIjbbV927H/hQMNYkJuxPSEJ05CTzAwFGG3GjzrcDkYVSVrPkwUBUGXEA4MADRi94blrCzQtNn54lBqOdy2mypdhnbvjiMWBMMgsuy6YhOTGEIvuPG2ErgFy2RYstTpM4Et/4hlYeCyMgJp4MW4uQuXtHTBkcQzQcANThecWMSK3v9ED/wuWGXCXKAptVp1LV27kalCZxggl1ANDK18Yqlm8071nnqJTOYRb4db+lKhfgu4xZalczHYeYqUXrdgOjxGAopZHRvSpcHQ/zWCEyRPmQrnx19rF2fNnf/Zj6d4at1TlU4Dp8I+r111eLDkwBPDZBHdwvAJY8IlhqcB6rBu/lSCW7nfMt2WE9wIMeEfVUAgfZbbvMwmlBpqSCAwHiO8ROdOOV54g9cKbcKGbzUc8z8v2NIHp65yP3QePGPZzC9PHTucNmNmloQg2QVKVUGTl5sZ3sDE3d+2B+unKdM4QpGQB2GqnP77MEuQnm5LJheHnNnTd1vego4qx49K/93enMBF3yB/bNTKfVcpJMVrxXYko0eq61kQwAGD94VX2/lEXxLDE6MTQdl3TriQHwHP0YmYjjooCPBXytuiSjbpbsf7Ii3yhI2LBce4OPBNN1hcIFZRAg9NpmuCXbtkur7OxJPPg3x5uSrrHSzz5yla2oS4QkqIh+BBn03a8sMtzUFfsG3sZMFUE0GJR51lAhiGMVRWVaTnf/UJUSM4e6iONx5qhe0Uz79r/8nx1C5FNI0DhvU1V3+JJ9yI87h5/TpGker4jX75+lMJx+ESrlCxfovqTTuYnpNpPQhJxmSatvj9pBUT+GwkY35XbwrmfPOSkWI+zRZfqgCC2VVuarkmej+QD0LT5IUPpGj3xhWLvh2Jv4dLFOJsYPmDnfNOFa2te6pCr7ipsuctZK1LTpIkSay3Gf0084KBnzfuYwgiO4dZI5OKnmYTTP5c9jMTZ6JesOX3tugCm7G2aE17wtZtV01F4qlZNOw10X3khLmZkSdtJs5Uj/nrgG8k9jQQM3xTxfgjJcftzUmfmSmzSQ3mxKY/1tFidZSyejiqZwuskvdQVrFfQXwBvxldfCX4YcQB45NFVvqi9CW+btcobdcP+hIpSLHtlp1Hb07bCHGx02cCLaEdrrzKTt3gymDvWYaAgbmat+A25JM2vjDQ97GSjZ9/r9JDbawV8fpgQe2wH1PLjyqmrs2Ep9mENEAxU6XNAEWwqconsSFdHsqW+04ihX2q5UXY00C8VPFSBCOEyeEt8yJjLYpy+hIrb7NilcR6oWlFQBFQBPxGIGPM13DYX21TA0+i4cBd6C2Wlc6EA3chGokAatn/JfvLche62wlP9iT7xF2o7kcVp0ZjvfQ0m6AaUxRM7sJeHlamIJ3Ahf+M1+jF2j37Ap4GYl+DVRKCbp+7WCtGW1oCeH3aTKOxL9F2k+h4FYFQIUAML6oJVNT4RL8hhrIQGMQ/Qps3LXa8gyen7Tee+d1oZFXEkyQbsgickrfLEIwSWUPw0tsC/n5awYvOiCgyAbwwGJsRHep9iYiZ1U4qAhGAANEkrL+whBQBfQ3XLua7uyIvs6F3+GDY30SAsGtgTbj2PY76BWXh1c+OO232BF6Tw0uG+JhGHLUdV82YTz3HVYNh0Y6M2r4DRulLWEybdkIRSAAIsExQq3U364eHEsCg4ngIhAyz6+eFL34hopktYGzqjuMORERzbEd65etlvEEAP1+LVwZFRJ+1k7EiIE4X9h/FKikCunhkEygVUwSCg4A8YWS3996X4DQZh1rYTxuHrSXYptjd3eFDn9+hnmDhcDcwIlTcvkLGnWyE5cFcI6zH8dTd6PW+2Fxdi6d50WYVAUVAEVAEFIEoQkCMsi4eRdGU61AVAUVAEVAEFIEEgIBPDCZ6vS8JYKZ1CBGKgP3F3QgdoHZbEVAEFIFQI6CxL6FGWPUrAv9BgMcL6AubSnb9+8GZ/wiE7mL3evdvJg1di14079pgN0DPi5K4KWKy4qYhbSVwBEIXOCLfJfC7h/ltR7QE2JDfPQyHioQGsnjEYSe6Q+lLOEyZ9iHqEPjfUz5/XjXoGAXyQx/4L2ydJ3oEfUQhUtjhw3AJpeRLnxF9ELFu/1i+zB/WKJ/sdmpl+fKAuLLw10BZ7HinTrm/vGW2szk+4aSHdwSUvnjHR0sVgSAjwM8T3/U4diHR0Yu2NNv50eSrh7Z0/VcokDWs+eMdH3YO6Pjvr3kgXMqnbmRP7ZO4Q3jjdz5XiYYKPCKHdJh2nr9tdqBR7YDYQOU+flLtIA7B5kijSkzpS1RNtw42XBDgMdTmk2jJ2jYe/e3IuAzdp4+7utQOckaobWGQu6vq+Jx4ZRt3pgKlCIQMAaUvIYNWFSsCioBtBNQW2oZKBRUBBwL8yeBADauHkDiemGjfeaTPfHF8w2lzioAioAgoAopA4AhEO30JHEHVoAgoAoqAIqAIKAJxjIDSlzgGXJtTBBQBRUARUAQUATcIyH4Cm0vJSl/cIKhZioAioAgoAoqAIhDHCPgUzqH0JY5nR5tTBBQBRUARUAQUAY8I2PS+6M4jjwhqgSIQEQj49LwSziOy+ZsVzkPQvikCioDfCAwaNMinukpffIJLhSMAAfvm3KZkIK93s9lEILAmGKsfB1j5jXO8gMwL1P3ucCAV42Wwrh0Ok264dkxzQopAjx52XxKo9CWkE6HKQ44ANq9Fi/h/AT/jdPtra/9P0W+k3Lbrtzat6BaBeKFWwpvjpWm3IERQJn8UvNs6gjqsXQWBgQMd7/K2/4Om9EVvm4hHwNefqhDZgxCpjXV65G8+VjEViFAE4oAB20TGvl2xqdC7mN/N8ZeofxTesQ3DUrNyZH/elb6E4Txql3xAwA/SYP/Pw4d++PLQ4JPaWIWj+bWbsYKjAoqAIhBBCPhE1nXnUQTNrHZVEVAEFAFFQBFwrLD48eQWOHAhahTXizjMfHoYU/oS+ISqBkVAEVAEFIFwQSBEJjZchhdP/YBh5MuXj0BDs8oTrI4wX8JdfHK90Hr0Lh6FaAUhWDOqeuwjEF8bNOz3UCUVAUUgLhFQBhMstEGSw8QSYTeDazqNcriLT64XBhi99CVYs6t6FAFFQBFQBBSBBIaAEAvOMi5YCwwjuNzFrBmh1lfuQq+UviSwW06HowjEGwJPPfVUyZIlX3jhhXjrgTb8/wjkyZPn22+/9cnYHDx48PPPPz9w4EDGjBnbtGnjU93/b1b/nxAQCDVxQT8H7wXgDF5++F0EZaUvCeFui+Yx8DfgffHo4sWLqVOn9gOiDRs2TJ06tV27dvnz5/ejehRWOX/+/G+//ab0JRym/vTp0/xp2Kcgx48fL1as2NmzZ6Xzn3322blz59KmTRsOY9E+xBkC8AnWiYRV0Cj3T3A9LsHVr6G7cXZjaENxisCxY8cee+yxDBkypEmTJnHixNWrVx8wYAC/yPY7MWzYsLfffrtAgQI5cuRo3Lgxz7IXLlywXz0BS968eRNMAMR1jDt37nTN1JzQITB27NgSJUpcu3bNtYl9+/a5ZnrK4VaHu4wZM+bSpUu7du3Cq588eXJPwv7le7lt/FOotYKIAMSCsFwO4S4QF977x2GfAbvtDNo4RLNE/nKJZFD0q/fFLeaaGfEIzJs3b/r06ZCP++67b9u2bUv+/5g1a5adsV2/fn337t133HFHxYoVb9y4wYPszJkz06dP/8QTT7zxxhsYbztKEowMxuzo0aPvvvtukiS3Hnhgh7/++muzZs2sY4Qp7t+/35qj6eAigCuxV69e5cuXb9u2rWg+cuTIli1bmJ3cuXNb28LjyBxZc7ynMVTlypVj+Q8x3I3PP/+8d3n/St3eNq53l3/KtZZ/CMAnrB4Xv5dypHVhJ5z5zSRHLk3HguvOUfpigNVEgkIAy1q3bt3s2bPLqCZMmADzsO8bmDNnzty5c/lhld9x2MzChQsHDx48dOhQCBBLJAULFkxQeHkeDGPnF41yLCLUzQjyjP7333/DV3Bu3XXXXSw0QO9MqSZCgcCOHTs+/fRTNENW6tSpY5rYvn07nsUzZ84wBUWLFiU/U6ZMV69eNQLeE3AgiEW3bt28iwWl1Om2SZUqldu7KyhtqRLvCARIXAxToRW3ZEVah7Kwvs+Zw3t/fC1V+uIrYiofGQikSJFCuAs/l7hh+vbtS78/+ugjm73n1x9J3O94vLHfyZIlqxVzvPnmm7jZITEff/xxrKr++usv/sKp27x5c+O3iLWWHwLYHn4+ODdt2jRz5sx+aPBeBdfVe++9d+LECRYpRo8ejbFEHnpn2GGnTp1Ya4PBmOAJ7wqDUkqozapVq7Zu3crMFClSxKoz1IBY27KZxnGSMmXKQG4D7sNSpUp99dVXo0aNIsyW2KwZM2bMnj2bDtSuXdt0Ay6CdzBdunRuV5SMmDXBghGXoYvx8n7bmLvL2qVA0mJKA9GQ4OtaiQuswm2Ai7ATgULSBlhrkRNWwlGEr1AUdMpibU7pixUNTSc0BF588cVPPvmEUT3++OMwj0KFCtkcIb/+SPIsW7VqVQgQdXnePXz4MF4c8nE2iB5+l7/44ovx48fz048Jr1atGvlkwpPwx2JERYx8doJImjMmZ+nSpTVq1HjggQeSJk1q8tn0wYrMnj17cONXqlQpW7ZspujKlSsQMi4R2Lt375NPPonPg0tcQb1792b5QCTpcMuWLU0tTDu0g/43bNiwS5cuMihTahInT55csGDBI488wqOwySTBqlmrVq0IAv3pp5/ETOJ26ty5s8hIW/xOlSlTRp74napbVfmddjuETZs2ESDM+qCoFfIkae+AIAMJAEA212TNmtWpV3gv8GHkypXLKd9tH5xkzCWsYtGiRRCCe+65h1UeFtSkCErxwQcflC1bltUfbowOHTq43SlKXaYJMaNQEitXruSWwH7gROQgs2bNmghLKUaiQYMGCBQvXlxWNnGV2aQv8+fP5x5Gz6lTp0SbORPsxRIqsTWSQ3PcJ2+99RaXKIeKcUBqv/vuO5ZorQxy9erV8HuIVP369fkbXLx4sZfbhj8xubukFTQjz0iZIEZE6zwAmCIvjYqMnr0jYCUuSIrrSzJNRS/sxMiQMEzFpENKVqxN30rzcBm1R968eXksjtrhJ4yBM4MQBU9jefrpp81Nzx4ifos9STrl8/tLxf79+8MkjAZJ4GYQYZzzEvwBO8EPgTnHAFD0/vvvI8klwcKY2LVr11qVY2uNQowNlIVSVH344YcmXxIYFamI+SSHh1T2tUrRuHHjKCJHLrHlGAB+8XnEN21hAyilG/JoDo8xRU4JWTWYNGmSU/7EiRPR0L59e/I3b97MGev+0ksvjRgxArUPPfSQk3y/fv2Qhx/Ae+gqI4U5OclYL2EhXK5YsaJr166YOkCgrWeffZbqIuZ2CJAtWqehRo0aTZkyBRBgAyLvHRDA4cda6lL9lVdeMZ2hiD6QyUGCFbEff/zRSx9MRWuCjnGPxei4deKuYFJEhqzWrVvTOkeTJk1Yl7TWlTT8GDECtpyKIK/CmLkT6CproAiwvgkTgpVSBXLjVAU+weIpmfjDGGmfPn1wVjnJyKXVJUmwF39Q3LcycTgaUc4SoUjKrXv58mUuoVDcAAyZ3kqfmT4R+/LLL8kxB3TH+21DLbm7SEDEnUwgLB9Xk2j20qgIyJkh8NtuzUl4ae4rX+0Xv5NU8ekASTmoy0GjcoQJnonCpB/x0g0mkrmJl6a10WAhwAzyd+VJGz/ZPFzy1EsELr+n/DpjITwJW/PluRaTz/qRMBisDgYDV4QRGzJkCDr5rScHlz5pYSoSnYD8yJEjzW+61MLMIMbvPglxC8FgkOGhlnwOtn7QBLYTC8clP/1UFPoixoMhkA9tIh/D76gTY4kxJNKEnBk4thPTAj3C+JHA2FsFrGmGiRIcS/Tk5ZdfhrVQiisCDRw8Xq9ZswYB1sJMLTCBtJlLSciIqMikOLqVKBEysBknMbmkYwiw+sOCCAlgxI3kqJMo0caNG5HxNAQ4ooDAiEDGqtwLIAytXr16KIedsOz12muvkf4/9s4E/qbi/eM/2SPKUnaizVokJbRQkUoLhWixpCiKiCSpbBUqJEsRkjVFSdZkLVu2rFmyln3LkvB/3++j6fzPvfd8773fc+/3Ls996TRnzswzM5+Z73k+55lnZoCd7HzuoyO5pSYwMB6h7LmlI/zVwVqoCdNZ5AIx3KQAjSEhI4fiSEM8T2EhLFE2WWwBqAlpaB3xfAEDo4wfuA7x0GLiCSPKZBS7BSzZxEgAWxrgo/gZYOTlR6NsaeSW5mM+IQEjFtIj9eQW1gLCBISUQ0HkESYZMsIkqCelkECoFWYn4oVxMt5WrlxJRgIAbsr1OWyso4s6I5Bq4OWDkYy/XArlJwzGX6FGvgTAjXc7itYWH0+3tC5Y/SWw2OgLkUnM5D9qEkO4KX2JBH3B4B9PfzlR1Rb580u2SqgB+TK2fnM75ELl8BrFY4Y06DAma7iFA+3evVtyEcnrnkhe0Pfeey8BXs1GIEpF1AZvdiwEooR4KgrefCt37dqVjLjmiLX29ddfNxII8BJHoRIwtlxKxICEfUI0HI/Q9KJjeMS7HuogEkThIRydTQ0JwL3kkfeVRySAvlBVAhgJSCNWZbwrCGMmsUkACqkbT5koEfsHLkEko0e4orREt5nG2spFOZHMfGoLXHKFF5LYoQkUZ3aXwfzAdJsR7g8QeCHFsZ2JpMQywS2MkFvaSBjFSZ/KU8KoTMIOdZCU1qvYz6xMgu5AMrMnJBMqA5m2ZvEOAyydi/1JOMG6desEKLLLKBo4cCAyhVKQXQYqU2YiClIotisILhKEGjKPg0xymXHoXS7jh0FOPNnBk0IJgwNCJLGQM4RAd4iRMc8tf1BSQ6ZQiWdwEgl3kVySWMJcfQ4b6+gSsZA/k+XHH39EoDTQX6EmsQRET8eQGrbVP5DbEOgLYmEqAo6VxMgrNBbhuoiRob/wIYCvgLh8iqdC+ApSyTYEUPmsBeUtL/FMn8vHN+pfYjCqy8euLaPcyoZdYszHfYHdNdBkmArw88DQTRoeIQHCgRMDXimY8YUESHbsK3yMyuQLbwfm73/55Rce8U3JFfWMIiHA2ODKvInUJG/evNzKj89N+SrlVqpBAGtBkSJFoDVoKdbKElOqVCm0L5Mm8AAMJzjojBkzhvgVK1aggKEj6C0eYZYwr36e2n7Cb2BmoqK++eYbhPCm42NXuBGiyMILzmTMkSMHn+NyCwmQBbdMKxBDodhRaCOYcIuXj8nlHaCbhMEwa4CyBGH0KAvdSenQhNy5c6OShcnBmcCEsjDzkMsfIBi0ePr888/zEocBiN/MqFGj4F7SLrx86GieQnTAE7ck5zrw1PaTbYGsi+oxw5AGmVypM1fpcVtG6y0eJHQu+It/NGMYNkkCbDniAiLyAUpyiT8TxItbbFF0t8x70i4kMAgZllA9Gk4CbiWX95XqMQCI503F/hyoN8IAghBEMV1l6AvcgkdCWSBJmO5ITLlSJbqPp+Yvy+raRbzPYWMdXQwqJOCZRGL5Wf+s/BX6b1r9fzII4G7FiAJGrsZdl79B/tjZmsWcyGi+l5IRl+qP+dNK2B9/daiWcDQfcws6xtq5/PGHoyCVSQ/67ESZaqELUIrYXVq1aiUvVvmMY5aHW96bvGd9Yig8Q+ZoTALIqORiWkHepHzdMjVjEvDlihLFb4ASZV4DmoJVg4L4iiVMVWVUYGZA48qLG9P9oUOHiEc47xG+YvmilVv5imWlCbeYaqQg4U/YbCgO3WY+97Hb83VLSpSNeErCJEzdCPj7+KYJ5JKfLNEijEIyn/h8QxNDo4w0XKGJAQfRiLwKeSQGGwCXgrjSIrHlmIwmIAAiRFgaKWWCALsIhIZkDk2gkjgGCfKgDc1CDr47DoAIl0K4/GHSEUBNLiACZAL8aDLVkLB4AjnUwTTEBGQGEDMJ/YX/h9SKESIwii3KNsdn8pqA2OeoA3LMaDFGI5IJ2RLnJ25lYpGJIbij1FamR8WOBY8UydJNzGaagmwBeCrNt0XKrCIdCiwIFA9fGVTE8DPNwWGcBGQ3TrjQZdyk+LvDqGPcqnwOG+voEpJEw/krgJjKpB5/y/Qswv0Vaqs22Xm3x6I5wdYQh1ta55b+4g+Bn4CGTPOT+GiG0fNlkLA/+ok+c7H53qyFd6XOHLmIsLcoetBnJ6JZeXejA3jliUJCtaNURIK4rRBvvD5tkvmShlswHWCLZyoEgXwfE88SYiSga3F2gVtgceGWNUrG5IPG4vWNeUZoCp/FDz/8MPoMCiW14oolQIrgK1PmC6S2fCcZXxO+d5ki4cNUUsKQ0Cii0uT9Th2YrOEnSov3DnpFwnx540VLdhEOLLYWyS3SKFemDzBsUElqa03JSKZpJsZoWXLRCgwhPBIhMD+TDJwx7Zhba0AmVkiAZkL7mrckOAAXKR2aIBwLybJ+R+xqoE0uf4DgcQJo1BbhsENoK5QRCTJ4KB2aRV46CC1OMiEBDnWwtsWExVeD7PyQw/ihFHkq3A6bmUnsM8DgkQpAqiidXmMIifKW9DK6zCSUmcSUQmmaJMN3igGA4cSUQtPgN+bWFsAhHQk2ggtBJAuIATgjh3FIWHxooBdsu2yEwKEZBpJ94sSJ1o83qgGnkZQ+hw2PzOiiObBkaQtX/mYpyNTKoVBTEwL0Ke92M6Ksj+ImTOtc11+AI5RFADQ8hkB0UhmlLy7QF2Et1r9YwspaIvOm4C+Nn3NZ6CrzBpSUKCc8NI0O8JndNm1v0lh1Cd/ZMvfBqxaqxOcmM0GkxKKAtjBvYZQQrqkIRKnwRiYB9bFO8BvhKHUUra225qkJmLphsUAvChmiOLQLn6qoPVJi3kATyCPi+bzmCCcjwRagUUwwWbWdLQEf99ancClWCcFy8HU1/kBkMc43kp1cmKxsosytv2aaeH9NQMkxlydzXgIy3EXWMTkAQrn0uxHOLa2mhqY+ErDZIfzVwZbLegv+0gXWSOrMnJQ1xl+YJlgpoHcyZi2tgxBywNjDUmhYAlkYIZRozcvINC4p1ngJI9C7zt7J6F9/ycyYNLm8Y/wNG9vooo/4K6CZRpS/gHcRpOSFoPTFH2KBxwtlsZIYCUt84HLClzINos1LNtECTPWhe5gFDK3hvJfJyHeJBAjDWvjdfvvtXLnVXwQQYMqWUkLuRFdqyHuWHclkXxarQP64+FaGQJiNK6gtS5n4crImcyXMxzEV8D6cElMNKicce9m5Uu1AhDg0ASMZTw17s0rzB4g1jc8wm7WwP5BtRDnUwacQjUxFBPgrE9c382mRipUJU9E0kGamRH8FVTHxYYe4mFz8gaQ6vOlMbTQQOALwFStrISN8BdO6spbAMYynlD7VJw3EC9LGG2DMzq6sIcNi9Xa0CsF30lYH69OYCDs0wWwK590Qf4B4pySGWR5MdDhiY7HAL6pBgwa2ZA51sKXUW0Ug/hCQLRa5Qpv4QVxSnbsAstKXIEaaspYgwNKkvhBgjQx+M8xHwGN8Pde4VECASTEWbVEwU0uycbB4yaRCVbRIRSC6EYgS4iIg6cLp5AcLrEUWP3OVeSKsLOLaIl4vyYvQFIpAEgLiWMqyEcUjehCAsrCaBhMabtQYyekj9qqPnuppTVxHANs5ltEo/4mucWg7TAJDiEOCuH+k1pdkuthQFls6/gD42SL1NhAEApxiYzIuEGmxlaZ06dIsKM2WLVtsVTvua4vf61133cWamunTp7O8Je7bqw2MfgTYry/AV2X0tyVMNVT64gQs/NcnBfYZ6SRIn1kQCBy9uGQw1m24LKhoMJURwENIFg+ncj20+PAjwIslLt8t4UcuukpIaPqSrPORTBLBgs2Ok/SeMGId/aENZNkCPNm8unorWYgSMwGLitnOh1MGWQqemAhoqxUBRUAQSGj6EsgggKzwg6zIVBE8RowHXIXTKI8JBEaTBjBNWAOKQLAIcEICkzs1a9Zkm9dg82p6RUARiCcE1HU30N6EpvBjJw9Yi+hgAvzw/1InmEBB1HSKQMoQkAMT2NMsZWKiLjeHanEwFpuwRV3NtEKKQLQioPQl6J6BxMiyI7iLZBYSg5Ov8pig0dQMikAwCLA1MMnNNoDBZI3qtGyJy6GVcgxnVFc0FipXsWLFWKim1jGlCCh9CR1BY4wRHiPTSWKMUR4TOqyaUxHwj4AcpXnppZf6TxLDTzjemdO2ObpZDmKM4ZZo1RWB8COgvi8pxRgSgwiuQlmgMsJmJKAuqCnFN7n8fGmFYw/+5IrV56mDAOczUHC+fPlCKJ4TBhx26Q1BoCtZOAsahx45qLlq1apGJvaYK664Qm45WJRBzrVOnTqxvoeyaaAGFIEUIqDWlxQC+F92GAw/m3MMM0rqHPMfRhpKPQQ4PVgKZ+oTTwsGqq0uHCG5Y8cODs+zxTvfcoAfS4EqVKjQtm1bzp12TixP2XcY3cwpQiYxPh8cGcgtdRg0aBAWCPNIApzbbNIQw7FEtgS2W4Rv2bIFaSYep5ksWbJwrOOIESOuv/56Kjxr1ix5imQO9zYpCQAOpxtycKBEwhsaNWoE+7GmcQgjkFZUrlz5iSee4JhGk9JnF3BOOO8NLC4kYy3kW2+9NW3aNDpCuAuHk+MTQ/ihhx5q1qwZj4w0DSgCiY4Af6gJ+0s6qziZw4pDBgclIWYYM8JCFqUZHRDgFLFkT5x2yJ4gj2QyAlX60UcfyYBk51/T9rlz55pt8tl8dtu2bfIIkiHHX3ft2nXo0KFELl68mMONzcnJ7LLPYdoIrF69Ote7777byPQZYFtbSU9iNrc1RyKj5u+77z6YkBwizdWcC80JRCVKlCA9FUPTE+BEaJ/CJZKji6xHcLdv317ixWmG47iRgHzOA589ezaP2OtFjqzC8sHR2ZAbiJSsLpw0aZLkHT16NLnmz5/PbbKYUHM2wSM9kLK6G+Ec20lGf13APkAdOnRo0aIFWYBXSpSroT5t2rTBPIMBhtZZE2jYJwK8ExLhxGk5WNsnAgkS6fnOSNhfWOmLQVVmkbiaGA24iIDSl0DAFN0pyrtAgQJoSlSsZBQjBBodgoK+RN2id+VR8+bNUfMTJ04kPT/OyhZNb/giT4lH2ZO+cePGPDW0w7tWcCASQ3EQOGHCBMLmjwL6Qq0eeOABIoXfMKWCBLiC7O9CTTp16lSmTBkSNGzY0Fu4xFC6ECmIDoYKspCeBvJUnGYkO1YQSd+5c2diqDY1wbYxfvx4bsko5+tOnTpVktWuXZv4mTNncpssJv369SPxO++8Q2LIEGEYGGGHLuCpTB7NmDGDsPkJ5UICJAxuZ+I14IyA0BfnNLH+FC6r9EXpS7isL7H+5xEr9Vf6EkhPmbNRUNVM8aCqoQtkZJoDfgB3YZ6FW/O5D1PhVpKZDeJuuukmVCn0AiFkXLZsGbf8zCluxtThXSVR3rxwycjTHj16kJFyJSUHKSRJ8uhpzrMkzOYuPKJzCWP8IIxRRPaZlNIlo+0KAyN9//79JR6qxC2kjVtMIIT5SUuJwdbCLdJgNpJe2AwTSczm8Igt8og3c0zz5s3j1hkT7EkIJC9Nk0ZhrBLh/rpAnsqsEFNFcsv8Gu0lvHr1aqxNUs9333336NGjkkCvDggofXEAJ54eqe8Lbwb9KQJxjgCzPNJClDGnXrNxEQqSZbos1sWx4/nnn8+RIwf+Fi1btpRkYocgTDISCHHBP4P5lFatWuE/SyR+JCSAZ+CfjnMGOgO/DcnufWWmhkj8Y9KmTcssTPfu3bnFPUV8PoSyYBAiHrM/1ErixQlG5mJ69+4NA4AcULqhAraCsJcQQ3N4RzNRhTGD21GjRnGmtJwzhW2GlkoujCIEatSoUaxYMQJM3NAEIXMZM2YkhuphpzGY7Nu3TzI6YALOVO/1119/5plnMmTIwKyQOSPCXxeIzKxZsxKA/XCF6oHA0qVLCZcqVQrHXmgl1I1jsfPnzz9mzBjJoldFIMERUPqS4ANAm58QCMg+b0zfCBHBd5VmY3+WJTxMnTRt2hQXUTQ63AKKgOep+NKSDMcLvFAJYJOoVatW2bJlCWMVEB3PBEfPnj1RsS+99BIKG+MKT71/cCYimfdp0KABXi958+YVboETMfEwJyn0oos8bySKY6aGgFgyXnvtNarXrl07DD9Ca4YMGcJT758QkXvuuQdHFmZ5MPbASOAT33zzDR70pDcUhHC1atVgCRC17NmzFyxYkFOmSfnII4+Q8rbbbiNBkyZNihcvDsMT04gwMCnUHybp06cnASTv5ZdfBhOMTBA7uBRg+usCEZgzZ04CoAph6tu3L2GqREbgAopbb72VsyTpmuuuu65+/frwKsmlV0UgoRGIJ1NSsG3h7cYv2FyaPqoQQD9pJybbI8yY4Bx6/PhxScnCHBxcZJIFH155AzLVIl4sxBODKoWXYI1gwgKiw8SK+LVwJe8nn3yCSsZnhZTYcnAWYd5HJnfER8RWJRKLdyrpycUEFgkwq0BQeIQJBxphssBsiKcg4/xBLuZiYACkgcfw1CS2BkhA3UhMtakPjq4sWWIeR0YILsD8rOmZP2IyCK6DcCF2OOVIAggcQp566im4AjGSgIAzJiQAGSoAFLAQ+ABcKMsAAEAASURBVCIuydzC/By6gFxm1onE/Mw0HFSPW6ThzcOP+nPLmCeL/vwhAD7Y8Pw9jY949X2hHxPd9yXuR3l8/K06tELpiwM4Do/EB0USoOaFGZj0zLZIWCiLiZcA7AfOQZhkKFr4gUflJvGYPn36mLy2XNzigIKFwxrvL7GpHulxOlm3bp01F6TEemsLM01jrTZVZTU4afBlwZRiS2xuxeYhZMVEmgACmTOSW6twk8BgQgzGIWFyYILvC3wLH2ST0gRMGyUG710SQ+kImDTU2bqQCkaFK4+68Rp8fAaUvviEJf4i09CkpDdPIl7q1q3LJLrMuydi++OizXz38yEyduzYuGhNrDYCPsEUjEz9xGgb8FMZMGDAkSNHZJop5a2AeDG/xoRaykUhgYohKnPmzK5Ii28hvBNgMPH9Ykdz0cYE11/q+xLff8jaOkUgEgiwFWwscpcpU6YweQQzACM8S3B5cYu7IJAZLre4C9Jgh8pdIjGUtYzYQUAPDYidvtKaKgKKgKsI9OrVC3cfZpTwkGV1z3vvveeqeBWmCCgCYURArS9hBFdFRwAB42QQgbK0iDhDgFkGtsKDuOBWjLGEkwHirIGJ2Rx9JyRIvyt9SZCO1mYqAoqAHQEWkLP5Hj4EuMTiPiWrl+2J9F4RUASiEgGlL1HZLVopRUARiAgC6dKlY8cadvBjaXRECtRCIoGAGmAigXJql6H0JbV7QMtXBBQBRUARUAQUgSARUNfdIAHT5IpAOBHwtx1+UGW6IiSoEgNMzBJ375QVK1b0joy/mHDbA8ItP/56RFsU6wgofYn1HtT62xEIXHkHmNKn0rWXmnQfoECfeSXSFSUUtYSAHdi8255y0LxlRmEMHjZhrZWLMKZwEIY8/EIrN7RcYe2LCAin1S72eAQqHI4ilL6EA1WVGVEE+DMuXLhwRIv0U5j3mzTkV7mfEtyJ9q6nO3JDkhJVlQmpBQFlcr2ZNu3l4kgLnK9Ly201sd0GhI7biXwSZbcLUXmpjIDuuqu77qbyEEyt4iPzko1MKcFiGKx+Cla+po8AAi7ylRTW1nVmZqtPuOXbiouVW/lmi+/NhZ37Qq0vzvjo07hFIDLvxMiUEmwnsdYm2CyaXhFQBBSBqEJAVx5FVXdoZRQBRUARUAQUgYAQ4OsoOu27AdU+xYkSmr5Ej/U1xf2oAhQBRUARUAQUgQRCIKHpSwL1szZVEVAEFAFFQBGIIwSUvsRRZ2pTFAFFQBFQBBSBxEBA6Uti9LO2UhFQBBQBRUARiCMElL7EUWdqUxQBRUARUAQUgcRAQOlLYvSztlIRUAQUAUVAEYgjBJS+xFFnalMUAUVAEVAEFIHEQEDpS2L0s7ZSEVAEFAFFQBGIIwSUvsRRZ2pTFAFFQBFQBBSBxEBA6Uti9LO2UhFQBBQBRUARiCMElL7EUWdqUxQBRUARUAQUgcRAQI9sTIx+dmxltJ2aEW31cQQvuh5G4QmRUVil6OozrY0ioAiEhIDSl+RhQ5sGq1AXLVqUvNzAUgRbdGBSNZUiEFcIuEuSQjgN7bGWzfKlvTiuMNXGKALRjYDSl+T7p27duskn0hSKgCKQegi4y/JDkMYXy9ixY1MPAC1ZEUg4BJS+BNrlrVu39k66658T3pGRidl9NtWKjkwDtRRFIFYQmD9gZAiMJ1Zap/VUBKITAaUvyfTLpBPbJUXGJjW8kxb1jopUTCoWHakmajmKQGwgAH2JjYpqLRWBOEJAVx7FUWdqUxQBRSDiCGxfskrKVANMxLHXAhMaAaUvCd392nhFQBFQBBQBRSAWEVD6Eou9pnVWBBQBRUARUAQSGgGlLwnd/abx+7ftOLJnr7nVgCKgCCgCioAiEM0IKH2J5t6JXN2m9Rny/n1PRa48LcmCwE+jvhrw6HOWCA2GEYHv3vkYwMNRgPq+hANVlakI+ENA6Ys/ZCIRf+bkqa/f6D3jw09TWNjh3X+Obv3mHxu3hCzn/Nlze3/bFnJ2zZgSBP4+eWrNtB/Pnz+fEiGaNxAEAPnbbn1hMIEk1jSKgCIQzQgofUmmd8yygmTShfQYzjH9/U+yXHZpSLn/y/TjkC/mfTpm0/wl/0UFGfr75MkgczglH9G847i2XZ1SJOozB2SO7z+YqKhErt0yQ8pUaTiKdHGv7XBUT2UqAnGGgNKX/6WWyXf2R8N/+uLrm+vVuvXJ2oyqI3/sC21s8UH5y6Tp5M1yWfbQJJDr1LG/Lsmdk8DsASMmdelz/ty5kEWR8cjuP9dMn2uT4Ipkm8yYu/WJTIbMmWjIXwcPx1xzYq7CO1evp86ZsmaJuZprhRUBRcCGgG5bZwMkQrdbfv5lwqs9815XrN77b1DkutkL+j3UtPHQ3uXr1Ay2Bqun/iDzPhdfGjp9OXPqdLYrcp07e3ZChx5UIH2mTDU7tAi2Jtb0fOD+dejI4V1/nP3nn2yX50K4W5KtpcRi2IbMpfmuyHBxZhpy7p+zsdic2KqzfCRkzZUjtqqttVUEFAFvBJS+eGMS9hhYwqiWr/MJ2HzcxxmzeI55+3XGPK6Zs2cNtmzcJsa8/LbkynRJ6N+U/5z+G+PNRWnTtp3xxfe9BoVsCVj9/RyY2c41G6hSu8K3SMVgaa8v/jaFkoNFxjk9uInNw5YMDrHr1w3bf/n18quKXF35JttTf7f7tmzfumTFRWnT3fhIjTQX+bZoOiCTMatnDJx1g774a5e/mhMfSOUdsgf+6Ni+A1sXrzi272DZh6pffGm2wDO6mPL08b+QlqNgXhdlqihFIFUQ4JjS1Jo6SJX2eheq9MUbk7DHrJ05f8/6zbVefzFXkYJSmLitXFPl5mDLnvzWB1g4mIH6eczkNGn/U5yoivnDxvFZX752zex5L7eKFT161a03Fq9aCb4ij04cPpLn2mKEi95ctsX4gdb0v/+yZla/YUf/3F/irip3tWx0UboLWUgDDzu4Y3fmbJdkyeFx34EDffxYc5O33MM1kFa4XKm8111FpLfkXWs2/LZw2ZnTp4vcWKZQ2ZKGT8Ah0lyUBh4Ai1r+9bRrqlS44uorjVjvgENjSWyrJDF71v329s0PdPpp8o6Va2f2G5YuQ4aHurQpUOa6rzr3WjjiS5FP17y1yjMlJz+qtGDEhMVjJucsXKBKk7rFbilHPJEz+g5lEpAKSDLiLyuQ50Iey/+ckUmXPoNH2jm/1hecsqf1GrR/284yNave9kx9obwW8ReCCz4b/2XHd3punj/13Y+ZT8xZMN8TH3fHuoMZbOaHn66f89PVlcrf3uxxmuApzrHyVHjR5xOP7j1w/f3VCpS+zloW423DvMUHft9V6IYSRcqXyZrzMvP0n7/PpMuQnuuKyTOIv+7OivJo1XezmZFkzMttxqxZrFbGAFtnSvHuUB5J0QQ2zv2ZMXnz4w+lSZPGZKE5DDMGKlZGIq+9/QKxNgk0oAgoAjGHgNKXZLqs0E1lkkkR/GM+7sn056atqE/exWgCArxS02fKGJSwVVNmoTvvaNYAOwH05aKLLhAL9MR7VeueSvrQRJ/V7t6+2gtPi2SchXHyJTyt9/8wirT8+lPUG7fHDxzOnP0SSUN2HkmYJaZ4m0p447zFvP3ve/V5bglMevP9hcMnSCn3tG760Jsvp8uYoU6PDiiStTPnkbjp8Pclo7kayScOH2XuzLp+Fc+b58Z8dOVN15N4ZIuOaNynh7z7zh2PMdUCk+iyfKqVNhmBBBwa67OSZDl9wnPapfg7Izx36YKwpWFN2q2dNZ+GV3uhUe6ihS7N74FFfmj6oU3aLv/qe9gJ03zA3m39D+hCqA9aGSvaw2+1LXh9CTicT+6CEGdk0qb3/BmiYrmu/2HR4rGTKzZ8xNh+Nv+0vPc9DSil8I2lIVgMmOovN5OK2a6gSnfQ43QxSIpRB97Z594nmF4sUa0yFYYytpz0KRkdKs+CuHer1SMlyab06H/jI/c+NagnTQCH2QOGT+z0nrXchh91vfUJj/MWdGf8K917/jaPAQNWxLyx7Dt4J9a4gfU8Y4ZByCAHJSsfCrx1SPDXofwpvXljTcx7O1etH/PyW6SE+95c/0ECwAXFx0GecP5S1+YomI9Aqep3cNWfIhDrCIgBhmusNyS0+it9CQ23FOWSb3cIB/+MoA0//nRo5x/+9J9JZgLY/D97pj3q9qG32675fg7xfPjKUxZjo8ZgD6iKb7v1Q59dlj8PtpDfl69GsZWqfjsUZPOi5RCIfg81eW3hJJgBGitDJo8DKUSqR5Xab66clvvKQqgWVBEK/tkv+uUolK9H5dp/HThEGtTYoPovoOxhTqVr3okFhc99bDkop6rPezaP+evQYeiLbSLDKvmHj0fCXaAsz4z8MG/xq1g2DA97r1q9ll9/ghwkMP00/NkOcBcmcVC9f2zYnK/kNcR7//w11qGSstQLKCrUrfXEgG7CHmZ/PFKEo+EMdZCYuUPHoI/hKHe/1IQuG96s/f6tOwteXzzDxR7E+DEVgonIH8GSNA7IpE+SA32ZM+jzce26kR7rRZdfvsdhCAwHN2hFF7T5fiRwoaS3Lr1wwo6ItV4vye1x6aBd9fp0vq1pfXk0uEFLAGz93Qga9fnznWgIyFBVh8rPGzoW7sIgKXn3bfM/G4dF6pJcOR7r1WnTgiXCXQCt7IP3wEsWDJ+ATMYtiXEhZ9R93bk3RUivMaShLwYWcMYCZ502Cqp1Dh0qLaVraPul+fNgH8LEKPRlWu/BcBfqU+GxB04cOcowI7Fpu2TUqyKgCMQiAv9NN8Ri7WO0ztfecQv84ME3WqMO+YdyoiEQkSw5sgfYor9PnBxYrwXaotLTj62YPJ1ZAzIOatASyzmfmzjzwlHgK/CJZ0f358Odb2gSQFm4omkKlyuNNmX2CtMFL3oiEXVRkg2AmRRuxRf459GTCMNd+GzF2NDll6noMGJ+HjMJ7lK31+vcwja2LVlJJLM8XOWXNp2HFvOtLLeoEwJWyRmzeJxVG33yHnNY+Nww+dV66ghi1s1awBVTkEcDTf0Bo06ryUOJ2blmPVfvn0NjHSqZNdeF+Y5H3+0o3AXJTT/rQ4vQxH0fbNz/4We2JjWKeFTs5C7vE0Cpf1T7WbgLhha4CzF3PNuQiTYY56hWnd+qcL8wA+Idfj6RSZ+EOfLhLtAgWk13LBn/LXIWfDaOmSloHCwB7kKgSuPH/Mm/5HLP2jHIcZUm9SQNlHHFNzMJQ/JghwtHfsnck/AJh8pvmPszWe5t15y5oQZ93yp+561zBo9iIkkmrWq2b45xKNMlWZlMhH1S4RXfzCD9iUNHuJKSPn1t4deM6u0rPFZGZgaZpytzXzVoRKcSVdnliNYRzy+o1jl06Mkjx5AGd2Govzzt88qNHtuyeAUxGAJnfPAp/dVh7gRc0UtU8zBjfnBrCehVEVAEYhcBpS+p03fYNpgC4IOef5cXK8yHNc6tsgKFCvF+xxzir2bo7M9f6ATzIAHLebDBrPthIWHUBvMLTBYQxp4hygMXBOYR0MpE7t38O9c5Az8/e+YfAtfc5nG1OfrnPtkwDUrErXzB42JJGGXA1bAQ4ygjjsZ4UZARnSGlLxn7jUmJbvNkTBKIlaVj8Tv5dLZK/vuEp3XZktQtAX6/LVjKVWpycMcuwtAvGF6OAnn5nv592RpPIq+fQ2MdKinVYzLFutScyRFa1H3jjxSKXQFl/3HdFjjfHPljL92Bzq709KNpM6Sv3uaZ578cbCoCTXxt0aTGQ3sR88lTrSExO1auM0+9Az6R+efMGVLCfujBFyYOqdnBM9WyKQmQHavWMzYwpDGZRT++8sNYLCLeYiVGlp7hH2PcPnav3cSjp4e8g+nlkstz1en56v2vtTLZ/VUe3yDSYHfhyizMNUmeImxLA5MjJtsVuY0E9kuUmVBiDicdOpFkq+vPNCiOL5jlJGW+Elc/N7o/kzs04avXe3W85valE77jUVCtc+hQs+nAi98Oy1koP4wK+gvfOr7vAH2Hnw0jmaqOafO21GdF0kYDEtarIqAIxCgCOnmU+h2Hnwd60dQD/ff+vU+kz5yp66+zvL1h+KAc2bwjb3+s9+LxysclxpJ3q9bFUM/38cHtu0VUz9vq8BV+bO9+6AXGcyJlPRE2dpgNSm7hyIlEsohUtB3vd24vTto85mAS3YHf4CGBZFQmGojZjZNHj/HNnftKj7mo34NNWBTNhyy+EVdWuAEihb0EzuGRmeTOuWfD5vQXZ/5h4OcYlvjit0ouVtHj+jr8uVeZ40ibLi2zDNQKG4+s1oZsoW8e//BNWcVzVcVy6+d4+Jn3Tz67ifdubLKVPPb/t4nDSrFz9bo7mz8JrcREAcn7pmvfyW9/KI4mK6fM7vDjBGOq8dCs8+dxEZ3ScwCEpnyd+8o9VGPpxO++fPWdTxu16bz4WzNjYquzT2TEOkWTWTkPiyILMK5NWoyGO7P4BTfs39WIktkfc2sCUujxg54JPvmJxWvbklViNpNIyX7g953+Ki8b6I1p89bqqXOAEYMKGVnXxhQkAaaHGIRZc14KN8KgQrWfHvwO8cf2evyXnxzYQ/y4C91QEudxKs8A+6zpKxhsIDS43TAjOaFDz6GNXy5SvnRQrXPo0JNHj1M01kTMigTyl7qOKxT8qkrlCSwe+w1GL47FoDIN+r29+9eNPwwcCZXns4GnLv4qVqzoojQVpQgoAs4IJDR9iRKPJ2wAeKVADkR1bf55OZ+M/GORpzd9+eHjEUwrsALo4a7tzEc2m87RzWKPQbUQ5oMb/wlMI4Qx/qNUCJw9cwaKwFseN1Xe5iie+h90gQbxiA9xDDZYZdDQfLzmLCQejrc3G9VvzsCRv86Yyz+SQUTQrHe1asyHNawIwwAzHfe9+oLHb2Pg56x2EfqSv/S1JGYnG678mo8dwBXjjZHMrNbDb7ed2XcYC8h5hH3lka7tKj31KFNU3NZ7v3P6jBmNk8StT9TBgcOnznZorHMlacg/py/MbVEiP4r7rueA2f2HM83B7JIoYwCs26sT1AFzV58aDW967P5TR48zMQFWzMExAYdtiX+l770zV+ECf5/yGCegkkx1obyTpNovPpHJlsdjz0Czihs14eptmmGMgVDW6vzSpvmLsetUW7ISi93u9Zvw7aWIPruWiCHHWsCleS7nVkxrEl/ukRprps2Bf+xet4k1RFgpWOPGvNiLk4cVKlfSX+UZezTt4uzZvu3eDzlwX5xdxOn1+S8HjW/fQ7bw4VHVFk/e/mwDKkb4rlaNSIkBSYqGH0987d3Du/cySA7t2sOUHKaXwmVL8fTvJNfpVd/9EFTrHDqUzZN2rFor3kXIz3PNlcyggR5/WQxvhiVuXh6a9cm7eMBAjvEyxtrnOn2RhutVEVAEIoNAGjHXR6awaCuFRfN169YdO3asA4+ZdGJ7q+JVqHmHNf/5drjbkM2Llu36daPxtWRChEUx11SugIuMd0HsXYsiueGBu6yP0OIjnutwdaWbMLfgdNnt1odQM5hMME5AgIQVkX7IEy8e3LGn/ZxxUIGTx45bp0744Gb5RrGKHt3j4QTnz5tcUhAzVmbySGKoJ7YEY2ZgIKE4jfsw3+U/j/762ttuYc4FziRZvCWzWIZHhqlIMu+rd+mSxqGxksBfJdk8HkuSrBWXlNR/1ZTZ098fYrxeULdYVmAzJMBHdeGICfIIc9d1d1Ss1rIRk184l+C2LP4lJEN/V3qyTrWWT9uwkiLk6hOZ03+dsC2HhiAWLlsSI9zBnXvwv2a+A1YBFcDQdUv9B2+odbdVpgkDCBxI7B8SCbOEhSz/ahp+M8TAICEWDDb611/lX7isFA1/sEsbZovYf9lWMYRQk7N/n4Frmt43FbAGTK+BNivMzTo1WkH9maSj34NqHcL9dai1XMKmaML4fTPM8BAXckwM7aKDzFi15Q32lnNFvmjUjlytW7d+6aWXgs2u6RWB0BBAeZERm1/CjjqlL6lPX0Ibu/5yyaYmuJRiULGl+fTpNsx3tJvlMcnEx8+hsSE3EG6HemYZuTFuGVHEMx3jrfagPjBFvu+d1bmRE0IAfYyNLVme5yAZFZ45W1aZj7Mm8658m3zlmT5jJbw1mSthKGO69OnhZDZpKW+dTWAkb5W+RBJtLcsgoPRFXXfNYEgmENazG5MpO5jHskkd/jTemTDbHEpycPF+FKMxDo0NuUVQEFiCN3dBIATFm7sQT2KyhI+7UATWgpRwFyR4GuVrR2DvyucsUuD4/v8caMjr1g/7hzd3QXjKW+dWDVWOIqAIxAoCSl9ipacCrSdaCi27ydfSUBZl4CWKASZQWVGfzqGxUV/36K0gPuBrZy/AIhK9VdSaKQKKQMIjoPQlDodAibursMMHMx22tolb5U+jvrbFx/Stv8bGdKNSt/Ky8FiWsqduTWKrdAcXuthqiNZWEYgJBJS+xEQ3BVfJ2595nO03cLq0ZWPjWvx5M2XzbMoSNz9/jY2bBka+IeUeqs748TnLE/nKaImKgCKgCPhEIKEXTvtEJA4i2aPMetygtUWsSLLexkHYobFx0LpUaQI7tfgbP6lSnygvNBzHokV5k7V6ikA0IKDWl2johXioA6cjzer/mVstYQNfloizQz8briydMMUtsTEth21aBjz6nCtNYB9ksz7cFYEqRBFQBBSBCCOg1pcIAx63xXHmADvwmqOtU9JOlvKip9mKV4RwZGD2PJdjZUmJzDjIy24lbJcHOD5XRQXVwFNHjrEpLRvbBJVLEzsjsPvsCecE+lQRUARcRECtLy6CGRWi+EZ/u8L9bF4SptqgPtsXq8yesN7y92/d7h0ZQgz7BcNdOHiPvWXf37OMkxRxxQhBjkMWh1Y45IrYI0xZ49p29Vmc7Ojv85F3pMNgiKcFaN4Nd44JU+/rLJIz7PpUEXAXAaUv7uKZ+tKO/Lmf0wOO7t0fvqpwdoz3mb2cXyMHQ6a83FVTZiGEbdPYF58tXzlJkVOdUy7WJsFnK5ixmtSlj7fXsy2vu7frZi/gQOnjB/7bauXI7j/XTJ9rKwWEiZGDq2yP/N36Gwy45R7bd9BfrkSI99n7idBwbaMiEDcI6ORR3HTl/2sIx+KwD/2pY8czZs2S55qi/++ZGzf7t+7ADHBo1x/s2XbF1UU4K5vNZtwQ7JGxdekq1u6mcJe2QCpjawX76MtpPukzZZLDIwMRkvI0HG3IuTxs6v/iN8PMznLcsvcg+/RwLma2y3NxFIAcSB6CXc17MLCnMGJTXvOYlmDrfXPee0w3SiuvCCQOAkpfku9rbMJsucu/KDcOc97Nym9nrf5+Dk364P6nTcN6/jaf03nMrb/Avi3bty5ZcVHadDc+UsMoUWtiFOeC4eP//G0bkWtnzX+laCV5ykFL9d9/I0OWzN47zVizBx7GySO326cBm9KdW9F2xhff9xoUlIXDSA4tQH3q9HiV7WjhKxwaxUGYW37+ZeeaDUhrV/jCoVccMPn64m8zZr2YyLOBTQs6DwZsWufCTF9oFxvf2U7OCg0iF3M5974URJpdv27Y/suvnGClHlcugq+iFAF3EVD64i6e//tj45ZpvQbt37bTczDeM/XluDvm2hePmVyqxh3mlETsImtnzs9X4mo5NdBnLqnZ0gnfLR47+fy585WeqiMH9aHdD2zbyRF0koCz/Tb8uIhjn8e2fZuwROKVWfKe26+8qUzea4sZ7sLX/MwPP10/56erK5VnRiZn4QIk5mXNcXqzPxqOOV3yclqvz8ma3xYtG936TUmDreXGOjWLVrihQOniHPBLpPfh2JLSXB3aaNJwgN93PT/CsfSE16EH/loteTkMku38ueI3w0GS191Z0cj8/Zc1s/oN46i/EndV4aRu51YUvbksx0WZvIBDerxcOQSxSPky+YpfbU4G4FGai9LA8+A6y7+ehrmIzWpNRmuAlHDKIjeWliMOrI9+HPwFvdZ3/6qGH3UlnuMVP36suUnA8d3Up3C5Unmv8/R1uvQZuJ47F5BXk/NgYB7KgQZR4QUjJjBiGSFVmtRlPJgq+QsAwt8nTl2SO4fhK7jvcMJ5nR4dJnZ6jy64snyZJwf1FKdjzoJm8yGruY6jsM+fPcvB4yJ/7iejL8mVo+xD1f0VZ4v3ObR8dpBz79OKrzr3wlVc5ONxpQvIbVDrrSIQPQgofXGzLzb/tLz3PQ14Lxe+sTTvQb4+q7/cjAKw3g9/tgMartWkoei/bctWffxYC+hCs8/7Ql/85cIDA7rAzIJU8dcZc5lcuPb2W2b2HfZtt77vbl2InubRkvHfzvt0TPWXn6WsYrcs5SjpuUNGP/bea4XLlba2Df3d594nqEmJapVn9hvG13nLSZ+SgDDeHtT54bfacpYyetondyElCvjuF5vkLlpo4mvvXlWpfIO+b1nlyxnLHq+RNGnmfTJm19qN97zURBgSyfy10SqB8NE/94nyYDKFgwMppVDZkmy1Bxvz12pYy6LPJ45/pXvP3+ahMsWn+I1l3wmZwHeVSCkFfx3WY9/9YmOHVpASzyGsHQQ4qXhki9esC4zZtrjJsD7CQka26AgdfHrIu+/c8RiGE1Rdl+VTDbmREuW6csoszvoWG5U1ft/W7ZAMj1fy+fN7N/+eq3ABdD/6HhK2duY8att0+PvW9GnTe/5aoThc1/+wCFJbseEj/swDzoMBUf+cOo0cZhg5MZu+q9HuOfGtQesPbdIWGGEtOOXgh9Rt/Q/mrGZrfSQMY2b80L/cMoo6/TyZo7UInzh8BFIysP4Lvy9fzemhadOnh7tAXEY274iFiZQM0SuuKcoUIT01rElbwHxt4YX9oOnr3FcWFPrCqZwwdf5kFo+eXOOV5llzXPrFi2+Ac/0PuggZ8je0fHaQ8xge1qQdZkV6v9oLjRjnl+a/wru9GqMIKAJRgkAY6ctPP11Y+Epg0aJFtgabp7Z4br333uZM8GSTeefyzhLWGMwDgxu0Qhu1+X7kJblzvnljTXw4pEQiUQYopNFt3rziqiJ8j5Kg/Y/jC5ct5ZBrxodD4S6l772zYf+3D+/e26PKI2KT2L7iV8SKMmO5MvYGbvl4Rc3zj7W10JeTR47bGjumzZtwl9bfjUDh4SiKfkJRoQYyXOxxCOWHIoFg+VTAkgDngIffbkt43tCxHLAskeYqdqbTJ06OeuH1ZROnEr/7141AgX3CoY0muwSK3Fimw9wJnzzZGkIAcfljwxaa813PAVhE/LUauw/GLeatvu7cm0Zh8KeZLFxCKaLY4C6A/+wX/XIUytejcu2/DhxybsWOlWt7VKn95sppua8sNKplZ7gLmDTo93baDOmXffkdarV75Uc6zp8oDIYpHlgpVZVCoTvsa2xrEbfZrsjFVY4Qgk5hKpDDnL/s+C7xTw95B9bSpWyNpwa/c3O9WlWff4rIvw4dZrSAm1AKYvilT+op6MucQZ+Pa9eNGLq+yy/fi/ykJP9dnAcDHlGMpSN79vZ9sDF0TbLV6vwSgblDxwAjXPbul5r8PGYyW+/s37qz4PXF/xNtCYHzhw80AuEnBnQD8P4PPwMDeHn6KJLArek7AnRooRtKEsC09s7tj8Lay9xXrWCZ4mnSpoWIcIL3K7PH0FjDtnesXAek2fPkJgt05+2bH+Avhb6AcF9WIO+JI0c5EINHdzzbEOOl89Dy2UEOYzhdpoxI5gcD88cLJYH1GiuHuVrrrGFFIA4QcJO+CCN5/33PJ6MDO0kWNe+83jHJCiHB2LFjI8lpFnw2jlcz/yAHfEzzCq7bu5PUE6rRfNzHve95fMFn44nByNF87ACcMQk75JozeBTm9MZDe8EMoDv9D64RboEORl/KBzEUhxKRg341ZRE4c+qU3KIA0LU7V61b8c1MYr5+ozdXtPI9rZuKNNQA6uer198b1aozs0i1Xn/xhgfudiAxZOdj3bi5oE2ZCGOfVpk1EO5yR7MGB3bsXj31BzgHasmhjVJJ6xVVh60Fq8BLU4YTj4MwS3JQjdN6D/bXamF1wIV1pOXXn75d4QHhOj+PnoQEuEv+UtcS6PLLVDERSXG+W5HBM0EDAYK+sO6J8DOf95UpP2w2V5a/HgsWwkvnvfz4gcP41fIPJG975vFOJaruXLPeJ33J/y+nITF0iq588I3WgINhA7LC9BClYI34Y/1vUjGuadN5/jCxFQl9ISMjIX1S3eCO0AtIFZNZ09//BNubw147wnFtgwGjBTN9x/Yf7F+7Gdyldvf2sz4ajsfP/R1b/nPmzOQunr9fSoFSYPBjrPrjLhCyoY3bMnigF0CEyw6jAsrIAjQMeEKq7nv1eeEuyJQJSpqMhYlbEsONbmnwsNiTmF0iEiY6vr2HmclwOpd08AVryOEuRP70xVdiG+O6cf5i6IvD0HLuIJ+93/SzPguGT8CYBKvDSHlfxxd0axxg158iEJ0IuENfPvjgA2Et1kYa6oDtxIRNAu8Y88ibrHjHkNhm0bGlQb5DEaYsFwM7Vq1HM9Xt1Qn7M1MwjYf1wpZg5POy5n0qt7c1qSfchVuHXKiE08dPnDp6XAwbhlKg0tCv6LbDe/6c0qO/yGTantIJX7CC/HWSMB/6b5W/r/2ccZgxuOVDf/faTX9s3Fqn56u3Na0vGbli2y9xl2crl2+79//kqdawBKZI/Ckt0lMx+Jlk/+KlLjAMrCNMeRCD3YVpC9QzygwNzRX64tBGEWK7si4GTibGIYgR/0jg0OrDe/aSIMnK0h/FjOMLqpcYOAFXgOLKz8pduPXZCrw3eISzC9aL03/9hYTMliOi1kz3mBPkd3DHLgK4p9BYLEzQi9+XralQt9a/z//7PzQIOZDOqb0GEUvT4BxjWr8FzmLwIBJ9L3WWbMKczpw4+b/Lssv8F+QVbsFTugmW9sLEIah26MumBUsd6IvPwUCPgAlDCGkcIkFL6Tt0Nv6quKQwUGu2b54+c2Y4bvU2z9zZwmMN8vn7Y/1m2gJ7A0koy9h/N6pZ/vX3VCnLZZeSC38jk3fT/CWEKzZ4mCtWk69e70Xgqoo3Mq9EYM8GjxFo2ZdTxX+LaURu5UdNoBFcYS1YMZn07FCs8t6NW3nqMLScO8hn78OZ8Am7+fEHfxw0Crr8XrV62D6f/Lg7f87/1kX/rwhECwIoVtSuz6mJaKlimOuRUvoCaQBBoQ5CF1q3bk2dU0IdvPN6x1DESy95bN3R80O/iiGkYf+uplaig3G/Hd++O09Rdewsgp1j9bQf7+vwPBTBIVfJu6ugn1699vbydWpizUavnDr6F64b19x2M94DfWs15pPUM0/fshH2HnQDqpRy5VW7e+3Gf/6+e87Az4m5LH9e7P8Eti1Z9VivCwYhbqVuB37fOaXnABRV+Tr3lXuoxtKJ33356jufNmrTefG3hjCR2PqDT0DRMLoc3L4b5SrzHbINGtrlgddakRjqhs7etnQ1YYc2WsWaMCuxCR/atce4znDr0Opjez32pycH9pC287nPpBtokwXPnner1kVD86XOV/7Jo8dQqOBJep+tuPiy7Dw6uJMF4f+79vaKqFJmQ0rfe8epY39xdgG3uLDggs1TFDYNfPzDN2WV1lUVy62fs5B4n7+rq1QQ61elpx/dMOenz55pT7LWw0aAjKSnj3asWmfyilcTGj39xZl/GPg5vUxfYIMhAYU2HtpbjBP0+NoZ80wu74DPwUAy2WAQogky3Jaufjv0hbU2xe+qzO3KKbM7/DhBLDfcYhHhP2kmt+YnM2hTuvffs/635RO/Z3wybfTRI82w3FRt8aRkh1Wb9ByuyREQ3W59CN4m5IlHxatWomnF77yVIc08FLNRtz5Zm9YxAcrclslbr09nJvVoO/5AgAabwR2Ypw5Dy7mDfPY+fbRz9bo7mz8JMvjd8+fzTde+k9/+kIV1piYOgV3/nCjvMd7pTxFQBCKBQLqUFGKMLtALWItPkpES+bGVly/pTfMXY72otmQlUw+712/Cv5LXNC6u8BXa0m7WGF67bCY7ps1bWClK3XMb9MVfLjacrfX6S6gf+AHsh+y8u5kEYX3yXa0as+EKzq0Eqj7/JEtIJnV5f+fq9UJf8A8gJXMB/CMXH8eY8cs9UmPNtDlMr+xet+n6+6vhUwnd4XP2xcnDCpUrSRH840MT79G/k2adqDZTIcbsb+uIQjeUwG+0TX7PLv6UdWfzJwjQZK5wCCE9XGt1aT27v2cCyKGNYmYgjfV37W23cMuckZW+OLT6rlaN0IjYJEQIa75wLsZbCKtSs1H95gwcySQI/3gKDxCUCPtsBRYa5mVyFvI4n+L8cWD7LpARVx6MFpivyte+TzxG673fOX3GjGZzmlufqDO4QUthhFIN6/X+ji9gi6KSzNTgizrkyZce7PwS8zImTdFbyjFfZm7zl/bMdvV7qKnEMNVIIFuSOwiOOMZ0V71NM4wxzA+aGCNBAj4HA49kvRsEWpIx53XDA3fBn2BXkG9IRp8aDW967H4sf1sWr8CFhTkglrbZhDM4nxrUE+8fvK3hHzj/Ql4f7NIGf2TMJLmKFCC9dXk2vAROs/LbmeyYB7tlCAGI+ImzKOmzZ1458PsuD8J17mNkQl8Yq1dWuAEhcO4CZYrXaPssQ1Q4U9UXnhrxnMcj22FoOXeQz96nN3G0YtDinZM112VCi/GLx6RqM93ZoNBbRUARiDwCaTyfViH9DHeJsItJSJX1nQmjUd26dZ3rP+nE9l5PPo93XuUWT1Ru0dC3oH9jcU78tlu/FZOm8yXKVA4v31vqP3jJ5Tn5JH32i/7X3uHRyt4/n7lkjbQk9vf5a0RRHIrTqFL0DV+NLJ245fGHUOGSDNvDt937Lf9qGk45xKCkPUu7m9bnSxd/VZafiHmARyiVSk/WqdbyaX+vbPyFcR3l87fcQ9VvfbKOeFmSkWrAZqQ4rkxJMAsjLpDJttHkkgBf7WI5sMVbb22ttj7CLcNWee8Yf63wzIKdPy/mDWQCLDYbbmUixlqKLexdhDUBjr3YVGQizBpvwjb0MLz9PPprmByUQnx3SAnFsVUDg1DhsiXZRdfIsQV8DgaqSjON7YcsR/ceOLZ3vxSE/8fCERPgEMTDsa67oyIWPrP83iafeSiQtHYWIw24GLSbFy7Ds0forC0Xty2yFYfQWE2V1jQwbOCC7vhjhCY+8KFl7SCfvU+dV02ZPf39IdJ26sP3BoZJ2Iy1brYwL4cvGrUjsu+6eQ9e7CHx+lMEIoAAKlg8KFBhESguCosIkb7EAXehM4KiL48Pey/Abet4SzLXYMhEgL0eWq4AhVuTnTh8FH8O77kA3t2sJ4J/+NM3ViGhhSPWxtCqp7msCECnWBbEunRrpFthRtrLBSuw+LlK47quyHR3aMGNaD4+WGJpc66h0hdnfPRpmBBQ+nJRaMiKo65OGPlEj+/+YLkLckLL5bMCzpHUzZu7kIU3NY/Cx10i2UZnBPRpIAhAZN3lLpiOWEPEpCel42fDlRmcQGoSSBp3/3z4K/D8maRJE0jRmkYRUARSBYFQfF8gfdQV7hJtzrPhQHD32f88EsIhX2UqAgmCAJQF7yv+4cvCBCutzl/qugRpuzZTEVAEXEcgFOuL9xpp16sVVQJ1W6qo6g6tTIwiUKxiOZZkU3lsMKwSxxfYXetOjMKi1VYEFIHQEAja+iJrpCksEUwvoWGquRQBRcAbAeZ37n+tVflH7x/T+k3O9MYl1juNxigCioAiECACIdIX2dwlwDI0mSKgCCgCgkCea4rKfsoKiCKgCCgCKUEg6MkjWamV4Fu8pARxzasIKAKKgCKgCCgCKUQgROtLAtKXAFdNp7A//GVPif9NSvJ612d70nYg3vHhjnG3FeGubTjkp+IILHTT9W61yK1WuCXHrXapHEVAEYgwAkHTlwjXL3qKmz/gc3+VCUGjqzL2B6bG+0MgFcdMKhbtD43Q4kMjPQGyt5syeo4W158ioAhEBoHg6Iv1bKPI1C8aSuGVx+t7/oCR0VCZVK9DaArAVu0A9YE1lyvlWgVGVThf2ouTrY+LHCJ/uuSLs9YnbhYbhoZhgLkC6UQrqhpWBBSBlCAQCn1JtCMu0bX+1K2z3cX5dWbWcKWk/yKfN8BXuXPFXBHiXIQ+VQQCQSC0eXDvd2BocgKpoaZRBBQBnwgER198iojvyOaXXNe8fdeobaO7HMhdaQ6giQO4QwJ9FFUIeGtrF6vniuJ3RYiLjVJRioAiEG4ElL6EG+Hwynf3re2uNIeW66ZBDuDoI0VAEVAEFIFkEQh64XSyEjWBIqAIKAKKgCKgCCgCYUVA6UtY4VXhioAioAgoAoqAIuA+Akpf3MdUJSoCioAioAgoAopAWBEIjr6Ib4T6XYa1S1S4IqAIKAKKgCLgjADqOGKLLZxrklpPg6MvqVVLLVcRUAQUAUVAEVAEFAGDQHD0RawvCc74DHYaUAQUAUVAEVAEFIFUQSA4+pIqVdRCFQFFQBFQBBQBRUARsCIQNH1RA4wVPg0rAoqAIqAIKAKKQOQRCJq+RL6KWqIioAgoAoqAIqAIKAJWBILedbd169Z169blCLexY8daBWlYEUhdBKLNJSva6uNi74gJ1kWBbomK2oq51UCVowgoAgaBoOmL5IyPV3Pcv+wC6aZA0pjhYgIpWTwfWommaA0oAqmFgOtvDLcOkwq8YoGnTC2QtVxFIEAEgqYvjH5+aCB+BAIsRpNFHgE6CDtZ4H0U1JvUIXHgJSaLiYuiki1LEwgCDJuohSI665YSKp+SvNZuClCOvLTVcG6FTsOxi0DQ9IWm6vxRrPQ36l9fVbHSWVFSz2imjNFZtxg6f/SDDz4IkOhEyWjUajgjwF9EdHJ652q79TR0191ERs0t9FWOIqAIKAKKgCKgCISAQCj0BcbHj8KUwYSAuGZRBBQBRUARUAQUgRQiEAp9oUjmj7iy/iiFxWt2RUARUAQUAUVAEVAEgkUgRPoiBhisL0ymBlukpo8YAgloHtuyZcvu3bsjhnDqFnT8+PGVK1dKHRKq4akLu5auCCgC0YBAiPSFqqsBJhr6T+tgQ6Bnz5533nmnLTJeb7/66qsbbrhB6JrrDf/ss8/uv//+eIXOuV1utf2333674oorjhw54lycPlUEFIEQEAidvogBhiLDYYBhxS9iwyE5BIw0izMCx44dsyY4fPjw6dOnrTGBh3ft2tW8efP169cHnsWW8uzZsxs3brRFxuvtmTNnaNq+ffu4ut7wkydPTpky5fz58/GKnkO73Go7cvbu3btp0yaHsiL8KAEtshFGWIuLGAKh0xeqaAwwrvMM/sZwrOFXuHBh14VHDNxEKGjo0KHZsmXjE7NkyZKVK1e+9tprL7vssty5c+/fvz+E5n/00UcDBw788ccfQ8grWU6cOBFyXu+MjRo1atmypXd8lMQIfTl37hz1CbnhEBS6b/z48T4bJdzI56OgIr/99lt/SGI3otN9StuzZ8/UqVPNI5gxLM3cpjzgYtsdhsqOHTtSXlWVoAgoAjYEUkRfMMDItiLwDHdJxu+//y7ciOoqibH1WVC34f7YmjFjBvXhK3Pt2rULFiwQy0f27NkzZswYVD1JjC758ssvCeTIkSPYvCY9pqDLL7+c2+nTpzdp0iQ0FmWkYQ367rvvzK0EXJFskxna7V9//UVG0OZqGh6CKCwEP/zwgy1j5syZiTlw4IAtPrRbOMovv/zinRfW9eqrr/7xxx/ej4gZMmRIzZo1P/nkE8L//PNPoUKFypUrR8Bn4kAieU1RnBA+Se9W230OFcEwhYMwkHYFnkYWjQaeXlMqAlGLQIroC63ijyFMNhg2gxISY+SLJSbc+jhquyo6KzZ//nzowsGDB//++2++lVetWgWDwY30kksuCbbC33zzjbAf7DfB5jXpIVJ58uThdvDgwViGHn30UauuMskCD9AWWke7li1bhopyUXLgdfCX8tSpUzwS+mIa7i+xczzNxNCyfPlySIawoixZspAlJVzBlIiVCAAxzpkYE9iwYQPhm2++2cRYA4sXL+b2vffe47p69WooGnJWrFhhTRN4GMsNLxOIVNeuXa253Gq791C5+OKLKcgVDK0V1rAioAiAQCi77tqAk00nPTM9ST9eEC5uQ2kVJfIpXQiN9ZGtSnobGQTWrVu3c+fOp556Kl06z0CCNwh1CKF0vsKff/55yRgC9TEl4nYjxhtGC3odjYLuDMEUxGTHwoULZV1Pzpw5RX6JEiV+/fXXFEo2VU15AMqIEKEvpuFGLH6jcH265rHHHrvoIh8fKqhVDBtCGadNmyZWK7I/99xzH3/8cdasWQm7onrnzp0L87jnnntM3Uxg0qRJhKtUqWJirAHBnxpu375dqAxPhV1ZkwUexkDYvXt3TEoutt1hqAgFdHfCK/DGakpFIM4RwGLvyo93OqZd+fG65NYVsTYh1lIoK+WlIIRdtG2lxMct7Qp36/iQ5c8DZ1t8F2CTjz/+ONZ+rB0hACiU9IknnkDgkiVLjAQMDKjSLl268M1tIiWAbuvYseOwYcNYP2wewTBq165tbq0BxNarV++OO+6g2nAa6yPCOChgPUKrERarhvnLx4TDSMPOhBnGlktuqUn//v179eqFnka5WtPAKrjlOnr0aCbarI+8w5TbrVs3ashfEDJtCZiDQIuTxsQLaHJrGk7TevToYbgIrSCXyWINWCeMoIzPPPMM9ioMMFBJks2cOdPWF9a8hCGv9FelSpXeeecd2IntqfUWPoR8yJY1kjBDpWjRov76S2xdwmXp5bvvvlt6ZNasWTY55hZeMm7cOAxRJkYCEAggrV69uolPYduNHOehIg5Jffr0MeltASrcoUOHm2666eWXX4Zq2566fsswBgfXxarA1EJAFG5qlZ7q5XocDlz82eiF8BjX+YGtFG5DbkK4FXzIFUt5xgjQl8aNGxsdbwIos2Ar//XXX5P9hRdeEAdSNKhI4O1fpkwZI5nhZJT3559/buLRcHPmzJEsqG1cXiTMV7uhKeg/k54AfMhUEu51zTXXyFPMAMI/UDmwHJgE8SalCRjJEJqnn35a8sqVCpgBDxugbrArCJA8ZVGVEWILIAo1ZkSREd0maWBFsAR5RPy2bdskHvwLFCggYdNw4ZQkoyMgTAZMW3HcUrF27doNGjSIxPfdd58tgThQQ9qIRw42NgMyMcRTHzJWrVqVACYNW3ZzSykkwG/XxJiACJkwYYKJsQZAj4wjR47kajqIMAYbazJrWLyDYYrWSMJffPEFGYGLMPYzrilpu024w1Bh+FGugIMxjAowp2myQ5elXfAqksHPzKMwBZS+hAnY1BKr9MV95Pkj4SfImqtQGeLNyz2FBScV8l8p3IYgMO7pS2iwBIgk7rpvvPEG7pDY5Pm25gWNPuNFbLNAOEtjWSm5MB6Qi09nsq9Zs0ayoBi4pQgmQSAlhKE4PCIlWfhwR6+TRSwNW7du5RHxkoYw6UeMGEFA1CTpMWmweoWASSNuEGiOiRMnokfJYmU2r7zyCjHezTGSSUyYClAT2AbF4VtKDHMxlPvpp58SpiyuoqgGDBhAvM+f6DAaC0UjGVmEpmBsIEydUefgTAOhMiIBVoRYCZuG9+3bl/TcoikNe/NZoomkzkamicRYhRxKF4EiE41LAgChyVSJqUP6ncCDDz5oMtoCwj98/tVLn3rDKxKEzMEzHn74YUrnx8I0rg78GKJAAqgtDafvYC2IOnr0KLXlRwfhwEQCBqqpZLBtNxltAYehwiBheNAjFM1PxgbZpV1CCmE2JAiwv2xFB37L20CtL4HDFf0plb6EsY+EYfAHY0iMLWA4jaTkHefzNedQRcloxMqtQ3rbIzIGW6JNQtTe0i5aByCRrCGWcF7QS5cuDbBQlBPEhSzUEz0nEwSo5NmzZyPhgQce4JG807H/y1NWqYhm7d27t5SCbiAZMyncmgBh9AH1IdCsWTPizXSMzBARLxvMMAglhjkXkqGMeSQ/JqeIMVYQJpgk3kgWr1LrrBDKkizMBZBSnnKLUYcZDSQby9CFAv79H17qJIOO/BtxXlpNxdC7ZJQ6QF9Ixk/mg+69917QkyxECgLc4o0hqIIk9C5ZpQhRwMolciBPLMYhjJMsMsVuhBWqffv23ArmZpkhlIu6Ec/6LMnufaXXMBF5TynKxIpYRLxziYc+05E8orEATkEQHcrCDuSdXmJkmRj0RXiwZJcpNix8pIEZ22obbNv9Fe0wVOgmCgUEZhgJiKFLiBS3ZgMtEPYn3K14/sqUvrgFZjTIEcUXDTVJlTp4PC7D9xPvWrnyPpJXEmpVApSbFPeTcwVsK/0qVqzonZ712/xlIo0rT7nyzkpw314DsjdcbsVgOEGtmg5iBMviWLN0CJcIXEdl+ah3oTCSpk2bYsLhkegYSYOyly9v7PzE4E+Dew3Op8xTQBRYILN582bimfV48sknc+XKdeutt3KLqwQVICBKjkDBggX5tCWA8uMKgeDKL23atBKAJBFo27YtMVho5NsdFwTokVSALW1IgKLFHZidWNnbAypAi4xk0cHsmyICuQqXkprIfrjoXbbHzZQp01133SX1MYlNQPxkuaWSApd4QwMOZALFRgUgT2brFGbZ2rRpA7kRtmRrOAqyRo0aqHC++1FXkJgxY8aULVvWFGcLINxsrYafCghDgMQvmILoju+//57OwuxB6ygXPyRIFcoYQwJ+zUzWVKhQwSZTbslFl6G/06RJY0sg/X7dddfZ4rmF1EJrCMgVtOkROpqFPAw2MUd55yJGuBSw01ncspaNhvM2wEcH+xAxVJsrryChFISDbTtZfP78DRX+BNi6hhGIJzgNYeaLzQDpOCFSUHYC0Ggq2aJFC5+S3Y30+f50twiVpghEBoHw0hdrG8xHhpVV2OgLrxWy2PSu8621CGuY1wHSUIpGuVqfatgVBNCO2OeZHEHNoJ6xXqDP0HZFihRBPlTm9ttvRxljWuCprUS0I2wA7YLnJsqvfPny6Fd8Sugv9r4TdS57pvFaRw8VK1ZMPl7RBEwAIY3EqCueoiC5hUOIjhSXT2LQFkKnOEYAswGSUVqlSpXCkZZ93FHwUs+GDRtSOg1BzTOFhLUA5Sf0BZWJHBQtirNfv36YNIRVGMmyGBj3FwgWj+BDKCQsGZ07dybjn3/+yRVpsnbpxhtvZEIHOiJKlEfmhxJlFgOugMcJivbSSy9F7WEIkckprtQBOkIkNItWQN34OwIlpm+gCELIpOFMor311luYnerXr4/tBGYP4cBnGS4olTeFmgDbDNJxMCF6itoKlUQyCUAbdiJLt5AGjyGSPqUVBGRHFpGDSvaWL7wK/Q3a119/PWwPEga9oAm33XYbGZlzgQyBJ47JxB86dIgFaMihvXSHOf+BISGlkJH5NRZRly5dWmKsV6EvMo/z5ptvkhIQMHvQd5IMnAlQB5Mr2LabjLaAz6FidnyhF2gjWZgwYrhCFgXSn3/+2dSNpz4xtBWkt4qAInABAd4vUf6DhVh/8BLvH5+YZv7IGiCjc+tInGwaZwlR+xSUaB3X8NUQ6GyamNUrZt2HeS/LZIStGu+++y5DkEkW67SCEBHYgCQmAaoUPSSDFX2G8uaRSGYmRaZIeAqJkTU+GB64hRuRDC1uXDrgJeKHK6LIiIGBos0nLx/o4j2DkkZnS61klkqycJ08ebJUzCqZhhgQ0JRMGKGDJRkTBDAPCXPFcQTJ/nxpUXWtWrUyoggwqgFz+PDhUgF4lfhJCI1DhbPWiUeoQISbhgu3I56pNwTSIyLTYUZPfIykFGooOCCWGPilqT81JwZag5exIA8xwh8FDOkaHkEKTWITEIcVEW69MtFj83o2T7FGUGefLr3yMdOpUycj3xYQcGSCCc4KlbQ1nGEArTG5gm27yWgL+BwqjASGBNTWJMawRNNAlQEm6+yoD5NxGNjkQ4sFXyax6wE41z5fAABAAElEQVR5c7ouVgU6IMAaN+tLwCFlCI9E2YWQMT6y+FhVEVsNQ4N6cxdiiOeXbFuUviQLkXMCiAJ66O2332aKx7a2GW2NmvH3OuZNCqWwCYeCYCdgpbTEo8/EeYX5IAwPJrGoKFGWaH2+Wc0jqNK8efPMLRYFE5aANbHEoO+twok065sIs5AHVY2FybjOSC6bZDQ6P3nkcPUu3Tsx1hQrpSMBJIZmWlNKDbnychTXV2vDoU3G3RUM4RbM+zgULZM1aHr6EROIKcgGC/FMHmFBIYCdBvIB10E++hiLETsmm4y2AN3KlBzWMuxDzDbiOcvfptSHooknBksVxjYWY9twtokCGfFxBiXbI7klAcTL2oO2ZFTG+jSEttsEmlufQwW4bL5HGJbEiYpqwFrEYgSM8Bi4lLVuRrJbAaUvbiEZuBx6Vn6QGNd5TILTlzR0w7/wxsz/ZcqJN6B1Xkm+XYKdLWInX+y6kjdm2h9YRfmk420FINbZusCyRksq/An4speVwNY6MXWCiwaftkyyWOM1bEWAP22YGQzDe07HmiwlYTgENM64OqVEVOB5KRT1LxN/geeK5pSMZPYe9Lm1oLvV5ihcfF9i94XgLhqRkSbGY4iLKY4ws+qwVRMTcgD9RV5x/A9ZSOxmjJzvS8oxgqygj62UBZkwD5mqj0sKknLQYlrClVdeKe4vtlbkz5+fGKwdSl9syFhv8QQKN7HA5ybcRVhbJGEKjSfuQqMij6E3qhoTJgSYqUQyV3gMk7/8DJUhIE/DVHTci412+uKioSXu+zL+GojDJucj8rUtrqmmgZhMCeP7Ygzv5pEGFAFFQBGIQgRgKvygL7jnC4PhKj9qy6MorHOUVylK6YsaWqJ83ESmeiw8YTEO/hZmBYqUizMKcyJMIYnzY2Qqo6UoAnGAgFqpU7cTmTPiB1kxk0owGKqkPCaEfrkohDxhzQJxYT6POVqZJOKPjR/uKUzviZMKt2GtgAqPHgTq1KmDfcV72xicOV577TVZqho9tdWaKAKKgCIQIAIeU0zSCjiIC4SGXMJgmPMVZhOgnEROFnX0RTpDKAt8RX5KWRJzjLInBzvU+ex9lmzISUmJiYzPVuOry95oPh9pZJwhwGpw1ijRKO30WO9ZSAzrB+lH6Iu0hQAkBpOz8hjnzo26ySN0VcL6UTt3lT5NIQJseMqss3WfjwAFsuEYc1g+WVSAEiKTjP1a2OeGBcyPPPKI6yWyGpnN+Fmsjt80G5mI+1HgpbBFIRvNsU9M4FkcUrIAm+1k2IGGBTsNGjSI/q5xaEtoj9jmhy0GPvzww5A7XZwwQis9rLlYlRNW+SkRLmaSlEhwyJtkjvlvUsnj5Zvk5yu0hqc+8zL4malIwD8B0Ig6+uKzh1I3EhbMMErdOiRbOvw9rH9ayVYg+hOw/xub96NB2UksqNqis1mlH/0vCPYXoV1stxNU6wJJzPi3uh/xumS8BZLRpGFzGoS4Ql/YAqd48eLsSSPC2QSIvVuyZMliykqQgOwdHHKn82Uf/a+1BOlK52YKfaGzYDD6krdipfTFiobvMCMmyv/OqSE/37WP1liOeuGsIraOla0LIlBN9kOjFFZi+6MvDlXi1MAI1DCFRbARHBKwQosctv9nC2BancJ9X9gIjo388ZXmxAA4HEyOo7D9VdUBQzmpyl/GwOPZ8hjuwvnbnMjIJntshZw+ffrAsweSEgzz5MkDMZITKwPJEuE07GQoJdo6PfBqQECjc24iml+2qVg3ig72myHwwRCjKZW+JN9xYtNLPp2mCAYBtjtDCYmndjD5QkzLBm5s/EpmB1Xnr0rmiJ8Qy45UNjk/kvkUKZCjudngld1sZZuckGsB7UAOzkZyNCbzU/z8SfOHIYdGWU8a8pc9kHj84TgfitkoErMz0IsvvhhIrmDTsIsx2sJKX2AM7AHNwVUcFBqsNNfT87cju8XYOj2ogvzNRwQlxPXE0Vkr15vpTyA0RWiloUp8mvqzu/A5IWtc/EmL73ilL/Hdv9HeOlQjWg1Fywl2zAg4cIsUtoQiRILR7v4EeleJzfHERO8vS5TEsx0+NbE1kC35mVuBVWA+8Xm8c7KVX7VqFWlq1qyZbEqTwBtDSjdPUxKgHyEW5uTtlIhKNi98F1sd4xM/Sg5eoFF9+/YlF3SwWrVqyWYPXwJ2QkK47Nnos9PDV7RKDhMCQlasM3pJJvU73NqfN0zVTl2xSl9SF//oKp3vS2hEuDcv59QbDkhavHgxjWf9Mz9BgSOQrCf1uAsNKlwEcjC1t2TnKnGws5jovTMGFYMQFI8cNRxgRlxGOAuQIwxxPTGnLvvLK5oMrQbhYFkWZ1KSsmrVqiY9ut9n800CnwGZp0h2js8ZQ3xTjLeKz1ICjJTlNhhdAkwfbDL6iGO04Xxk5BRuOe2SMIdUcBTXiBEjGLrmSPNghbuVnoPGECXWF9PpbglXORFGQMwtxtZC6Q7mlgjXLcqLU/oS5R0Uruqh21hkW6VKlXvuucfsaYtu4/zksmXLYiTnG5fDiuV4FNQh5yCiOSpUqIDxnImAwKuFnMGDB5OFhRL58uUjI2tGjH0YN5T69evfdNNNnBd49dVXO4tF97MqDetCzpw5TUpe5RkyZOB2w4YNHFjNUcPWD338NjhQhvTylue9IIlNdgk4V4nJI2f6wtbAnDCFKLwxGjZsaBNubpnvoLYcfNGuXTsWvt588814E/NlDzvp3r073/q1atXijCfYEllwIsHRVY7g5lbUp4jatGkTS8rNgQlk4URJtvITfkA8t/SXJMa8jOGEskjgwF1oIKd549cCViQuWbKkeMxwuLcsUGf2zZnBOGOYKVMmqY+/ayAYcvznp59+igQqY5PDHAprcKi2xNP8WbNmic8jTYOR84NgcYo1Y8BKBDn8mRVVjJOmTZuyYgsQWFclQhhIDFoOCbrhhhvEcMU2idadEv2BRnbnYWmrPLeMB87Hhl/WqFGDY9ht7kqssSKGTpc/VeHiQl9Mp3vL1JhoRkBZiwu9g5NaIv8S88RpdKEZOmg1Xo4yBohEAfPW5le7dm1UMucKcbCwSUyA71GUnKRHj0IL+BjlhcsW/kSyE+7dd99tTi3+9ddfEWWy84ImDZ+2rVq16tWrF/ESI9IcrhgAONnKiMIPwySmPojCRCGlUFvzaPTo0ZIFkgStIQHGf/PUGnCuEgqPRpEeVUHRHTt2lEOeRYIQF7bXg4FRBPrPKtkaZrErThtwNZIRbty4MU9Jzy31FDMJPIZIYJeac5LzuHHjwBYWKKIomvQwG7kVn2LJhW2AR9AgzgCHgEJruMXKJSkdrri2wHJIbH7wWrqVLNTTRNLAJk2a4DMLV/CW5owhZ1kjBwIKUYDoMAKppxESIIYyZqQ+9CkHy3OStsjBn5d45npEpoxwBie3EA5aAaRyxARXOQKayjRr1sy0jgCMB2YAuWQYgz+5TA0lwDhkCknCDqCRwGFY2mRyO2zYMGs1YF0mzcCBA5m3kqd0igw86kDMyJEjSWY63WRxCICY+eN1SKaPwocADlW8T6zdzS2RoZVIh8axCksWE886hUT+xXHf85VP67ja+ldmbXg1E5BNUGAw8kIXUzmvS9amSi75fiWenfv5eOVdiQLmbw+7OgnQBHydw0WIqV69OlpT/iwxs0v2Bx54gBgUMIpB/D0JyyPUCY86d+4stw5X6oZwEmNUoNxOnToRRqBkoW7oeFlMxBWtg4LkEW8EknFLejiHsAHmBRwK8lclWkHlUecAhUx+MBiRs3z5cm6pAKYp9ChhfwyJ9EKhSMOntmRHG1F/YIRB8r1OAFE8wrogLeIW24wkluuyZcuQ8Oqrr8otq2O4xZrF7YABAwgL7eBWJo+w30hKh6t0Da9R+Cj2LSxw1IofomBsAjh1Mx67MDB/0vxhKCMNFxxeuFSSH9IgEMgJHEOsHZhPyEtvUltqmCTJw1oAijC4IRAKIo+EZkFfAFOGolABrIkkk5PMice7WerA7j6mXYxzamhuJQDNQrKEHUAjAcmokvewtAnkVvgr8LJGjPoTYJxLsq5du9IoqDPzqtJwYTbijSR/SrZO95ZvjVH6YkUjkmFeR4xYfjJiuRImkl9KqqH0JSXoxXzeOKYvjGx+3vRFFIn5KJdXJBui0JdCTTDRm35l5S1/aVYViAYiBnMLaXg7iy2BGMIoFd7aXHFh4SnKhnjzCYuO4REMQISL0VvkEAM3spo0JI1c+dxHDnpabnmbc8vnNbcwFcLyQ1t/8cUXhJlE4JGoK/QxYeO3CyDc+vv5qxLLT1B7YlzBXCQ6SQifaE3aRYArpRsziXcpsCgSCNWQp2J1IBJ+ht4iAAWUR8gxu6RgR2GtssTTOyQT1SWQctutWzeeSmeZCkD1eMS6YskIQxJiJ7fW67333ktKusBEsr+fybtu3TrCWAh4CseCThkjh0lvAv4wFC0L/ohCPUvvyCAMCkMKAmeIIAGaAyzwLcKQCfqIAD/h3BTECORWWsct+MuWmGK3oB/5UeGkTOelQyXMVSY05RZuKqhiC0GOkCQH0ByGpZFvAmIBgrtIjNSZMDhTFohJTI8ePbhlkPBIFpvI36yt041YnwGfLwSfKTXSLQQgKDbWIsTFFfl0aByrsGQhuog/Cf0lFAJodNqL1YT3NQHZjkwUPJPrJkYwkWWZVp8JzDA8YmBJAmZtoCz80I7E4E+DMw1zRoRF5pQpU8RgwBFFzFCY1bPiQCNTIUhj2ZHoBhFrvU6dOpVbth4hGWnEHWTUqFEoFaxEkhJyA0+SD2L5kqYOKJirrrqKGQTykgy1h9aXFlnlm7C/KlEQMyN88sItoBQiTb6AMcWjfZnUwCGX7V9pqWBoZFoDOXLk4BbnBhO5YsUKoKNW6FHAYWdbo2uRA1WCiqHe0P1FihRhPghzhTiRSCe+/vrrQhfgE8gUviiettyKD40gjC6kCKw+pmhrAHMF4FiXLBlDGsmElklvsjQMxxGH06b8YSi+R7QUuxFjj7YgWY44CApDcoEMs5MEcBvCHYfXN2HKpY/oKSZWDH2BkPFIKAsclCk2EoODTDXSHThCCR0nmc3dhKfiYsIjuLKs05Y/BMnuAJrDsESa7Sfwml4zjmizZ88mZdu2bYnBQkPlucXCR+kMA8KSxdbpNuF6m+oImF1PjbnFRmhSvYYxXIFkCU58J4hj6uqPmBMv4xVDCFoE9cktpmk6Wr6Jsf+bTmeOhqdYZVhwwfe3+C1ih5APUDQBT9mMX2ZVxEgjn4moVZxPpSCuKABZVWQ+kSmCFzc6m3cxX8Okka9JU7QJiKrD6CIfMfIFSXp0oRgG8BoxiZGJlwy3VJIwvhF4DJAYj1p20ycgVgST3hbwWSVpnZmvEeKCPzJ5xWSCUrHKsX3Hm0di98JKZGLES5SGmBgCkp0llLipYu0gBvmCPN48wleop9i9gI46CKrQAhpo7Gry+c48F9RBCmJDXmtBJiz6HmCRBlUSxOgysdaIssTrxaR3DvjEkE6hbvBLaR1XkuFohaigMCQ9fJGBZ6uDaHfkI5b+Eg9fAZYYfmZU441LArKLMYxaEUMYExeDxFhBxAWbDZelu2VQwbRIj8M12R1AcxiWtmpzK3N8iIW54nbDVCy2JTpaGCQ9C0o8JSDEnXoKQRe/MVune8u3xsjfjjVGw+FGgHGSwhkihxrSoXGswhwaLo/U96VQvPqy+RvZ0AhUO3MKvNN5LXLFpCGjQXQMrgDWcSOeEKTkh9rAWM1aHkkAb5B5Il7o8k4nno9dKBG6U16yaEQhLmSHghjPDFIKW0oS/D94jCg2a9ES5ltWzCqIRc3w0UkF0Hk0kE9P1DM8yeTCICFVEqcQEf7KK68gnB8qH7VkEnsHfFYJL2YUHl/2Jj1FQAi4lTZSMaZvYCdoGsLU1qS0BmQJD1cTiU4VboRRB4dWNBNaijqjNWXrKpop67/E+4fqkRdPXpJRirgJQ9HIAq3BeEMAU5DIx+4iXSwgAJ0p1xYgpdVBG5ToNeAyyYDaTAKaSH8BnxhKd8hcnmRE79I6wkFhSHo8q2iRtXpEwvPoEcAHN9DD7kJYXJRoi9WpBRIALJ7RcOYMY1vwRyCRQPrbb79J9YRrCnQ8khlJWDsxMrvqAJrDsBThtiuuLdb5BaoEp4E7Mh6kApBXqQA2JCojPSs9Yut0m2TbLf3oPZtsS6O3MYSAv5d8DDUhJVVV+pJw9AVljH5i0PD6tro7EMNrURYQeQ8pFC0/Wzzf5fxskdwimSsfsrx8xZkDwmFV/5IFioNW5lsfj1qfi1mskrEciFiJ5OUu60qsaQgTb6oEVWIBrZUwIQGbvy2L9dZnldBGIGNNxkyK+UzHTcQobPQxPAB9bE1swtSNxNZW8Ai2RxZ0ElihcTEtYCUinhLxjEGgKDCulGIlakYsAeMZAxe3thctiOUMnWc1+VgzWsNUDKYIDbJGShjKaEPAO42J8YkhT5mmMWkI0H2sHZWYwDEkPTB6D0WrZAlTnL9kdKg1vXXMmHjGCUvDIPqsmTIr6XgKPqQ3yRxAM2kI+CzCmkDCtooRCWGy9Yj8HQEy06OSy9bp3mJNjNIXA0V8BBKcvqShF837MQEDzJ2zBzmf/vHX9rp16+Li59064lltK2bwsLaa12upUqVQnHwmhrWgaBCOUoE2hXxwIHlRt7KTh605EDueCr+xPYqz2xRiGGdohKM5/O2zjY1s5hQO+Sozwgj4e8lHuBqpVZxuW5dayKdauTA2cZkMdw1kkzq+gMNdUDTIT3ZbNudK4p7pk7uQS5xhnbPHx9MUYhgfIGgrFAFFIEAEdOVRgEDFajJvwxJrWFglJMsxwtoq9DE2A+tm2GEtToUrAoqAIqAIJA4CSl8Sp68vtFTWleBpG4GWs0iYXVuYvI9AWVqEIqAIKAKKQOIgoPQlcfr6QktLly7NEs1s2bJFoOVskYLzKX6LEShLi1AEFAFFQBFIHATU9yVu+xofPdmd07uF1k3JvJ+6GMNp73I+i4syVZQioAiEhgDvhNAyai5FIAoRUOtLFHaKa1XydnxxTbQKUgQUAUVAEVAEUg8BpS+ph72WrAgoAoqAIqAIhIpAgpvTdPIo1IGj+RQBPwj4m7PzkzxWox2aGTdmv7hpSKwOMq23IuAfAaUv/rGJiycOOsahfaHlchBoHkVguzwpK3xNMG3xF4hjnWdF1aGZ7EzvD5xEjndAzBmWED6yQy7LuSb6VBGIHgSUvkRPX7hcE95fcIXQFIlVS7lcrQQQF8foBagUA0yWAGPBnSaGQPr9ZQmBCbnTBpWiCLiNgNIXtxGNGnmoEE4MiJrqaEUUAUVAEVAEFAHXEFDXXdegVEGKgCKgCCgCioAiEBkElL5EBmctRRFQBBQBRUARcB+BOJ6tdgZL6YszPvpUEVAEFAFFQBFQBKIOAaUvUdclWiFFQBFQBBQBRUARcEZA6YszPvpUEVAEFAFFQBFQBKIOAaUvUdclWiFFQBFQBBQBRUARcEZA6YszPvpUEVAEFAFFQBFQBKIOAaUvUdclWiFFQBFQBBQBRUARcEZA6YszPvpUEVAEFAFFQBFQBKIOAaUvUdclWiFFQBFQBBQBRUARcEZA6YszPvpUEVAEFAFFQBFQBKIOAaUvUdclWiFFQBFQBBQBRSBZBBL8bFSlL8mOEE2gCCgCioAioAgoAtGFgNKX6OoPrY0ioAgoAoqAIqAIJIuA0pdkIdIEioAioAgoAoqAIhBdCKSLrupobRSBuEZg74n//XkyrlsY8cZdkTniRWqBikA4Ebj84nBKjyPZSl/iqDO1KVGPQN16daO+ju5UsGiZiu4IioiUK6+/JSLlaCHRjsDWlT+lYhW3rFrEH86nXV9KxTrEUNFKX2Kos7SqMY/AlpU/3fVE62CbwUst2Cykp6wQcrmVJXVLD7oVI4POoRliHYGirnJWV/g6QpL+2JW+BDS4lL4EBJMmUgTcQqDaE0G/m6r9L+gsrtR2yyr3CVDqft26AosKAYFI2quKlkkU2xh/caF9qyTmmFT6kpj9rq1WBJJHIBxqIxwyk2+JplAEFIG4Q0BXHsVdl2qDFAFFQBFQBGITgRibdU1VkJW+pCr8WnjiIRCOGZnEQ1FbrAgoAomOgNKXRB8B2n5FQBFQBBQBRSDmEFD6EnNdphVWBEJB4HzSL5ScEc8ze1TfZdPHh6PYtQunTxn4djgkJ47Mo/v/GPdu6+OHDyROk7Wl0YmA0pfo7BetlSLgGgJHD/z5RdcWHasXeadBxb2/b3JNbngEwbJmDO8NgwmH+OUzJsyf+MmR/XvCITxBZP66cPovMyduXrEgQdob+Wb+9JP7K/4i34oIlKj0JQIgaxGKwAUE2GoiwiuHt6xc1K/5vavnTslVoOhN99a7OHuOADvjzOmTX/Zu9/vaZd7p9+3YPLZnq7+O+Pj+PvvPGcr65+/TkgshJuwtx2fMsQN/En9wz3afT1MYuWfLOiT8dfhgCuVYs8O3uj1WjlZbI+M4vPf3jbTuxJFDYW3jn9s2dHmwxOE/d4W1FBUe0wgofYnp7tPKKwJOCCyf8eWQdvWw81dv/MpLg6ez5UzWS3M6ZbA82752+dJp4/45fcoSdyG4cs7kFbMnpc/oY7v+339diqXnyz7tJOnYni92fbTsgd3bvIX4i9m9eS2PMmbO4i+BQ/yZ06e+GdCFVvtMc+r4UWFFadO5vGEECEMTfRYaf5Hb1/1Coy5KmzasTTt14vjpk39tX788rKXEuvBbbvHsiLNoUaKMPVt/ufxnbJOut4qAIpBaCKz/efb499rAA57pNTb/1aWDrcafSdNM+a4u5Z1xz+a1+a4qlSGTj6NZdm1aQ3rITc1mnbJcmvO35fNRQut+mln5kabecnzGHDu4l3jy+nzqHHlg19aFXw8jTfZceYqVrWRLfOzQPonJlOUS26OU30KMMEcd2ce0VJpcBa70CU7KS4kGCYBMNTJlzRaByhzY/fuRfbuPHdyXLn2G3IWudp13RqAJWkT4EFD6Ej5sVbIikGoI4N4x/PVGFN+s9/h8V5UMoR6rfvymcMnymbNmt+U9feI4DrD+jj7Yu/2Cbw3WiDxFi8NdyP73yRM2IQ63p08e5+mll+dzSOPvESU+9sr7i78bfWT/H95ppDLEZ70st/dT7xiMRlgaMDOUuf3+NGl8GKrPnf1nydQx+3d61PnGpT92fbScCLn5/oYPtermLTDZGObafNq0/GWkAn9sXb9r42oI05WR2ppWYMyW8wp/tXKIp8Lnzp2DizikOXH0ED24c8MK0kwf9h7/JHHDNwaXrFTdIWNoj/4+dWLnhlX7dvxW9IZbcxcoGpoQzZUqCCh9SRXYtVBFILwIzBk9gALuqNeCK2YYGExQ+oZPXqaBar3gY5HO+sWzkelPkWxbswR7Dxpu49K5h/51XDh/7lyyrf3nzN94yWAXEV+ZYmUrmywnjh2eN37Qb78suLJ0hYq1nrosT0HzyDONNesr5N90b/2SlWsQX/auR/hnEhA4f/7cyWNHLs522dkzf3OL+1HadOklgYd/fD8WR9QceQrCOaBrxBM5d/zgBRM/MYtripQsnz23Dzq1bc3Sr/u+JqJodZk7axUuXi5vsZK5CxaTSK5/bFm3dc0SGlXwuuuxgTmwkyXfjZ4y6O3Xxi2bParfmvlTL7s8f522vbLlyuOz+ej4qZ/0WPr9WCkoR95C7YbPM4UC5rJp444f2l+iUvW8RYubeOf64LcEMlwpPUu2HFeV+68LyHjy+BHamOaiCzTO2Z4He96yYtGhP3dipSt43Q1Z/vW4mtCrLfLve67z1CHddm5czaNH2/VJkyaNtYaLJo+YOaKPxGTPlff6O2sVuPZ6iGmu/EUkEgK9ecVC3JggbQWuuT5nvsL/1955wDV1tWFcZYiKiOJeiBMnCqLg3qtuHHXWOqt11j2r1latA1s31vVpVax74t6C4ETcinuUSh2gCIp8Tzj2GhMSQwzkJnnuz1967rln/k/KefKe95yrnF0lDGPe+/fxKEfEB21fhfXTMtWb4BbuNdiGduPsB24qohMOXofWzoNRrZR3fa9mXWz1Ws1UaQxvDUuA8sWwPFkaCWgkEJEMG4TGQnR5gFWMoO3/Q8qrwYcOr1PoGFxw3e00fmFuF1dxq/3z3IHNSJCkRjkTsB5FYTpRL+Hpw9tPH4TX6TTwcuC+S8d3SwngxyCF1QOYXfAL+4j/QjxCsZi2EXCtVEekxMKB37B2KLZ4xZrHNix5En61+7TVeARRsuW3scG71ohk10IO9/x1bZHyVSAUsNMK07mIh77Z7DsKcgoTmFhOKl21sXgEmbJu6kC43EK13DhzFCalUWtOQT+hlj3LpmOqbtxzNGZf6J4ktQsKyV+iXI22fZzyFdq1eIpLucqtB08TJYtPzPeYHZV3gGPi7DJpacGSFZSTSWGkRzt3Lp5yasdqpLHNoFib09R9OBXB3pPTuVh1n16Yvx3+m56RBQ5ACwa1hGxCeP8qX5iO2o7whcFDe3sgd+A2NMb/NBSGcEP+YdkhYY14dPOS//RB2LMGJu4N2qDYUlUaaFodA9UTm5btWvKJ8cln6IyKDdshI9rwMjJi1cReD65dcK1cx8rKWkW7IA30CsSZY858u/ymVPPpUc2nFyKlC1p846xhkrJEvHeLb7BSqcmis27qgNdRLwYtChAlHNvg55THGfIFLVk4qBWAoy/l67SETJTkEVJCuy8a4oP+QjlBJuIrWuvr76U2pHQAO4+EU0tKV2Tq5VO+mPoIsv0koEogJvqliMIchlWefMXLPbp1ad+KmX/+1Hew377POhBAGQRtWwW5oG6wwY9R/FrFvK5aZeL9mT1/4b/QEFid2TZvPML1vxl6dP0isVclySyIhMCCdoEkwiwC71psbEakjV0GkX7r3LHQLr1n+mNxBDuhMLNigkxnZX10/WJol5Je9Vr/8CtOIpnbr0lM1HNkObXzz8Nr541dr3D5xPnr634ZgEmoTscB8fHvgrYpJJ3tfyWf2vEnSkNfarT7DgYYnGWC3sFMJRlIsHAGUw3qEi1R/8QU3rjXGMSjKIm5lOzk5uXQLpAsnSYsyulc/Nqpg8c3LV04qGX3qauKedSQkkkB4e4D7dJiwBT83Bfxqyf1TrL71rbpRQJM8yrLRqd2rsa4w027hGctrMLAQoOSm38/WXt7EtKkwVwesHQamGAsUOmtcycgX2DCWT6mC+QCmpQxs+PloP2oF0ilZqsEbl8MFtqlzdAZUAnQASEB6zBwLyIeoUkZHbJBfyBL//k7NNlvsudzadZvIuQF5MubV1HK5UOZiSXRKi2/hUqGjfD8wS2Qm49vXek9y199gQ/fZHwHIEFEIdBhGOLM2XLiFontMjmgywhDNCsvG2EJD9ihgPvM+iuTY/bZPWrfv6pYyeIlNwIa/8+UW0PZHhIgAR0JSIsj/eZuLVCiPHLhl258XOzBNXMf37ok/TXXVBocPmD8x2Yl9QShR3YgUtjeVZ7ChyBw6wrM1oXKeDqX9gw9vP1OWDB+SeOAkEc3w1QSS7dYSjjqvxBGjt6z1mNSvB5yWMgXuM5gRkHGSyf2IDGmVXyiYTXb9xV6InDrShhUvh79O6z6qPTn3bdEPOZ1zLWY9mBHOb5xCXINWLjLKW8hTMPCwANDS8VG7TFFwcSCp5A7qAvGG7RBOAlVadkNU9fuP37ZNGfU0Q1+DboNgxVKi4hBIfCPERMhwli4iX0dlSmLk7BPtB/1e6EylRCPJS0s5WAzMBqQpHwRHjkwBWEVA+lxael+h7Hz4XazZ+m0paM6QWjW7TJEMupg1QZ5odjQZow1dANY1ek4UHt7Yl4qxB9SorU9pq327VXv4Y2LiLlwaCt4fj1mrlut5tgifvPccUTCUgWDRLp0VgirXEIdQlt4JJpbinvWwr8lw9uHnQiAfMmcTeF1hIAm7SKVBsMMwvhSiRisoNnYphfKEoia9v0RZhusSWHU7OyzQJpDPkJdSdlF4F2cYrkQS1T4RONhXkJA2Gnw9RiwcOfhdQvxJUF3vJp1rdnuO8dcCssfNJ/CuvM8cuPsETAoQvEkuYqKlLyMS+DDQqZxG8HaSYAEDEgga678+LOOAtPbffyV/OTONcRINn9Mt/gtm2SlDxJ/azrlc1F/ei/xGJisufOrP9o2bwLKrNysC2ZNGHg6/7gYqw/QDYVKe2IyePbkvnoWxEQ/f4pcUDnQLjCibE202SD+0jHF2tOT24o2txs5BwYGzO5Nv5sAcw5icGVwcLSysZGWpSR5kTlrdjzF2gQ+/757HRYItAGSYuu8cYhBLVBg0C5YwkC9mGXhNGNlYwsnoW9/XokE4oLaG7R4D7QRbrEP3LdXffxw/+9hEv/FxBmX+Dsez7b8NgbrLwjExcbg0z6xPSLP7dBTCCTA0JHUJbyk4Wkhrado6T7mYO/mXUevC4HKhIUDRp3/TeiBNRcUjF7jE3MwPmFjgEcqAlhP1N4erLghGXRbl4l+kIBFK1SDGxNi/nkQjs+yNb7C5/ENS6AgwRDo7idun0akyvU2NhYxwsIhHmFY4VwswkJhFK9YS9xq+RTmt7g3CoZQHr496sAuBbGLWxgFJUTx795B8iLyfXw8PlUuYWgURzXCFR16GgmkDWiQmF/1GTdi1YlKTTpisXV6lypQyZBl2FgHQdxx3AKsKEEU4jcAvkUqJfNWDgRofZHDKLANJGBgAlgTwe9y/Opt2H0k/tyfP7QVPzHxZzpHwaKoCZOx39C2NuntRq4OlJYhpBaImWDVjz1KV2ucPqM9nB9hpbeysqn/7TAs3CDZ732bYIUobZq0LyOfYI8P5jwY84WTR8WG7UU5+GUsHDbhcQKrD8wb0oKIVBEC4vc0XG1gwDiybgGETush07GbBvuf8cPX2laxReX+1XNY+5ByicUjzCiwpkztUAlWAfxojn39CjYPeKLYZ82JlGIyhmEGczBWr64EHcDUVaFuq9JVG62e3AcKpkj5qkgGH53+83ZIq2mYJjFX4qi0A6t/q/l1P7faLTBtI/HORZPX/tJ/yJJ9kkiSGiMC6Ck8UeBSCmdVcBC7xGGFwtO/ZgxFx9E7eJtiiQrrFHU7D1LJLm6RBoFXiRJExGjpPoxSMKRVadUdPhmwHJzcugIWiL0rZmLH06vE4/y3zh13NWh/trzOMKigtPQZ7LW3J/r5P0jWdvgs+PogkK94WazNYTggVnAL56RXL/7FOhTWlbpOXjq7e23II+HpjKfKV94ipXAb8MdUOErjjES4x2KYUEj7Eb6IT5d44o6QWcq51MMQXsiFPmIBCPvw0RJ8zWBWRKVY3oLOgEsytqmf3LIClkIITQgO9UIwXsXcq2O5848RHcAfVjdrm/RQKvh64H8KjHKWHHkq1G3davBU6Nd9/5uNpuYpUgr/X6A6lObzw69SmeJbJ90yIAcClC9yGAW2gQQMTADbRvrM3oBzX2AAF0Xjb3eLAT+Jn634vY4f0In/otXlS/m6LUOP7oABA793lZuFLc1N+ozHmgJcK4RnqHiK0hxz5IGbSN6iZdU3PBcs6Y556Nz+jVgTkX40S8Xi53iZ6o3Dju2Gl6tikhv1GzxgoJbgSXrnYjAWIOAqgQkYs2CpKg0hpG6HBsEA0HP6GqzpYKKFVoBzLkpDXigDG7uMjrkUliGc+4JPyDW44MD1EmE4uDT8djgCWE+BV4dHg7aYnADHb2gbt9otIX3uXTmLurCuAfGBYvEPjjXY5fQ2TmGjgi8ItrpoWvLA6ThQYBNblkZKtKRKq28RgMKDZwz2TG3yHYlbLHU16TUWBx9j2QK36pfwNHr/TiEQxVW2RlNN3YepBrPv8Y1/wPMUVgQhPjBezftPxrCiCziXRezfgeCAGwoUHv5paU/1Nr2z5yss1rlQe0mv+rsyTIHhpEyNJkf/WizW3SAZYQkDdshE1AWt819LP/4X9Xb7eeWOhZN2LPqgOKu27oH9YmJ/ULbcCpdqIYI/5tEQKuzmjRODxjRUWAFhBXGtXBcBVLp5zmjRHtyi++1H/+ZStrKGMtK0HTEbbs6QlWg5jHwwGkG+4OskaMMp59CauVDYsEoK2Xfh4FZkwWvhYXWD17BT3oJ/37mBJTN8ASZuuQQ1r6kixqc+gbSJPzhSv1651Ojs7Ozv7083b7mMh1m342JkmoHfti9czhtzZKp1FIeXRD68Aw2hfM4Y9rMEbVuJ6UH9bDepYfiJDEsAFikwH8Nojz/u6TNlxrIUfg0jO+ZIeDngjz4WTaKePcXkpH5CjFQUJhv8XB6wYKdY0pLipUDEvZtw1czlXFya2rG+k7gIZYN1H0zD+MENFwSkh0gqqdjI2lXaaSJMJspum2gSdIyQSvidjYz44S6mK5SAuRNlikU0uI+cDlgHPYR4tK1ohaqYxbHcA6cTvHRJuN3gERSAZ6P21dr0StLbAwngorF9/o8PblyEtQYplZdO8BRdw6cWPngqLohCLFgI+4eI0dR92CGuBO4FWNF4JMYEX7N9P0znoxs4w5YAqxsYYku5+o5fHdsjebeAJ45ChoVJeUM4nKyVv1EfOqD0H3xDYIDBgCqbrDBYMIM5l/JQjlTK9EkQ61Y7FkzE+pFHA0jMFjCKSI+x7oneYRDVxbeURlMAIhgexDC5oQTYkGDnE18tpIdgbdhjJL6HzyMe4VsXdmwXegG7TsFSHu71fMTOfE3FGiQesmnJsPZDhgwZPFjXPxGYwjB/YRYzSANMqxDKF8oX0/rGmnBrjSJfZMILP39hqNc0/evSSEy6mAuVZYouuXRJgykKYkjyd5ayYK598+ol1Jsuc62UK4UCmroPNYb2YxONZNmCd7BX866NeoxKoZaYX7HQMThMDwOt0jUIOCwI6qI7VTLqfSvkS7LkiCXLFy4e6f1NY0YSIAFdCcBso2tSDelSbhZRn7dEEyAIUq5SDb3UGK2pJZBWKo+y5iko1kE0lsUHnxKQtsp/Gp0GaluFrUoC3hqXAHceGZc/aycBEiABQxLAhvMbZ47BcmDIQlkWCciPAOWL/MaELSIBEiABfQnApQmbceD4rG8BzEcCpkGA8sU0xomtJAESIAFdCJSt0QSuyta2Hx1ddcnFNCZKwJL3ndD3xUS/tGw2CZAACSRBAPuolV/fqJwCe4giH99TeYmjcgKGScCECND6YkKDxaaSAAnIiAA2pf/czl3lvTwyap9aU7AV/PfvGokz/dQeMoIETIwA5YuJDRiba7oEUu2N06aLyLRajtNHcDwrTgc2lWbjvZVoKs7PNZUGs50koIUA5YsWOHxEAiRAAgoCeI3RtvkTkmSBo+uTjP/ySBwOi0Nc8BKDLy9KlCDeGYSz7AxVIMshASMSoHwxInxWTQIkYBoEcHz+teBDKm0V58C+TjFjBl5IiSPp7l09q1Kv3rdvE1/gLB1trHc5zEgCciBA1105jALbYEEEXNy8LKi3ZtRVHC3/Our5y38eYwkmc9YcON1fHHcmVmRSrqORj+7iJVB4UQOOBs5RsJj0gkk9anwbp3gdNOWLHuiYRYYEKF9kOChsktkSCL8QVLeL2fbOLDt29dSBu5fOPA6/jN795OMm+pjTudiQJfvFi5OSuxaDY/5xRL30zqYkoeGFU3gdz4Nr5/EUb3vGP5Gs849+pas2TDKLLpFi8YjyRRdWRkzj7e1txNpNqGrKFxMaLDaVBEhAVwI4dhZeI5iqld+AiPlbvNsIrwO8eeaYe4M20hsDoCruhJ2+d+UM0hdwrZDLuRjO48cbE1eO7y5ViTcy4nWDeGc15AsiRVHx8W+lBCoBWGvwxumb5064lK2Ety7j/dVIADcaNOOr7ybsXvLzg+sXC7iWbzt8tvTGIlFC4Lb/ifdF4xavqsarklEpXqmdPV8hkSDJ1opH+MSxdeHnA/GeKbyHEuXjbYviUeyraATk8AonqakMkIDeBChf9EbHjCSQPAJ/v05eeqbWj8C7uNg9y34N2b0WjiMooWb7vtL7C6d1rFyjXV+XcpXn92+GRw9vXGwzbCYCeOX1xlnDpLc3I6ZQmUodxs7De6q/+m58/Nu3108fhuWs47gFeCRdYh0H1SEGSzyH187Dm709m3QQCbDc4zes3dMH4cUr1jy2YcmT8Kvdp63GI7x88WVkxKqJvR5cu+BauY6VlbWKdkEa6BUYYBxz5tvlN6WaT49qPr1EmeJTS2sha05sWrZryc/K6X2GzqjYsB1i8AZKiCHlRwyTgOkSoHwx3bFjy0mABFQJYP6GMrh++oh3i29KetXD2fkH18wt5lGjSPkqIumdsOATm5YijIk87NguTO1QD5t8R0K7FHbzaj1kupW1beiR7bCazO3beMDC3dVa90Ri2FEgX/BqYuXX+wkzBsQNylwxthvU0uk96x1z5UN1yLJ17lhol94z/V3KeW2cNfzi0Z1oG7JkdMh29dRBJOg/f0e+YmURUL+y53Np1m8ihA7ki/q5Mlpa+8/9W0K7tBk6o0z1JncvnQ4JWIfaX0Q8qttlMFpoY5dBvTrGkIApEuDOI1McNbaZBEggaQLn9m+Cdmne/6fm30+GjLh/VeE+AumAz4SEBBzTcvnkXqytfPPT8sa9RmM6fx6h2JZsl9Een50mLHbKW8gxZ94abft0HL8QiWGeQTwu2EjwKVxfEUAJitg0abD2dP/qucU/tLXL5IBKEXNyy3J8ProZhjPiEAhYOm3hoJaQNV7Nuwq5kzlbDsRDTGjSLngqLlFpXOJ2IcRAQkE/IaCltbaJ6qROp4EeDdulz2hf3LNWp/GLIMvCTgQgI07dTWdlJQrnJwmYOgHKF1MfQbbfxAgULsedRyk4ZNdCDqN07+ZdIVZO7Vh94+wx3J4/sBlLPK9ffjiuDToDqzbOpT3x6NGNMHzGxryGEFH2aVXZJg0pgGRCPZzZ+9e0jl4wpSRmfIUakbfvb5tQaTH36rCs4NGT29fwtN3IOTC92GfN0fS7CfW/GYoYXBkzO+KzeMVaihutl7CUxL1RSBZ0x7dHHfQIYS2tfRsbiwSZs+WUCsaW74fXP4iw9/Hxwv1FeqoSgJ7DWXwiEjW+iX4pJYB44lusJRoMyIEAF4/kMApsAwmQgGEIOOV1RkHLRnWGRy2We8rVbAp/lB2LJl8J2p/bxRWPsIoEnYEArCyQHXfCQkpXa1S0QlUsAPlPHejqVS/2dfSFQ9twW7lpZ9fKdUWzhPdrxN0bWDw6uWUFXHdhSpGOr+0wbkGWHHmRslTVhhBMTx/esba1xS0MMzACiRLwKRaP0lkr/urCtUWK1xRImzYdWvj41qWEhPfnD26FNQjvYkRiLa2NfRWFBAF/TI1/G5cxSzYcfHfEfyEKaT/CF/G2GTLCbgQVki5dEjaYRzcv+Q1ti8NsRq4OtLZNjyUnCLXWg6fBm+fMnvUbZg0HzA5j52tqLeNJIJUJUL6kMnBWRwIkkIIEqrfpfftiMDSEvaMTnHbrdRkCu8vJrSvg5gKZUrtDf7c6LaTqq7bugQketzXa9cE+HczWoUd24BbbfGA4cavVTHKqxa4fxC8b/WHXe9fJCu8ZLO7Agaakd70SnrVwi8u9vs+BVb5Pbl8pW6MpzDCBW1ei/FJVGkb9G3E7NAjuNT2nr8mWWyFBhPEmMZO2j8Ju3hBeYxq6IFHBkhWEnNLSWjt7h24/r9yxcBIUmygXfcSmJ6HqYHDCkhZWxAqUKK9eKxxlYH1J/BcN+QJiSBP5+C4+sR6HT8gyfPIiAZkQSAsLoUyaYpRmODs7+/v7W/I7x42C3TIrXXMtzegGzlP3KuYDXilKALt+MmXJKnxNUBH+ykEuiH3OyvUm/vVLgJFDRCIN/GRxIotthkzKyUQYZoyz+zfCeFOpcQehZhCPpRbsP5IqQgy2BaXPmAmyBpuusfkZbjc47w7xcEAp6V3fq1lXNAOmHWzAVs4lqlD/xAbvHQsmYv3Io0Ebt9otxDm/Ipn21kKFwACD5TDlWtCke5fPFnB1U3ZAlioFtKBtKyGYilSoishLxwMi7t2A9IEkgmEGaqZCfZ8c+QtL6RkwOIHw0KAlw9r/vty/RR1dl5jnzJnj6+t7964l/lWhfKF8Mfj/gywwaQKUL0lzMfdYbCCCjJBEkrl3l/3TnwDlS7LYcfEoWbiYmAT0JMDXTesJzvSzZbDPYvqdYA9IQHYEPlhNZdcuNogESIAESIAESIAENBCgfNEAhtEkQAIkQAIkkOoE4CaV6nWaZIWULyY5bGw0CZAACZAACVgyAcoXSx599p0ESIAESIAETJIA5YtJDhsbTQIkQAIkQAKWTIDyxZJHn31PPQJ/K05+50UCJEACJGAYApQvhuHIUkjgswRwqMNn0zABCZCAJROA3y4PWdDxC8BzX3QExWQkYBgCS4a3FwUVLudtmBJZisUTCA8NtHgGBGBxBChfLG7I2WFjEcC7pnvN9Eftty/QDPNFg8DZ+ovwGSNzzgzGqNXQdeKNCoYu8pPyxOvoc2b8JJI3mghQvmgiw3gSMCSBsk5pcuEveIHEEx2+Mp9zHYz0p3awIceGZZEACZggAcoXExw0Ntk0CRhppjdNWGw1CZCADgQs+X3DdN3V4QvCJCRAAiRAAiRAAnIiQPkip9FgW0iABEiABEiABHQgQPmiAyQmIQESIAESIAESkBMByhc5jQbbQgIkQAIkQAIkoAMByhcdIDEJCZAACZAACZCAnAhQvshpNNgWEiABEiABEiABHQhQvugAiUlIgARIgARIgATkRIDyRU6jwbaQAAmQAAmQAAnoQIDyRQdITEICJEACJEACJCAnApQvchoNtoUESIAESIAESEAHApQvOkBiEhIgARIgARIgATkRoHyR02iwLSRAAiRAAiRAAjoQoHzRARKTkAAJkAAJkAAJyIkA5YucRoNtIQESIAESIAGdCfCN0zqjYkISIAESIAESIAESMDYBWl+MPQKsnwRIgARIgARIIJkEKF+SCYzJSYAESIAESIAEjE2A8iWNJa8dGvvrx/pJgARIgARIQB8ClC/6UGMeEiABEiABEiABIxKgfDEifFZNAiRAAiRAAiSgDwHKF32oMQ8JkAAJkAAJyIRAUFCQTFqSms2gfElN2qyLBEiABEiABEjAAAQoXwwAkUWQAAmQAAmQAAmkJgHKl9SkzbpIgARIgARIgAQMQIDyxQAQWQQJkAAJkAAJkEBqEqB8SU3arIsESIAESIAESMAABChfDACRRZAACZAACZCAUQhY7MmrlC9G+b6xUhIgARIgARIgAf0JUL7oz445SYAESIAESIAEjEKA8sUo2FkpCZAACZAACZCA/gTSJiQk6J+bOUmABEiABEiABIxHAEfuWqb7C+WL8b50rJkESIAESIAESEAvAlw80gsbM5EACZAACZAACRiPAOWL8dizZhIgARIgARIgAb0IUL7ohY2ZSIAESIAESIAEjEeA8sV47FkzCZAACZAACZCAXgQoX/TCxkwkQAIkQAIkQALGI0D5Yjz2rJkESIAESIAESEAvApQvemFjJhIgARIgARIgAeMRoHwxHnvWTAIkQAIkQAIkoBcByhe9sDETCZAACZAACZCA8QhQvhiPPWsmAfkRiImJiY+P19Ku169fv3//XkuCV69eaXnKRyRAAiRgEALW+pXyKP61esazp4Kzp7NTj8cbGVQiAwMDVWI03arn1ZQyReP1eKOEt7d3spqUrCqSlThZzWBiSyYwadKkiRMn5s+ff+HChU2bNlVHMXTo0NmzZxcuXHjJkiV16tRRSYAXqPXp0wePSpUqtXTpUn5LVfjwlgRSlIAcpsvU/L8+ee88Ah1fX9+4NO+fxr+5FxKaoiPBwg1IQPkrBV01ePBgAxbOosyDwK1bt4oWLSr6Urx48atXr6ZNm1a5a+fOnXN3dxcxnp6ewcHByk8RPnz4cO3atUVk48aNd+3apZKAtySQagTEbJVq1RmqIjlIEEP1RY9ylKcqZNc+WyXP+gKyOsIt6FlOj6bLMIt5qDTlUUMYXxGVb4kMybNJqUwgOjpaqvH58+dYIbKyspJiEIiKipJuIyMjpbAUUE7w7NkzKZ4BEkh9Aoq5Ss3wn/rNYI3JIqAyZLjVMlslT76IRZ92A/pYuxc1G4GSLLhGTPzlQqqETZbd85apfD+M2CNWLSsCbm5uPj4+GzduRKuwhKSiXRBZvXr1+vXr79u3D2EsM6k3vlGjRrDKhISE4NGoUaPUEzCGBFKNgJitevUf4uHpJSo9E/LRjeHV21RqSFmPD7Vrr+95rPbnRnj6r1GbVNghzdZlvtpnq+TJF4GwdGWPmPLORsBp2VV+uV4sbZt9d5pllk2RvddGYMOGDbdv37a3t8+RI4d6Oqwl7d279+bNm05OTlmzZlVPYGNjgxUlrDrlzZvXwcFBPQFjSCC1CRTyepbng4Ao3FwnJZHaLUysz9EotWqt1LhNKusE+aK1fWnS6LPz6NKpM58plY9JgARMk4CLi0uS2kXqDfxjktQuUgJXV1dqF4kGA8YlcPvCR4uLcVvC2g1OIHnyJbm7aQzeXBZoEALaLXIGqYKFkAAJkAAJkMAXEtAyWyVPvnxhO5jduASS3O5u3CaxdhIgARJICQJapr2UqI5lpj4BfeRLVEJqeT2lPg9zr5H2M3MfYfaPBEiABCyCgD7yxSLAsJMkQAIkQAIkQAJyJUD5IteRYbtIgARIgAS+jEB4qK4nvH9ZPcxtBAKUL0aAzipJgARIgARIgAS+hADly5fQY14SIAESIAESIIGUIqDlDYn6yJeo93TdTamhYrkkQAIkQAIkQAKfJaCPfPlsoUxAAiRAAiRAAiRAAilHgPIl5djKrmSe+yK7IWGDSIAEUoAAD31JAaiyK5LyRXZDwgaRAAmQAAmQAAloJ0D5op0Pn5IACZAACZAACciOAOWL7IaEDSIBEiABEjAIgXC+stEgHGVZCOWLLIeFjSIBEiABEiABEtBMgPJFMxs+IQESIAESIAESkCUByhdZDgsbRQIkQAIkQAIWTOCz28coXyzr28EjBy1rvNlbEiABEjBTApQvZjqw7BYJkAAJkAAJmC8ByhfzHVvNPdPyFgnNmfiEBEiABEiABORCwILkS+yr1w8uXpULeLaDBEiABEiABEggKQIRMUnFfhpnQfLl/Pb9v1Rt9eJxxKcEeEcCJEACJEACJGBiBExGvrx9E7t1ku/F3Yf0Bhz/VvGi7Kin/+pdAjOSAAmQAAmQAAnIgYDJyJcbx4P3zPIL9t+uN7X4t++QN+F9gt4lKGcM+nPzT5Wavn8XrxzJMAmQAAmQAAl8OYFtftMOrFukXk7cm9d+Y3tcP3dS/RFizh3e+fyfx+JRfPy7mOiXSSYzj8jkyRcjunyGBRwB8czZs+nNPe61YjEtQxb7f8Lv/a/vmLtnL+pdFDK++Pvp46u3XkY8VS7EICUrF8gwCZAACZCApRGARvGfPfr50yfqHb9xLvDIxmVv3yThG4JcM79rOqljNQSQEeqnZ8UswXs3qhdiHjHJky/G6jMcVg77/YnaM2Z10LsNWH5C3gwOmS/vPw7byYK2fV88+Ufv0kTGiJt3/r5xG0royfVwxBiw5C9smKbsL98rVtB4kQAJkAAJyJbAo/BraFvRcpXVW/jg1mVEFirtrv7o/vUwREbcDz9/ZBcCl4IO4vPY5pXqKc0jxtokurFp3AzRTrvM9no3WPi+QL54d2kdHfns6qGTUf9EZsmdI7kFPgy7dmHHgYsBh5FxTtNuUvZpN49/YclSUQyQAAmQAAlYLIEzB7ei7yUqVlcnELTLv7h71UwOWdUfPb5zXURePLHPs4FP2Mn9uI15FaWe0jxiTEC+hO48EPLXjspfNz+1bls6KyuJ+7vYT6s/NwAADPJJREFUuMDVm15GRLo1rZu/rKsUjwB0yfHl620zZqjo0yRLnpziUczLaATSWVvZWlt9Nfp7/JOyvPz7acCMRQ/CruYv4/rVmP6ZsjlKjxB4/vBJOmtr++xZRe3+w366efKMSODi6Va6QU0Xz3J5ShRxyOmESJWSkffaseDIuw8Lli9VqGI5e6eP37l3cW+tbW2Q5frRU//ef1S5Y8u0adOKYvlJAiRAAiRgmQQSEhKOb11VqYGPXUbVn+uRj+9fP3ui2/h5SZK5deEU4u0yZT59YEu1Fl3eJAqX+HcmaXG/di4oyT4qR8pdvkTefbCi18h8ZUo0HTdQIV9sPjT4bcybX+t+DUMIOrNz6jyP1o2/WTzNOr0tbuGSMqNO+zfRrxDeOGa6zy8j6/bvhvCbl9GO+XIjgAv6BlpEyJT7Fy77Nu4q0kOX3A+9MnSvYqEK17Fl/gfmrcAKEcJFq3j03/yHbQa7hkN7F/E6HRMVfXTJ2nYzxjq7l1Uk/e+SSoZX78EFKyW7kXjeef6UKl18EMaS0ySPJsP2rXkQenXd0MmISZsuXeUOLf4rhv8lARIgARLQk4CXl5eeOWWQ7ca5k1gA6jD8V/W2nNi+GpEV67dSf/Q+Pj4oYH1RNy/YZnYtn7VkbA+RRogY9fRmECNr3xdoFL/OgyAseq70tbZRGCqsrD/IFwgLaBeYOkYc9K/S1efMpt2bxn4Y7C0/zhJZBm1fXsTLHQrm7OYA5EUkxIcYsz8H/rh5/EyEY15ECe3S+8+58/4N82zbVNpZDXvM2sETnQrkxaNeq36Dstk3ZymylK5fo8XEH8o0qJmYXWHRUb6kkm+cCBHapcuCn2c/DOm/ya9Ciwarvx+3c+p8KT0EGbSLEFViNUp6xAAJkAAJkIAFEjiyaTl6Xb5GY5W+J7x/v2/NgnLVGmbNmVflEW4vntz3MjLCrUbj6i264BYLSSUr1arWvPO9a6HYgqSe3gxiZC1f/IdPgWnEq1MrKJXdMxRbyDaPm3FwvsIR6dpRhZWs8fC+WJHp9PvkkrWrwLcXC0lQoDgbpkzDmu6tGpWo6dVn7Tw7+0wwzyBxbPRrK+sPa0826W0fXlIsE17adxSy5utZE8o3q4d1pW5Lpv94eifiYR3Z9tNvMOp8v8kPjyJu3UXkqbWK9UhxWSXagd6+eSNunz/6GxY/hKWS02fKiNsmI/t6d24Nl51S9apDAxWvXun89n2Ih2zC57Gl69C8oXtWV/u2XXjwecTwIgESIAESsFgCcW9iDm9YWtOnu62dYgZRvm5cCPr3yYOqzTopR0rhPat+R7hyo7YFXd1a9BmDcO22PXMXKo7A04d38Gl+l3zly9E/1p78n2LHF3YJ/fHNENwinKOIc/kWDRB4fOUmPo+vWI9PLLsUr6kwFUY//RdeLAiE7Tly7/wlBOBrAveUZw8U28/ev3sXE6VYUcIFhxhsF4JwESLjbWysEB8oCv+Q4NoRxcJbvYHfYo3pVuCZPTMX4/bpnfvSdmuRMfaVYvcath2Nca1171wYwlLJcTEKZeOQKwc+xQWJc++colW4pE1Pg3YsdyqYD7IGXjKQX+IpP0mABEiABCyQwMPEjUV5C7uq9z08NBiRuQsVU390dPOKC0d3Y9koX5GSeNpm4ORJ/oEQOkXdFHuXbpxXTGfmd31Yi5Fbx474rYGHbAG3Um5fwS23RMHypaED+mcr45Aze7b8edBaKBV8rvth8sXdh3O4FBDbqu0yZxJWDTyaVqNN9R5fR0U8vXLoZM6ihRCT3j4jJALMM1AkWRJVBfxUnD3K4REWmHAgXlFvj7RW6d5ERTvmzQXRg/jlPUc4VygDx2GU0HvW3N9bdA/8c7NwdhF+M48uX38XV//wIsV6ZNZ8ioZJJRcop/gabZkwCy669k6Ojy7f2Ov7B2wt3fymIx5bn/DZfPwgUVq+Moov6+3g83BDRiDlrqgEk3TjSjkgLJkESIAE5EMg9rXiN/baGSPuXjnvlKdA1LOnzyIevXrxrH6n78Ua0Kx+LTzrt85g74Dj6eDJa21t03bwlM0LfkKuhp0HiI5gjoMTDMIQNPg8e3AbVpHEI3P6lKN8uR1yAdoFBgks/WCfs8AtjBnCpoIYGE7qfP9NxiwOO36Zi1vIC7iYZCuQ9/6FK7jFGtD5bfuwNIMw1pW6LpqKQGEvd7z2CCUU8ijn7FEWMZAa2J009uSWgJmL4T2DhSpEwhOlWrd2WOup0asDnHPht4tNT3Acho0EC1LBa7e1nzkeW4Sy5s8DLYKM+IdcDYb0dMiVHQHlkr/fuPivkVM3jFLUjqtOv641+3TK4VIQ4fLN698PvYwuJD5Jk7u4C9x0YJ4Rt/wkARIgARKwQALF3KvUaNUN1pSTO9Yod//mhaBZAdeCdq8PvxiichovtkZjnxHOevFs0Fo5C8LpM2SCN8ypgL+in0faOyr2xprTlVYsmujYpVbt254NCq7Wr0u1fiko5a4eCrx7Lqz+wO5wRlFuGHxpn9590HneFET2cyjZ8IdecKGFrIFDk1jKQTy8ZH6u0hK6AQ62sMTY2KUX25HwCBut4V8CXxnhwAsBBP2hXL4wzCjHxLyMgoyVCheFSAViiWr7lN9zFC7o1bElvG2kjCol4zY+7i10mEp3pPQioF67SgKD3F5Y5L973jK45fv7+xukQBZCAiRAAjIk4OzsLFo1da/Cc9G0rtiYVzC6pE2bLkOmzPBngA3m3du4giUUawUQIri1toUyyYzIl//+A0tMjnyFNHXwSvDhKV1rdxkzp1HXQZrSyDD++bWg6QPao2FaZqvkWV+gXVKhn661vfFPvaJGw7+TIqE8hPCSNhOJR1AbCIgXG2XI8sFyIx5BdsCio1yCFBYBkVc5UrL9SJGSdkEMJIuyapHSqKgilVspmUpAvXaVBLwlARIgARKwBALQJrmdP/q4ZM6qMO2LC0YUZTuKYw6F04KWC/uPFp6MsE1vpyWNiT5KnnyRTyedCuWPfqpwH1G5xCF1r569UInnLQmQAAmQAAlYIAGHbB93kJhT9+W780g75VzFXC4fPIEFF5VkGR0dYO24cSw1rEQqVZvE7c3gcybRTjaSBEiABEiABLQQMFX5InYa3zxxWr1vpepXP7lqIzxO1B8xhgRIgARIgARIwAwImKp8cW/ZMHuhAjb/naKrPBI1e3XEI/jzKkcyTAIkQAIkQAIkYDYETNX3xT57tsmhe5MchmLVPDU9SjK9RUXGJqgut1lU99lZEiABEiAB8yBgqtYX86DPXpAACZAACaQEAdcKinPbeJkxAcoXMx5cdo0ESIAESIAEzJMA5Yt5jit7RQIkQAIkQAJmTIDyxYwHV2PXvL2TOBVQY2o+IAESIAESIAGZEaB8kdmAsDkkQAIkQAIkQAKfI0D58jlCfE4CJEACJGBqBOISj84onPjiZVNrO9v7kYCWtQLKl4+YGCIBEiABEiABEjAJApQvJjFMbCQJkAAJkAAJkMBHApQvH1kwRAIkQAIkQAIkYBIEKF9MYpjYSBIgARIgARIggY8EKF8+smCIBEiABEiABEjAJAhQvpjEMLGRJEACJEACJGApBEro8M4HyhdL+TawnyRAAiRgaQQKl+MRnWY75pQvZju07BgJkAAJWCaBv19bZr8tq9fJky8FPctZFh72lgRIgARIgARIQH4EkiFfHsV/ELQUMfIbR7aIBEiABEiABCyIQDLkiwVRYVdJgARIgARIgARkTIDyRcaDw6aRAAmQAAmQgOURgPeSl5cX+i0+kwRA+ZIkFkaSAAmQAAmQAAnIl4C17k17+O5LnbnvhYTqXp1Kyi/Jq1KUbG/1cCrSIwu6r0XPyhYOG0YCJEACySXgYtA3ToeHBiW3AXJLX7icwqRhHlcy5IvU4eMLVklhS1AVUmdNOiCEDsfLpAeRjScBEkgWgQOrfA8oZQi/YPL6Q6k38g0WNoRq/OxgJU++FPR0w/zHKVC+3xrNLeOoaWbDJyRAAmZFIOJ1GhxYh/nvs1OgWXVbNp1JHexpExISktXloKAP6lUKSNkDAwOlMALqCZSfmm7Y3auSSuNjE+JVYpK8LV2pYpLxn40sXdnjs2nUE0TGv1GPRIyTlV236g2TfMRIEiABEjAPAvD9PBv8GVuLe6WUXUn5bAN0QX0m5DO9SLKQs8GfTMdJplGJPPM5XCrpdbz1+Bxk90qfnIzs4flhULLaKWrQ4uqQbPmiY4t1SWZcfaMFii6NZxoSIAESIAESIAFjETCmfDFWn1kvCZAACZAACZCASRPgxmmTHj42ngRIgARIgAQskQDliyWOOvtMAiRAAiRAAiZNgPLFpIePjScBEiABEiABSyRA+WKJo84+kwAJkAAJkIBJE6B8MenhY+NJgARIgARIwBIJUL5Y4qizzyRAAiRAAiRg0gT+DzfEemx+wMjwAAAAAElFTkSuQmCC\" alt=\"image.png\">\n", "\n", "Image from [How Function Calling Works](https://huggingface.co/docs/hugs/en/guides/function-calling)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By definition, Tool Calling refers to the capability of models to interact with external tools. As you may already know, LLMs are excellent at generating text in the manner of question-and-answer pairs, chat-like conversation messages, and others. When provided useful context, LLMs are good at responding to prompts that can be used as actionable command parameters that another system would handle. This could be a search engine, calculators, company APIs, spreadsheets, or SQL queries. These systems when interacted with are usually done in a single command line or if more complex a runnable scripting language such as Bash or Python. The painful part is how to sequence these commands and assign the correct parameter names and values. What if we would just prompt the LLM to do these for us? For example. \"Schedule an appointment for <PERSON> with <PERSON> Monday, 10:00am Eastern time\"\n", "\n", "For this notebook, we'll use a smaller Qwen2.5 model 1.5B. We will guide you on how to prompt a model to respond in JSON format so we can then parse the results and pass those arguments to a Python script. The intuition for using a small model is that we do not want the model to use its pretraining data for the responses when calculating the vector sum. Smaller models have less stored knowledge and it would be possible that our prompt is not on the training data.\n", "\n", "Our intention is to provide a simple framework for integrating tool calling into your fine-tuning workflow for unsloth. Let us know how we can help you further."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 448, "referenced_widgets": ["10c7f59dc0864aa59b754f0234514f1e", "063541a0053545bea8f2e876fd2acb68", "92dc9145ab524f498bfc0c35f9fdb174", "11ca17ec35074f318bc144a9227478ac", "ea1e5a33362a43db8766cb0c08b578dd", "09d55268e88d4dc288a001427f61a4fd", "806dac7b48c0492396d0e967c65d8c71", "7eb5ea11b3c84889995392a4a34e273b", "c11547368c9a4e81bf787800d355d7f4", "5ebe13f6cae44361aed89ea22530cc87", "6fd5a120e3ed475ebb764bd55a45d8ec", "2dcc9afb45354f89b5930e740d889a7d", "e3659031c23d4bcb8136899c476e1a6c", "e1a8bf02391b48efa942016ec7bebc6d", "8f5bd8b322054dddb9d394e452544762", "f2ae3349b890487abee3a557b186294e", "6091d60224314261a301664d0362822a", "5701898928264a458c89f2c8a080e83a", "10a81cd2eab6441a872147db79f7ef1d", "3671f2e22aac43edadd1240a1e1deeea", "e3523f3c07f64fc88dacd72ac2ca1d3b", "ed299eeae37b44afa7f2c605a93199e4", "ab92d2ba313e4e1097f6bc302fb98409", "926ae2d61c204b0c8b9b6e3a4a90926b", "cda22ce77643405596be676b2eb35c80", "c9195c4996084510ac2baeffc3958673", "e718ccda074f49e1a488d71fd5c8f71c", "0e74b5680f5e4e5787bdb5e88984b74e", "2a01d4ad5c8441baa8357117a9bada43", "e72ddbfb5d9b4b86830a77498594da7c", "fad3f711763a45efa87750a5a978395f", "bedc55a741994190afdde78a0b6952ce", "790165ff4f0046448b17bd3505cbc416", "61fcb0f1cdd84a15a996cf6faaa0dc11", "c0be1d43b3f446b98c32951e3d2bebe6", "8a5cdb013ac046199d532fbaf286b577", "33d943034caf4ad98387b9116ceec2b5", "7223c1ddd12240eda1aa0d94120c7cbb", "b56b2851286c47e09cd032bad6f697cf", "cd5e8b1e84c74e0e959bffb5c75abf50", "1de87285321f4f01bc8bf86e3b0f86a5", "cc126f714dd0422fadec2262b9818560", "66d521d8e65e4f5285f89daaf7c8c38b", "103932911c4d409b8d801f9343affab9", "b4aa6dc09c4147bab29f7ace05579fe4", "768be9bccefb4359bc21c0731e534399", "fefd0ef6800c4a8aa57e22d3681fa324", "754e6537ebc3499499a68fbb5b25df53", "2483117f317a44d0b4eecb4a4e4f62aa", "fa6a56bbe82f412d8eff4968cddff88e", "cfb8e59c6bdd4e84b24507aa86db8eed", "49eb1b29c8064d3baf42e78cd9305b73", "f73f20e93f8a4763bd105d3dc8f61daf", "40ef2bc30330404b89c337510d560b5b", "a8f68b32207f43898a735122fb1e1026", "bd19725aa20d42d399f029caad53aa01", "9841a7ca0ec74e61b6c9654e2774f955", "657fa8603e7e4e23b30ea3fbe3b66bcd", "14cce34c29324f2abb209aa31251d414", "837b744b041b48949cf2fb0e53ccf931", "32be1aba98784b208667179dd5862de2", "e8ab843c82c34379b3f7c0edf8fa98ba", "e640358764354406b64ea9d8cd62b97c", "c895eb1145aa41e2b0dc4e7862c8e1ad", "3c1922834e5b420bbe3d8f7d2429b492", "0accca7a631d42b8a95b7dcad70d14b8", "ee6aa8e7f0ce4a31981d8e7f751a8c03", "34852aab3a4441b88dd65be909a7673b", "be0a1fb6e452464fbca63cba9a005b2b", "a9222c840014441d9199ddf2b7650d08", "e6ebb4fdfb2c43a5a3e7df423112cd46", "8a55ef8aed5144dbb02dfc99e5bd43ba", "fc9ad241d66b4cb380183a04fc2e6c2f", "4b481b20fe98408e97377eb6efea26f4", "f13f684bf6224980830986ed0a81e85a", "3321d3cd97f942b38125b98226a72899", "e9b5c45236e94e3480dd6dbbb3d6fcf2", "c109cfd6be8c428993f7089a6c2a94df", "b1f0cbd2181141d1a33948740a18a917", "c2a1021f9a8e473ba170c943a003dc84", "b8d3126b7d9d4a9a80c40789d9f68446", "3b9cbaf2c1044450b41dab928d7c27da", "79b9bffeb1fb44abae641493395ddfd1", "0dd25d69dee74fd58dd979cca5d3e587", "a3f10cdeab64466c86de3470279b5fbd", "d9288dbb67ca47d29fe3d188e11ca3d0", "a5b61a1d115d4a9db489042bedbec8f9", "90dd6bb65f444300b184c720a6e1b339", "89a8ed76a8df4e87a1659b6adfb0d458", "a442f35c4641456ba3b3b3c4fbee8995", "a1604bdde1b34bbfb6ff7c12cb6c6e58", "5d880920945845578a03b0216241ecb9", "4d3b101d7b1a492bb1f2e0c1f94f7674", "e545856974b842b3ad9d76508a9e9fcd", "4a8f7ce47d0f4071831027affdb22bfa", "69bbbbee4ca24dfa98cb2fd7aaded0d4", "c02c11128bed4f3bb57f3d185df12ebf", "c2f5d7804bce46af81dad2129baf5a01", "eff94aaebf5e45479fabd5b11c156291"]}, "executionInfo": {"elapsed": 81172, "status": "ok", "timestamp": 1742164795340, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "2jfx5fOpXiSN", "outputId": "2b97d305-aee4-49bd-dbc4-372932e820d4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.3.10: Fast Qwen2 patching. Transformers: 4.47.1.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.5.1+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.1.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post1. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "10c7f59dc0864aa59b754f0234514f1e", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/765 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2dcc9afb45354f89b5930e740d889a7d", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/3.09G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ab92d2ba313e4e1097f6bc302fb98409", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/265 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "61fcb0f1cdd84a15a996cf6faaa0dc11", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/7.51k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b4aa6dc09c4147bab29f7ace05579fe4", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json:   0%|          | 0.00/2.78M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bd19725aa20d42d399f029caad53aa01", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt:   0%|          | 0.00/1.67M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ee6aa8e7f0ce4a31981d8e7f751a8c03", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/632 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c109cfd6be8c428993f7089a6c2a94df", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/613 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "89a8ed76a8df4e87a1659b6adfb0d458", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/7.03M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastQwen2Model\n", "import torch\n", "\n", "max_seq_length = 2048  # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None  # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True  # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Meta-Llama-3.1-8B-bnb-4bit\",  # Llama-3.1 2x faster\n", "    \"unsloth/Meta-Llama-3.1-70B-bnb-4bit\",\n", "    \"unsloth/Mistral-Small-Instruct-2409\",  # Mistral 22b 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/Phi-3.5-mini-instruct\",  # Phi-3.5 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/gemma-2-27b-bnb-4bit\",  # <PERSON> 2x faster!\n", "\n", "    \"unsloth/Llama-3.2-1B-bnb-4bit\",  # NEW! Llama 3.2 models\n", "    \"unsloth/Llama-3.2-1B-Instruct-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-3B-Instruct-bnb-4bit\",\n", "]  # More models at https://huggingface.co/unsloth\n", "\n", "qwen_models = [\n", "    \"unsloth/Qwen2.5-Coder-32B-Instruct\",  # Qwen 2.5 Coder 2x faster\n", "    \"unsloth/Qwen2.5-Coder-7B\",\n", "    \"unsloth/Qwen2.5-14B-Instruct\",  # 14B fits in a 16GB card\n", "    \"unsloth/Qwen2.5-7B\",\n", "    \"unsloth/Qwen2.5-72B-Instruct\",  # 72B fits in a 48GB card\n", "]  # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastQwen2Model.from_pretrained(\n", "    model_name=\"unsloth/Qwen2.5-Coder-1.5B-Instruct\",\n", "    max_seq_length=None,\n", "    dtype=None,\n", "    load_in_4bit=False,\n", "    fix_tokenizer=False\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 816, "status": "ok", "timestamp": 1742164796157, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "tocysGVwoxbu"}, "outputs": [], "source": ["# save a copy because apply_chat_template() has in-place modifications\n", "import copy\n", "\n", "tokenizer_orig = copy.deepcopy(tokenizer)"]}, {"cell_type": "markdown", "metadata": {"id": "m64sv66VHfam"}, "source": ["### Tool Definitions\n", "This is a list of all the functions that we would provide to the model. The standard format is from OpenAI's [Function Calling Definition](https://platform.openai.com/docs/guides/function-calling?api-mode=chat#defining-functions)\n", "It is highly possible that the model trained for tool calling was in the OpenAI standard format.\n", "\n", "Below is an example of two function definitions. The function definitions of `get_vector_sum` and `get_dot_product` will then be added to the prompt as a context for our prompt:\n", "\n", "> Find the sum of a = [1, -1, 2] and b = [3, 0, -4].\n", "\n", "We can just provide the correct one: `get_vector_sum` but to experiment if the model can identify the correct function to call, we will provide both."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"executionInfo": {"elapsed": 11, "status": "ok", "timestamp": 1742164796171, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "v6MoZMRQa9jI"}, "outputs": [], "source": ["def get_tool_definition_list():\n", "    return [\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"get_vector_sum\",\n", "                \"description\": \"Get the sum of two vectors\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"a\": {\"type\": \"list\", \"description\": \"First vector\"},\n", "                        \"b\": {\"type\": \"list\", \"description\": \"Second vector\"}\n", "                    },\n", "                    \"required\": [\"a\", \"b\"]\n", "                }\n", "            }\n", "        },\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"get_dot_product\",\n", "                \"description\": \"Get the dot product of two vectors\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"a\": {\"type\": \"list\", \"description\": \"First vector\"},\n", "                        \"b\": {\"type\": \"list\", \"description\": \"Second vector\"}\n", "                    },\n", "                    \"required\": [\"a\", \"b\"]\n", "                }\n", "            }\n", "        },\n", "\n", "    ]"]}, {"cell_type": "markdown", "metadata": {"id": "jE57KGSLNfnc"}, "source": ["Below is the user prompt declaration"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"executionInfo": {"elapsed": 47, "status": "ok", "timestamp": 1742164796219, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "23p0W2pPdaZY"}, "outputs": [], "source": ["user_query = {\n", "    \"role\": \"user\",\n", "    \"content\": \"Find the sum of a = [1, -1, 2] and b = [3, 0, -4].\"\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "YEjwPmJyNfnd"}, "source": ["### Python Code\n", "Below is the actual code for the function, you may notice that it has Python docstrings. This is because `apply_chat_template()` can accept and translate functions into OpenAI function definitions that are PEP 257 compliant."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1742164796220, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "GBq-wmhRNfnd"}, "outputs": [], "source": ["def get_vector_sum(a: list[float], b: list[float]) -> list[float]:\n", "    \"\"\"\n", "    Performs element-wise addition of two numerical vectors.\n", "\n", "    Both vectors must be of the same length and contain numerical values.\n", "\n", "    Args:\n", "        a: First vector containing numerical values\n", "        b: Second vector containing numerical values\n", "\n", "    Returns:\n", "        Resulting vector where each element is the sum of corresponding elements in a and b\n", "\n", "    Raises:\n", "        ValueError: If vectors have different lengths\n", "\n", "    Example:\n", "        >>> get_vector_sum([1, 2], [3, 4])\n", "        [4, 6]\n", "    \"\"\"\n", "    if len(a) != len(b):\n", "        raise ValueError(\"Vectors must be of the same length\")\n", "\n", "    return [x + y for x, y in zip(a, b)]"]}, {"cell_type": "markdown", "metadata": {"id": "EXH4AAlcNfnd"}, "source": ["Now let's prompt the model to provide us the arguments in JSON format. You may notice that we passed the actual function `get_vector_sum()` to the `tool` parameter and not the `get_tool_definition_list()`, You may try to change it from `tools=[get_vector_sum],` to `tools=[get_tool_definition_list()` to see if it works with the function definitions."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2025-03-15T00:12:54.604054Z", "start_time": "2025-03-15T00:12:53.440302Z"}, "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 599, "status": "ok", "timestamp": 1742164796826, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "ZFRTAly3LfFf", "outputId": "c81b7262-5a6d-4e15-8ac3-508c3e0ec3b0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.\n", "\n", "# Tools\n", "\n", "You may call one or more functions to assist with the user query.\n", "\n", "You are provided with function signatures within <tools></tools> XML tags:\n", "<tools>\n", "{\"type\": \"function\", \"function\": {\"name\": \"get_vector_sum\", \"description\": \"Performs element-wise addition of two numerical vectors.\\n\\nBoth vectors must be of the same length and contain numerical values.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"a\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"description\": \"First vector containing numerical values\"}, \"b\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"description\": \"Second vector containing numerical values\"}}, \"required\": [\"a\", \"b\"]}, \"return\": {\"type\": \"array\", \"items\": {\"type\": \"number\"}, \"description\": \"Resulting vector where each element is the sum of corresponding elements in a and b\"}}}\n", "</tools>\n", "\n", "For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n", "<tool_call>\n", "{\"name\": <function-name>, \"arguments\": <args-json-object>}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "Find the sum of a = [1, -1, 2] and b = [3, 0, -4].<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["messages = []\n", "\n", "messages.append(user_query)\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=True,\n", "    add_generation_prompt=True,\n", "    add_special_tokens=False,\n", "    padding=True,\n", "    tools=[get_vector_sum],\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "print(tokenizer.decode(input_ids[0]))"]}, {"cell_type": "markdown", "metadata": {"id": "MwY97s7jNfnd"}, "source": ["Below is where we call the unsloth function `generate_with_grammar()`. This function uses Grammar-Constrained Decoding. Meaning it will only respond in JSON. It uses a library fork of [transformers-CFG](https://github.com/Saibo-creator/transformers-CFG) by [<PERSON><PERSON>-creator](https://github.com/<PERSON><PERSON>-creator) the output is very similar to the llama-cpp-python output. We decided to use this library so that our code would be portable to `llama-cpp-python` later during production.\n", "\n", "If successful, the model should output a single valid JSON response with the following result:\n", "```\n", "[\n", "    {\n", "        \"name\": \"get_vector_sum\",\n", "        \"arguments\": {\n", "            \"a\": [1, -1, 2],\n", "            \"b\": [3, 0, -4]\n", "        }\n", "    }\n", "]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#@title Function for Generation Constraint { display-mode: \"form\" }\n", "\n", "from functools import partial\n", "from transformers_cfg.grammar_utils import IncrementalGrammarConstraint\n", "from transformers_cfg.generation.logits_process import GrammarConstrainedLogitsProcessor\n", "\n", "JSON_ARR_GBNF = r\"\"\"\n", "# This is the same as json.gbnf but we restrict whitespaces at the end of the root array\n", "# Useful for generating JSON arrays\n", "root   ::= arr\n", "value  ::= object | array | string | number | (\"true\" | \"false\" | \"null\") ws\n", "arr  ::=\n", "  \"[\\n\" ws (\n", "            value\n", "    (\",\\n\" ws value)*\n", "  )? \"]\"\n", "object ::=\n", "  \"{\" ws (\n", "            string \":\" ws value\n", "    (\",\" ws string \":\" ws value)*\n", "  )? \"}\" ws\n", "array  ::=\n", "  \"[\" ws (\n", "            value\n", "    (\",\" ws value)*\n", "  )? \"]\" ws\n", "string ::=\n", "  \"\\\"\" (\n", "    [^\"\\\\\\x7F\\x00-\\x1F] |\n", "    \"\\\\\" ([\"\\\\/bfnrt] | \"u\" [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F]) # escapes\n", "  )* \"\\\"\" ws\n", "number ::= (\"-\"? ([0-9] | [1-9] [0-9]*)) (\".\" [0-9]+)? ([eE] [-+]? [0-9]+)? ws\n", "# Optional space: by convention, applied in this grammar after literal chars when allowed\n", "ws ::= ([ \\t\\n] ws)?\n", "\"\"\"\n", "\n", "def generate_with_grammar(model, input_ids, **kwargs):\n", "    tokenizer = AutoTokenizer.from_pretrained(model.config.name_or_path)\n", "    grammar = IncrementalGrammarConstraint(JSON_ARR_GBNF, start_rule_name=\"root\", tokenizer=tokenizer)\n", "    grammar_processor = GrammarConstrainedLogitsProcessor(grammar)\n", "\n", "    partial_generate = partial(\n", "        model.generate,\n", "        do_sample=False,\n", "        repetition_penalty=1.1,\n", "        num_return_sequences=1,\n", "        logits_processor=[grammar_processor],  # Ensure grammar_processor is accessible\n", "        temperature=None,\n", "        top_p=None,\n", "        top_k=None,\n", "        sliding_window=None,\n", "    )\n", "\n", "    # Execute generation with merged parameters\n", "    return partial_generate(\n", "        input_ids=input_ids,\n", "        **kwargs\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 408, "referenced_widgets": ["26062536695547da98bb96e8bae5fa30", "587b3d5a5782431d8ba0257f997390c0", "c2e9d271af1449329bb7a3aeed9dec95", "9f95b66bc04b45ac8c291e90c4101921", "c1275a9bc9d949c8944870b1d0a58f0c", "498f42b240534add84e727a2ac2f8d0c", "cc5e0350a895469b8907801d2ddfc116", "06de6a033cd9416da2d32e343ffd6f82", "88be632353ac4aa9b4c7580f5c8f4d05", "7ce8c1e1642d4ff2b4e9fdd75487a7c1", "6f45335cfbda48b4bc7de3824c8adda2", "a4451c36012745b9a12218af49e93956", "73aa3cc053e44d1aa1c2c3930e07abce", "cc2b6ccbb4044f499b3616dbb3704275", "170c8e302fa5457a91c43e02b964ff4c", "557d12eabf05431980b26af12ca9e3b3", "3c73c54b7701493d92ff6ced3735dce3", "c18476863ce24354adbb343720fa1085", "44aed22fadf340babe47e18cc3609603", "c2f9d9cbd9b648fdaa533005e0231538", "6867fef6ddfa4901b267e92a9a2cdae9", "1119aa8b93da44fe84f9133c6d564ffd", "4f4ec8343cca49529ba82e28a94d4ba3", "043f65a4ea764e6cac6c9635c97f97eb", "69ac7164e60f4a3aa46a37a722e03451", "3089a3c50708464783a3447c0ef650f1", "03d65e13dc174e92bbbcbc5088909f9d", "d712e72eebf14a23a5c321795d0a4d19", "906d688903c7464ca09c6c61644a27a0", "2c8df8d547574b2f985c7794b4642b76", "e92bfb506b4744c08f40d33962e7c856", "2c43b1321e4d4fafb433c496e6c19b90", "05f2869680dd4e6aa1e8068406bff183", "453990d396d348769cee958b5826a932", "a962f4949e1f42d8aa1791674e16da78", "d052810c41e843bb8e63ed650fc72dbb", "341c4de23f1848c79d0ce62190f308e7", "14c690cb2b5f410dbd8bf82f772c4802", "ca9d082a491b499c93fc044ea69151e5", "f1edfa6acde44acca81dc5cac5543ab8", "025ae279fcb74246b9bfb3bfd923904c", "0431e31bf3ea4e0d80eaef4db8311bd6", "6d3a4e95a8954714bb681834d05ab309", "e52f671db0794a548ae77ad06b5134b6", "e5c600ca83f345b7bb6550bd6f25bc2c", "051169313068408a9aee6ae705be5d72", "2ec34140efa84ca29135afa6b4aa475f", "6ae2bcda9b1540bfae0b7c182e60b2c7", "0ad2ded850b542f5802ade7571301c3b", "16ef86cc39a44c839621cda63cccfc41", "92b35a13a55b4838bf1c728b8a74bab1", "1ffa1f53cbd74a76a5933f749881e94c", "624d5c08b8fd4e38bca6c55878bd26eb", "b3ba88110d5c4b1da54fa61657d3dae4", "3ca40da457a94b0c9bf4f0d3e7045bad", "6b4cec98b8e64dfaa187b21f34d0a45e", "096795df31e64d2688c4be21e1326498", "d68c4628980a462c9b5dad58c54d9fbf", "2c0c7406572e4218a9541d152f0229c9", "62d6a0005faf4359a3b7e980f6a12676", "7ff72b61dadb432f98826d83aa972e70", "bb0039fe21594c27a09e91d58f0db14a", "52a12ec9d90444f182a329c34feb9e29", "f50500601bc94cdeb200a0d3b3c80a95", "2b6ea547721a4099815aac0c315dc97c", "de7962e4c6a04f839a98fd8317ca26d7"]}, "executionInfo": {"elapsed": 21358, "status": "ok", "timestamp": 1742164818142, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "5I6ljHnWNfnd", "outputId": "e33ccb3e-a3fb-4fab-dea8-a4a00f867dd0"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "26062536695547da98bb96e8bae5fa30", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/7.51k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a4451c36012745b9a12218af49e93956", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json:   0%|          | 0.00/2.78M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4f4ec8343cca49529ba82e28a94d4ba3", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt:   0%|          | 0.00/1.67M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "453990d396d348769cee958b5826a932", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/7.03M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5c600ca83f345b7bb6550bd6f25bc2c", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/632 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b4cec98b8e64dfaa187b21f34d0a45e", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/613 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"name\": \"get_vector_sum\",\n", "        \"arguments\": {\n", "            \"a\": [1, -1, 2],\n", "            \"b\": [3, 0, -4]\n", "        }\n", "    }\n", "]\n"]}], "source": ["output = generate_with_grammar(\n", "    model=model,\n", "    input_ids=input_ids\n", ")\n", "\n", "generated_tokens = output[:, input_ids.shape[1]:]\n", "\n", "decoded_output = tokenizer.batch_decode(generated_tokens, skip_special_tokens=True)\n", "\n", "for i, message in enumerate(decoded_output):\n", "    print(f\"{message}\")"]}, {"cell_type": "markdown", "metadata": {"id": "EESZm0wBNqA2"}, "source": ["From here we can now parse the arguments provided to us by the model"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 11, "status": "ok", "timestamp": 1742164818143, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "0xCxL_SgM4J0", "outputId": "ce8e68ba-a9bd-4e7b-aa42-02e27dc7fcf4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["args a: [1, -1, 2], b: [3, 0, -4]\n"]}], "source": ["import json\n", "\n", "content = json.loads(decoded_output[0])\n", "arguments = content[0]['arguments']\n", "vector_a = arguments['a']\n", "vector_b = arguments['b']\n", "print(f\"args a: {vector_a}, b: {vector_b}\")"]}, {"cell_type": "markdown", "metadata": {"id": "hBRMiKiuN-D4"}, "source": ["Here we actually call `get_vector_sum()` and capture the result"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4, "status": "ok", "timestamp": 1742164818144, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "XvA-YnvRN7vJ", "outputId": "d29deddd-725d-4802-b1fa-15b266dcadf5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["result: [4, -1, -2]\n"]}], "source": ["result = get_vector_sum(vector_a, vector_b)\n", "print(f\"result: {result}\")"]}, {"cell_type": "markdown", "metadata": {"id": "82zbHycZNfne"}, "source": ["Below is the final prompt to the model in the form of a chat message. To ensure that the model responds with the actual answer  prompted the model's answer with the following:\n", "\n", "> You are a super helpful AI assistant. You are asked to answer a question based on the following context information.\n", ">\n", "> Question:\n", ">\n", "> Answer:\n", "\n", "Then we set `continue_final_message=True` for the tokenizer"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 781, "status": "ok", "timestamp": 1742164818923, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "j6p7fmcHSCll", "outputId": "f57f0558-5d29-412c-c243-49a5bc72aeb2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "Find the sum of a = [1, -1, 2] and b = [3, 0, -4].<|im_end|>\n", "<|im_start|>assistant\n", "<tool_call>\n", "{\"name\": \"get_vector_sum\", \"arguments\": {\"a\": [1, -1, 2], \"b\": [3, 0, -4]}}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "<tool_response>\n", "[4, -1, -2]\n", "</tool_response><|im_end|>\n", "<|im_start|>assistant\n", "Answer:\n", "\n"]}], "source": ["import random\n", "import string\n", "\n", "\n", "def generate_alphanumeric():\n", "    characters = string.ascii_letters + string.digits\n", "    result = ''.join(random.choice(characters) for _ in range(9))\n", "    return result\n", "\n", "\n", "messages = []\n", "\n", "original_prompt = user_query['content']\n", "\n", "prompt_with_context = f\"\"\"You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "{original_prompt}\"\"\"\n", "\n", "messages.append({\n", "    \"role\": \"user\",\n", "    \"content\": prompt_with_context\n", "})\n", "\n", "tool_call_id = generate_alphanumeric()\n", "tool_calls = [{\n", "    \"id\": tool_call_id,\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"get_vector_sum\",\n", "        \"arguments\": arguments\n", "    }\n", "}]\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"tool_calls\": tool_calls\n", "})\n", "messages.append({\n", "    \"role\": \"tool\",\n", "    \"name\": \"get_vector_sum\",\n", "    \"content\": result\n", "})\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"content\": \"Answer:\\n\"\n", "})\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "tool_prompt = tokenizer.apply_chat_template(\n", "    messages,\n", "    continue_final_message=True,\n", "    add_special_tokens=True,\n", "    return_tensors=\"pt\",\n", "    return_dict=True,\n", "    tools=None,\n", ")\n", "tool_prompt = tool_prompt.to(model.device)\n", "\n", "print(tokenizer.decode(tool_prompt['input_ids'][0]))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2012, "status": "ok", "timestamp": 1742164820936, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "tYKun5XgQURH", "outputId": "bec258e3-139d-4337-d583-82f8cd8e9fce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The sum of a = [1, -1, 2] and b = [3, 0, -4] is [4, -1, -2].\n"]}], "source": ["out = model.generate(**tool_prompt, max_new_tokens=128)\n", "generated_text = out[0, tool_prompt['input_ids'].shape[1]:]\n", "\n", "print(tokenizer.decode(generated_text, skip_special_tokens=True))"]}, {"cell_type": "markdown", "metadata": {"id": "Xyeipvv2xSxB"}, "source": ["For comparison, if we would prompt the model without tool calling:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 492, "status": "ok", "timestamp": 1742164821430, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "kXdIa3jueI-i", "outputId": "4d0b3486-5ebe-46e4-dcf2-305e1c300619"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "Find the sum of a = [1, -1, 2] and b = [3, 0, -4].<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["tokenizer = copy.deepcopy(tokenizer_orig)\n", "input_ids = tokenizer.apply_chat_template(\n", "    [user_query],\n", "    tokenize=True,\n", "    add_generation_prompt=True,\n", "    add_special_tokens=False,\n", "    padding=True,\n", "    tools=None,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "print(tokenizer.decode(input_ids[0]))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 10351, "status": "ok", "timestamp": 1742164831782, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "LJfdZ1G194_t", "outputId": "69b421ed-7c25-4e22-ecc6-a2e563e8f9c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To find the sum of two vectors \\( \\mathbf{a} = [1, -1, 2] \\) and \\( \\mathbf{b} = [3, 0, -4] \\), we need to add corresponding components of the vectors.\n", "\n", "The sum of two vectors \\( \\mathbf{a} = [a_1, a_2, a_3] \\) and \\( \\mathbf{b} = [b_1, b_2, b_3] \\) is given by:\n", "\\[ \\mathbf{a} + \\mathbf{b} = [a_1 + b_1, a_2 + b_2, a_3 + b_3] \\]\n", "\n", "Let's compute this step-by-step using Python code.\n", "```python\n", "# Define the vectors\n", "a = [1, -1, 2]\n", "b = [3, 0, -4]\n", "\n", "# Compute the sum of the vectors\n", "result = [a_i + b_i for a_i, b_i in zip(a, b)]\n", "print(result)\n", "```\n", "```output\n", "[4, -1, -2]\n", "```\n", "The sum of the vectors \\( \\mathbf{a} = [1, -1, 2] \\) and \\( \\mathbf{b} = [3, 0, -4] \\) is \\( \\boxed{[4, -1, -2]} \\).\n"]}], "source": ["output = model.generate(\n", "    input_ids=input_ids,\n", "    max_new_tokens=1024\n", ")\n", "\n", "generated_tokens = output[:, input_ids.shape[1]:]\n", "decoded_output = tokenizer.batch_decode(generated_tokens, skip_special_tokens=True)\n", "\n", "for i, message in enumerate(decoded_output):\n", "    print(f\"{message}\")"]}, {"cell_type": "markdown", "metadata": {"id": "6NgmkhLSoaXv"}, "source": ["### Example currency calculation with API call\n", "For the example below, we are going to ask the model to compute a total list of items and convert it to Euro using a public API.\n", "\n", "Pydantic is used below because it will help us with type safety and validation, and also helps us with clear function schemas.\n", "\n", "For the prompt, we will ask the model \"How much is the total cost of all inventory items in Euros?\"\n", "\n", "Based on the prompt we need to define three tools\n", "1. Get a list of all items in the inventory\n", "2. The conversion rate of the Euro currency\n", "2. Compute the total inventory cost in Euros\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"executionInfo": {"elapsed": 1, "status": "ok", "timestamp": 1742164831785, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "K_2-9JLgva3X"}, "outputs": [], "source": ["user_query = {\n", "    \"role\": \"user\",\n", "    \"content\": \"How much is the total cost of all inventory items in Euros?\"\n", "}"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"executionInfo": {"elapsed": 13, "status": "ok", "timestamp": 1742165129359, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "2XACEzJdyWud"}, "outputs": [], "source": ["from typing import List, Optional, AnyStr\n", "from pydantic import BaseModel, Field\n", "import requests\n", "\n", "\n", "class Item(BaseModel):\n", "    id: int | None = Field(\n", "        default=None,\n", "        description=\"Unique identifier for the item (auto-generated by database)\"\n", "    )\n", "    item_code: str = Field(\n", "        ...,\n", "        min_length=3,\n", "        max_length=20,\n", "        description=\"Unique SKU or product code for the item\"\n", "    )\n", "    name: str = Field(\n", "        ...,\n", "        min_length=2,\n", "        max_length=50,\n", "        description=\"Human-readable name of the item\"\n", "    )\n", "    cost: float = Field(\n", "        ...,\n", "        gt=0,\n", "        description=\"Unit cost in local currency (must be positive)\"\n", "    )\n", "    quantity: int = Field(\n", "        ...,\n", "        ge=0,\n", "        description=\"Current inventory quantity (non-negative integer)\"\n", "    )\n", "\n", "\n", "def inventory_check(item_codes: Optional[List[str]], conversion_rate: float) -> float:\n", "    \"\"\"\n", "    Calculates the total value of inventory items in the target conversion rate.\n", "    When item_codes=None, calculates total value for all items.\n", "\n", "    Args:\n", "        item_codes: List of item codes to include. (None for all items)\n", "        conversion_rate: Exchange rate to convert costs to target currency\n", "    Returns:\n", "        Total value of matching items in target currency, rounded to 2 decimals\n", "    \"\"\"\n", "    all_items = get_all_items()\n", "\n", "    # Process all items if None is passed\n", "    if item_codes is None or len(item_codes) == 0:\n", "        items_to_process = all_items\n", "    else:\n", "        # Convert to set for faster lookups\n", "        target_codes = set(item_codes)\n", "        items_to_process = [item for item in all_items if item.item_code in target_codes]\n", "\n", "    # Calculate total value with conversion\n", "    total = sum(\n", "        item.cost * item.quantity * conversion_rate\n", "        for item in items_to_process\n", "    )\n", "\n", "    return round(total, 2)\n", "\n", "\n", "def get_all_items() -> List[Item]:\n", "    \"\"\"Fetches all the inventory items\"\"\"\n", "    return [\n", "        <PERSON><PERSON>(\n", "            item_code=\"ITEM-001\",\n", "            name=\"<PERSON>\",\n", "            cost=1.13,\n", "            quantity=4\n", "        ),\n", "        <PERSON><PERSON>(\n", "            item_code=\"ITEM-002\",\n", "            name=\"Bottled Water\",\n", "            cost=1.04,\n", "            quantity=20\n", "        ),\n", "        <PERSON><PERSON>(\n", "            item_code=\"ITEM-003\",\n", "            name=\"Instant Ramen\",\n", "            cost=10.13,\n", "            quantity=4\n", "        )\n", "    ]\n", "\n", "\n", "def get_usd_to_euro_conversion_rate() -> float:\n", "    \"\"\"Gets the conversion rate from USD to EURO\"\"\"\n", "    response = requests.get(\"https://api.frankfurter.app/latest?from=USD\")\n", "    response.raise_for_status()\n", "    rate = response.json()[\"rates\"][\"EUR\"]\n", "    return rate"]}, {"cell_type": "markdown", "metadata": {"id": "hhXc902j-ICe"}, "source": ["Below we validate the `tools` before passing it to the tokenizer"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1742165138114, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "IsEBB0zZ1HCs"}, "outputs": [], "source": ["from transformers.utils import chat_template_utils\n", "\n", "tools = [get_all_items, inventory_check, get_usd_to_euro_conversion_rate]\n", "\n", "orig_tools = copy.deepcopy(tools)  # save a copy for later\n", "\n", "for tool in tools:\n", "    _ = chat_template_utils.get_json_schema(tool)"]}, {"cell_type": "markdown", "metadata": {"id": "nsnNrPIM-TeO"}, "source": ["After ensuring the functions are valid we pass it to the `tools` param"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 18, "status": "ok", "timestamp": 1742165306048, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "YCbA9e-PvJkl", "outputId": "37e480d2-9542-47e1-d45f-5a49d0577bf3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.\n", "\n", "# Tools\n", "\n", "You may call one or more functions to assist with the user query.\n", "\n", "You are provided with function signatures within <tools></tools> XML tags:\n", "<tools>\n", "{\"type\": \"function\", \"function\": {\"name\": \"get_all_items\", \"description\": \"Fetches all the inventory items\", \"parameters\": {\"type\": \"object\", \"properties\": {}}, \"return\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"inventory_check\", \"description\": \"Calculates the total value of inventory items in the target conversion rate.\\nWhen item_codes=None, calculates total value for all items.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"item_codes\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"nullable\": true, \"description\": \"List of item codes to include (None for all items)\"}, \"conversion_rate\": {\"type\": \"number\", \"description\": \"Exchange rate to convert costs to target currency\"}}, \"required\": [\"item_codes\", \"conversion_rate\"]}, \"return\": {\"type\": \"number\", \"description\": \"Total value of matching items in target currency, rounded to 2 decimals\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"get_usd_to_euro_conversion_rate\", \"description\": \"Gets the conversion rate from USD to EURO\", \"parameters\": {\"type\": \"object\", \"properties\": {}}, \"return\": {\"type\": \"number\"}}}\n", "</tools>\n", "\n", "For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n", "<tool_call>\n", "{\"name\": <function-name>, \"arguments\": <args-json-object>}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "How much is the total cost of all inventory items in Euros?<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["messages = []\n", "\n", "messages.append(user_query)\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=True,\n", "    add_generation_prompt=True,\n", "    add_special_tokens=False,\n", "    padding=True,\n", "    tools=tools,  # pass the tools\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "print(tokenizer.decode(input_ids[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 11854, "status": "ok", "timestamp": 1742165320283, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "WhutWZ65xJuI", "outputId": "9e76c456-18e6-43b2-f34f-81e55c08f2a7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "\n", "    {\n", "        \"name\": \"inventory_check\",\n", "        \"arguments\": {\n", "            \"item_codes\": [],\n", "            \"conversion_rate\": 0.85\n", "        }\n", "    }\n", "\n", "]\n"]}], "source": ["output = generate_with_grammar(\n", "    model=model,\n", "    input_ids=input_ids\n", ")\n", "\n", "generated_tokens = output[:, input_ids.shape[1]:]\n", "\n", "decoded_output = tokenizer.batch_decode(generated_tokens, skip_special_tokens=True)\n", "\n", "for i, message in enumerate(decoded_output):\n", "    print(f\"{message}\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 46, "status": "ok", "timestamp": 1742165362660, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "87_9T7uJzUnF", "outputId": "dc1242af-b6c4-453b-d87f-21cf2c80dab7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["item_codes: [], conversion_rate: 0.85\n"]}], "source": ["import json\n", "\n", "content = json.loads(decoded_output[0])\n", "arguments = content[0]['arguments']\n", "item_codes = arguments['item_codes']\n", "conversion_rate = arguments['conversion_rate']\n", "print(f\"item_codes: {item_codes}, conversion_rate: {conversion_rate}\")"]}, {"cell_type": "markdown", "metadata": {"id": "iCrWHc567986"}, "source": ["Here we actually call `inventory_check()` and capture the result"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 16, "status": "ok", "timestamp": 1742165373667, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "pCxTSXsz7uUV", "outputId": "785c8d4d-831e-436f-d04d-e7010dbeace5"}, "outputs": [{"data": {"text/plain": ["55.96"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["result_total = inventory_check(item_codes, conversion_rate)\n", "result_total"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 686, "status": "ok", "timestamp": 1742165400392, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "t_NtXUxf8Hbi", "outputId": "785ab824-9a52-484b-8c9a-16c678d22f67"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "How much is the total cost of all inventory items in Euros?<|im_end|>\n", "<|im_start|>assistant\n", "<tool_call>\n", "{\"name\": \"inventory_check\", \"arguments\": {\"item_codes\": [], \"conversion_rate\": 0.85}}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "<tool_response>\n", "55.96\n", "</tool_response><|im_end|>\n", "<|im_start|>assistant\n", "Answer:\n", "\n"]}], "source": ["messages = []\n", "\n", "original_prompt = user_query['content']\n", "\n", "prompt_with_context = f\"\"\"You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "{original_prompt}\"\"\"\n", "\n", "messages.append({\n", "    \"role\": \"user\",\n", "    \"content\": prompt_with_context\n", "})\n", "\n", "tool_call_id = generate_alphanumeric()\n", "tool_calls = [{\n", "    \"id\": tool_call_id,\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"inventory_check\",\n", "        \"arguments\": arguments\n", "    }\n", "}]\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"tool_calls\": tool_calls\n", "})\n", "messages.append({\n", "    \"role\": \"tool\",\n", "    \"name\": \"inventory_check\",\n", "    \"content\": result_total\n", "})\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"content\": \"Answer:\\n\"\n", "})\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "tool_prompt = tokenizer.apply_chat_template(\n", "    messages,\n", "    continue_final_message=True,\n", "    add_special_tokens=True,\n", "    return_tensors=\"pt\",\n", "    return_dict=True,\n", "    tools=None,\n", ")\n", "tool_prompt = tool_prompt.to(model.device)\n", "\n", "print(tokenizer.decode(tool_prompt['input_ids'][0]))"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 809, "status": "ok", "timestamp": 1742165403470, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "FsqMTNst8iQh", "outputId": "e55b383c-3251-4d3a-b418-a66f92729b51"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The total cost of all inventory items in Euros is 55.96.\n"]}], "source": ["out = model.generate(**tool_prompt, max_new_tokens=128)\n", "generated_text = out[0, tool_prompt['input_ids'].shape[1]:]\n", "\n", "print(tokenizer.decode(generated_text, skip_special_tokens=True))"]}, {"cell_type": "markdown", "metadata": {"id": "wA7I2BvY-sxq"}, "source": ["Let's try if the model can use the correct tools for fetching item names. Note that we added a prompt \"Ensure to use fetch_item_by_name first for fetching the item code\""]}, {"cell_type": "code", "execution_count": 55, "metadata": {"executionInfo": {"elapsed": 41, "status": "ok", "timestamp": 1742165907603, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "tFSIik9M8ydF"}, "outputs": [], "source": ["user_query = {\n", "    \"role\": \"user\",\n", "    \"content\": f\"\"\"How much is the total inventory cost of item name: Bottled Water in Euros? Ensure to use fetch_item_by_name first for fetching the item code\"\"\"\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "J1i3vOjFIA7U"}, "source": ["Below we declare a new function `fetch_item_by_name` which fetches a single item based on the item name. Next we append the new function to our tool list"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"executionInfo": {"elapsed": 6, "status": "ok", "timestamp": 1742165932486, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "hiH8xlwcGuG1"}, "outputs": [], "source": ["def fetch_item_by_name(item_name: str) -> Optional[Item]:\n", "    \"\"\"\n", "    Fetch an item by name and returns the Item object.\n", "\n", "    Args:\n", "        item_name: The human-readable name of the item to fetch\n", "    Returns:\n", "        Optional[Item]: The Item with the given name, or None if not found\n", "    \"\"\"\n", "    all_items = get_all_items()\n", "    return next((item for item in all_items if item.name == item_name), None)\n", "\n", "\n", "# append to the tools list\n", "tools = copy.deepcopy(orig_tools)\n", "\n", "# place it at the top of the list\n", "tools.insert(0, fetch_item_by_name)"]}, {"cell_type": "markdown", "metadata": {"id": "w9PPKfsTIllY"}, "source": ["Let's make sure that we have a valid tools definition"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1742165933515, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "EJqNBoP3Iisp"}, "outputs": [], "source": ["from transformers.utils import chat_template_utils\n", "\n", "for tool in tools:\n", "    _ = chat_template_utils.get_json_schema(tool)"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 677, "status": "ok", "timestamp": 1742165935451, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "T3CLRZ00-9A1", "outputId": "c257d6fb-fabe-4225-d64a-89b346f637c9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.\n", "\n", "# Tools\n", "\n", "You may call one or more functions to assist with the user query.\n", "\n", "You are provided with function signatures within <tools></tools> XML tags:\n", "<tools>\n", "{\"type\": \"function\", \"function\": {\"name\": \"fetch_item_by_name\", \"description\": \"Fetch an item by name and returns the Item object.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"item_name\": {\"type\": \"string\", \"description\": \"The human-readable name of the item to fetch\"}}, \"required\": [\"item_name\"]}, \"return\": {\"type\": \"object\", \"nullable\": true, \"description\": \"Optional[Item]: The Item with the given name, or None if not found\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"get_all_items\", \"description\": \"Fetches all the inventory items\", \"parameters\": {\"type\": \"object\", \"properties\": {}}, \"return\": {\"type\": \"array\", \"items\": {\"type\": \"object\"}}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"inventory_check\", \"description\": \"Calculates the total value of inventory items in the target conversion rate.\\nWhen item_codes=None, calculates total value for all items.\", \"parameters\": {\"type\": \"object\", \"properties\": {\"item_codes\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"nullable\": true, \"description\": \"List of item codes to include (None for all items)\"}, \"conversion_rate\": {\"type\": \"number\", \"description\": \"Exchange rate to convert costs to target currency\"}}, \"required\": [\"item_codes\", \"conversion_rate\"]}, \"return\": {\"type\": \"number\", \"description\": \"Total value of matching items in target currency, rounded to 2 decimals\"}}}\n", "{\"type\": \"function\", \"function\": {\"name\": \"get_usd_to_euro_conversion_rate\", \"description\": \"Gets the conversion rate from USD to EURO\", \"parameters\": {\"type\": \"object\", \"properties\": {}}, \"return\": {\"type\": \"number\"}}}\n", "</tools>\n", "\n", "For each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n", "<tool_call>\n", "{\"name\": <function-name>, \"arguments\": <args-json-object>}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "How much is the total inventory cost of item name: Bottled Water in Euros? Ensure to use fetch_item_by_name first for fetching the item code<|im_end|>\n", "<|im_start|>assistant\n", "\n"]}], "source": ["messages = []\n", "\n", "messages.append(user_query)\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=True,\n", "    add_generation_prompt=True,\n", "    add_special_tokens=False,\n", "    padding=True,\n", "    tools=tools,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "print(tokenizer.decode(input_ids[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 20707, "status": "ok", "timestamp": 1742165958839, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "jPOewumK_BrL", "outputId": "02456456-17bd-4030-95f1-aace59055674"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "\n", "    {\n", "        \"name\": \"fetch_item_by_name\",\n", "        \"arguments\": {\n", "            \"item_name\": \"Bottled Water\"\n", "        }\n", "    },\n", "    {\n", "        \"name\": \"inventory_check\",\n", "        \"arguments\": {\n", "            \"item_codes\": [\"12345\"],\n", "            \"conversion_rate\": 0.85\n", "        }\n", "    }\n", "\n", "]\n"]}], "source": ["output = generate_with_grammar(\n", "    model=model,\n", "    input_ids=input_ids\n", ")\n", "\n", "generated_tokens = output[:, input_ids.shape[1]:]\n", "\n", "decoded_output = tokenizer.batch_decode(generated_tokens, skip_special_tokens=True)\n", "\n", "for i, message in enumerate(decoded_output):\n", "    print(f\"{message}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pz_A2g9rIuGF"}, "source": ["The model should return the values for the arguments of `fetch_item_by_name` and `inventory_check`"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 10, "status": "ok", "timestamp": 1742165978785, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "G62KcBUq_D1C", "outputId": "31c6c040-a0d8-4662-8d90-cabf09a37d03"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["item_name: Bottled Water, item_code: ITEM-002\n", "conversion_rate: 0.85\n"]}, {"data": {"text/plain": ["17.68"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "content = json.loads(decoded_output[0])\n", "arguments_for_item_name = content[0]['arguments']\n", "item_name = arguments_for_item_name['item_name']\n", "\n", "item_code = fetch_item_by_name(item_name).item_code\n", "print(f\"item_name: {item_name}, item_code: {item_code}\")\n", "\n", "arguments_for_inventory_check = content[1]['arguments']\n", "conversion_rate = arguments_for_inventory_check['conversion_rate']\n", "print(f\"conversion_rate: {conversion_rate}\")\n", "\n", "result_total = inventory_check([item_code], conversion_rate)\n", "result_total"]}, {"cell_type": "markdown", "metadata": {"id": "6EYlMN28KLv4"}, "source": ["After computing (actually call the inventory total of Bottled Water, we prompt the model again.\n", "\n", "Note that we've added the prompt below for for better accuracy.\n", "\n", "\"Answer:\\n\""]}, {"cell_type": "code", "execution_count": 62, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 906, "status": "ok", "timestamp": 1742165991037, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "_PNksyC7HQZq", "outputId": "a9fdc69e-7306-4e8f-d25e-7e774b01da7e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "How much is the total inventory cost of item name: Bottled Water in Euros? Ensure to use fetch_item_by_name first for fetching the item code<|im_end|>\n", "<|im_start|>assistant\n", "<tool_call>\n", "{\"name\": \"inventory_check\", \"arguments\": {\"item_codes\": [], \"conversion_rate\": 0.85}}\n", "</tool_call><|im_end|>\n", "<|im_start|>user\n", "<tool_response>\n", "17.68\n", "</tool_response><|im_end|>\n", "<|im_start|>assistant\n", "Answer:\n", "\n"]}], "source": ["messages = []\n", "\n", "original_prompt = user_query['content']\n", "\n", "prompt_with_context = f\"\"\"You are a super helpful AI assistant.\n", "You are asked to answer a question based on the following context information.\n", "Question:\n", "{original_prompt}\"\"\"\n", "\n", "messages.append({\n", "    \"role\": \"user\",\n", "    \"content\": prompt_with_context\n", "})\n", "\n", "tool_call_id = generate_alphanumeric()\n", "tool_calls = [{\n", "    \"id\": tool_call_id,\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "        \"name\": \"inventory_check\",\n", "        \"arguments\": arguments\n", "    }\n", "}]\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"tool_calls\": tool_calls\n", "})\n", "messages.append({\n", "    \"role\": \"tool\",\n", "    \"name\": \"inventory_check\",\n", "    \"content\": result_total  # pass the result total\n", "})\n", "\n", "messages.append({\n", "    \"role\": \"assistant\",\n", "    \"content\": \"Answer:\\n\"\n", "})\n", "\n", "tokenizer = copy.deepcopy(tokenizer_orig)\n", "tool_prompt = tokenizer.apply_chat_template(\n", "    messages,\n", "    continue_final_message=True,\n", "    add_special_tokens=True,\n", "    return_tensors=\"pt\",\n", "    return_dict=True,\n", "    tools=None,\n", ")\n", "tool_prompt = tool_prompt.to(model.device)\n", "\n", "print(tokenizer.decode(tool_prompt['input_ids'][0]))"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 708, "status": "ok", "timestamp": 1742165996187, "user": {"displayName": "<PERSON>", "userId": "11553494090571428644"}, "user_tz": -480}, "id": "URnnSKQ0Hf-T", "outputId": "acfa55a3-0496-4277-a882-9546513d542e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The total inventory cost of item name: Bottled Water is €17.68.\n"]}], "source": ["out = model.generate(**tool_prompt, max_new_tokens=128)\n", "generated_text = out[0, tool_prompt['input_ids'].shape[1]:]\n", "\n", "print(tokenizer.decode(generated_text, skip_special_tokens=True))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [{"file_id": "12cUzlVDyxosNhe_k4J-g3zQtUmvl2_NR", "timestamp": 1741936053454}], "toc_visible": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"025ae279fcb74246b9bfb3bfd923904c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "03d65e13dc174e92bbbcbc5088909f9d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0431e31bf3ea4e0d80eaef4db8311bd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "043f65a4ea764e6cac6c9635c97f97eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d712e72eebf14a23a5c321795d0a4d19", "placeholder": "​", "style": "IPY_MODEL_906d688903c7464ca09c6c61644a27a0", "value": "merges.txt: 100%"}}, "051169313068408a9aee6ae705be5d72": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_16ef86cc39a44c839621cda63cccfc41", "placeholder": "​", "style": "IPY_MODEL_92b35a13a55b4838bf1c728b8a74bab1", "value": "added_tokens.json: 100%"}}, "05f2869680dd4e6aa1e8068406bff183": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "063541a0053545bea8f2e876fd2acb68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_09d55268e88d4dc288a001427f61a4fd", "placeholder": "​", "style": "IPY_MODEL_806dac7b48c0492396d0e967c65d8c71", "value": "config.json: 100%"}}, "06de6a033cd9416da2d32e343ffd6f82": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "096795df31e64d2688c4be21e1326498": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7ff72b61dadb432f98826d83aa972e70", "placeholder": "​", "style": "IPY_MODEL_bb0039fe21594c27a09e91d58f0db14a", "value": "special_tokens_map.json: 100%"}}, "09d55268e88d4dc288a001427f61a4fd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0accca7a631d42b8a95b7dcad70d14b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0ad2ded850b542f5802ade7571301c3b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0dd25d69dee74fd58dd979cca5d3e587": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0e74b5680f5e4e5787bdb5e88984b74e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "103932911c4d409b8d801f9343affab9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "10a81cd2eab6441a872147db79f7ef1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "10c7f59dc0864aa59b754f0234514f1e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_063541a0053545bea8f2e876fd2acb68", "IPY_MODEL_92dc9145ab524f498bfc0c35f9fdb174", "IPY_MODEL_11ca17ec35074f318bc144a9227478ac"], "layout": "IPY_MODEL_ea1e5a33362a43db8766cb0c08b578dd"}}, "1119aa8b93da44fe84f9133c6d564ffd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "11ca17ec35074f318bc144a9227478ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5ebe13f6cae44361aed89ea22530cc87", "placeholder": "​", "style": "IPY_MODEL_6fd5a120e3ed475ebb764bd55a45d8ec", "value": " 765/765 [00:00&lt;00:00, 62.9kB/s]"}}, "14c690cb2b5f410dbd8bf82f772c4802": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "14cce34c29324f2abb209aa31251d414": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3c1922834e5b420bbe3d8f7d2429b492", "placeholder": "​", "style": "IPY_MODEL_0accca7a631d42b8a95b7dcad70d14b8", "value": " 1.67M/1.67M [00:00&lt;00:00, 4.95MB/s]"}}, "16ef86cc39a44c839621cda63cccfc41": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "170c8e302fa5457a91c43e02b964ff4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6867fef6ddfa4901b267e92a9a2cdae9", "placeholder": "​", "style": "IPY_MODEL_1119aa8b93da44fe84f9133c6d564ffd", "value": " 2.78M/2.78M [00:00&lt;00:00, 11.1MB/s]"}}, "1de87285321f4f01bc8bf86e3b0f86a5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1ffa1f53cbd74a76a5933f749881e94c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2483117f317a44d0b4eecb4a4e4f62aa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "26062536695547da98bb96e8bae5fa30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_587b3d5a5782431d8ba0257f997390c0", "IPY_MODEL_c2e9d271af1449329bb7a3aeed9dec95", "IPY_MODEL_9f95b66bc04b45ac8c291e90c4101921"], "layout": "IPY_MODEL_c1275a9bc9d949c8944870b1d0a58f0c"}}, "2a01d4ad5c8441baa8357117a9bada43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2b6ea547721a4099815aac0c315dc97c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2c0c7406572e4218a9541d152f0229c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2b6ea547721a4099815aac0c315dc97c", "placeholder": "​", "style": "IPY_MODEL_de7962e4c6a04f839a98fd8317ca26d7", "value": " 613/613 [00:00&lt;00:00, 38.0kB/s]"}}, "2c43b1321e4d4fafb433c496e6c19b90": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2c8df8d547574b2f985c7794b4642b76": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dcc9afb45354f89b5930e740d889a7d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e3659031c23d4bcb8136899c476e1a6c", "IPY_MODEL_e1a8bf02391b48efa942016ec7bebc6d", "IPY_MODEL_8f5bd8b322054dddb9d394e452544762"], "layout": "IPY_MODEL_f2ae3349b890487abee3a557b186294e"}}, "2ec34140efa84ca29135afa6b4aa475f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ffa1f53cbd74a76a5933f749881e94c", "max": 632, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_624d5c08b8fd4e38bca6c55878bd26eb", "value": 632}}, "3089a3c50708464783a3447c0ef650f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2c43b1321e4d4fafb433c496e6c19b90", "placeholder": "​", "style": "IPY_MODEL_05f2869680dd4e6aa1e8068406bff183", "value": " 1.67M/1.67M [00:00&lt;00:00, 4.98MB/s]"}}, "32be1aba98784b208667179dd5862de2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3321d3cd97f942b38125b98226a72899": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33d943034caf4ad98387b9116ceec2b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_66d521d8e65e4f5285f89daaf7c8c38b", "placeholder": "​", "style": "IPY_MODEL_103932911c4d409b8d801f9343affab9", "value": " 7.51k/7.51k [00:00&lt;00:00, 473kB/s]"}}, "341c4de23f1848c79d0ce62190f308e7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d3a4e95a8954714bb681834d05ab309", "placeholder": "​", "style": "IPY_MODEL_e52f671db0794a548ae77ad06b5134b6", "value": " 7.03M/7.03M [00:00&lt;00:00, 14.0MB/s]"}}, "34852aab3a4441b88dd65be909a7673b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8a55ef8aed5144dbb02dfc99e5bd43ba", "placeholder": "​", "style": "IPY_MODEL_fc9ad241d66b4cb380183a04fc2e6c2f", "value": "added_tokens.json: 100%"}}, "3671f2e22aac43edadd1240a1e1deeea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3b9cbaf2c1044450b41dab928d7c27da": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c1922834e5b420bbe3d8f7d2429b492": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3c73c54b7701493d92ff6ced3735dce3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ca40da457a94b0c9bf4f0d3e7045bad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "40ef2bc30330404b89c337510d560b5b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "44aed22fadf340babe47e18cc3609603": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "453990d396d348769cee958b5826a932": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a962f4949e1f42d8aa1791674e16da78", "IPY_MODEL_d052810c41e843bb8e63ed650fc72dbb", "IPY_MODEL_341c4de23f1848c79d0ce62190f308e7"], "layout": "IPY_MODEL_14c690cb2b5f410dbd8bf82f772c4802"}}, "498f42b240534add84e727a2ac2f8d0c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "49eb1b29c8064d3baf42e78cd9305b73": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a8f7ce47d0f4071831027affdb22bfa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4b481b20fe98408e97377eb6efea26f4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d3b101d7b1a492bb1f2e0c1f94f7674": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f4ec8343cca49529ba82e28a94d4ba3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_043f65a4ea764e6cac6c9635c97f97eb", "IPY_MODEL_69ac7164e60f4a3aa46a37a722e03451", "IPY_MODEL_3089a3c50708464783a3447c0ef650f1"], "layout": "IPY_MODEL_03d65e13dc174e92bbbcbc5088909f9d"}}, "52a12ec9d90444f182a329c34feb9e29": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "557d12eabf05431980b26af12ca9e3b3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5701898928264a458c89f2c8a080e83a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "587b3d5a5782431d8ba0257f997390c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_498f42b240534add84e727a2ac2f8d0c", "placeholder": "​", "style": "IPY_MODEL_cc5e0350a895469b8907801d2ddfc116", "value": "tokenizer_config.json: 100%"}}, "5d880920945845578a03b0216241ecb9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2f5d7804bce46af81dad2129baf5a01", "placeholder": "​", "style": "IPY_MODEL_eff94aaebf5e45479fabd5b11c156291", "value": " 7.03M/7.03M [00:00&lt;00:00, 21.4MB/s]"}}, "5ebe13f6cae44361aed89ea22530cc87": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6091d60224314261a301664d0362822a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61fcb0f1cdd84a15a996cf6faaa0dc11": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c0be1d43b3f446b98c32951e3d2bebe6", "IPY_MODEL_8a5cdb013ac046199d532fbaf286b577", "IPY_MODEL_33d943034caf4ad98387b9116ceec2b5"], "layout": "IPY_MODEL_7223c1ddd12240eda1aa0d94120c7cbb"}}, "624d5c08b8fd4e38bca6c55878bd26eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "62d6a0005faf4359a3b7e980f6a12676": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "657fa8603e7e4e23b30ea3fbe3b66bcd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e640358764354406b64ea9d8cd62b97c", "max": 1671853, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c895eb1145aa41e2b0dc4e7862c8e1ad", "value": 1671853}}, "66d521d8e65e4f5285f89daaf7c8c38b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6867fef6ddfa4901b267e92a9a2cdae9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "69ac7164e60f4a3aa46a37a722e03451": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2c8df8d547574b2f985c7794b4642b76", "max": 1671853, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e92bfb506b4744c08f40d33962e7c856", "value": 1671853}}, "69bbbbee4ca24dfa98cb2fd7aaded0d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ae2bcda9b1540bfae0b7c182e60b2c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b3ba88110d5c4b1da54fa61657d3dae4", "placeholder": "​", "style": "IPY_MODEL_3ca40da457a94b0c9bf4f0d3e7045bad", "value": " 632/632 [00:00&lt;00:00, 18.1kB/s]"}}, "6b4cec98b8e64dfaa187b21f34d0a45e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_096795df31e64d2688c4be21e1326498", "IPY_MODEL_d68c4628980a462c9b5dad58c54d9fbf", "IPY_MODEL_2c0c7406572e4218a9541d152f0229c9"], "layout": "IPY_MODEL_62d6a0005faf4359a3b7e980f6a12676"}}, "6d3a4e95a8954714bb681834d05ab309": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f45335cfbda48b4bc7de3824c8adda2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6fd5a120e3ed475ebb764bd55a45d8ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7223c1ddd12240eda1aa0d94120c7cbb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "73aa3cc053e44d1aa1c2c3930e07abce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3c73c54b7701493d92ff6ced3735dce3", "placeholder": "​", "style": "IPY_MODEL_c18476863ce24354adbb343720fa1085", "value": "vocab.json: 100%"}}, "754e6537ebc3499499a68fbb5b25df53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40ef2bc30330404b89c337510d560b5b", "placeholder": "​", "style": "IPY_MODEL_a8f68b32207f43898a735122fb1e1026", "value": " 2.78M/2.78M [00:00&lt;00:00, 6.74MB/s]"}}, "768be9bccefb4359bc21c0731e534399": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fa6a56bbe82f412d8eff4968cddff88e", "placeholder": "​", "style": "IPY_MODEL_cfb8e59c6bdd4e84b24507aa86db8eed", "value": "vocab.json: 100%"}}, "790165ff4f0046448b17bd3505cbc416": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "79b9bffeb1fb44abae641493395ddfd1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ce8c1e1642d4ff2b4e9fdd75487a7c1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7eb5ea11b3c84889995392a4a34e273b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ff72b61dadb432f98826d83aa972e70": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "806dac7b48c0492396d0e967c65d8c71": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "837b744b041b48949cf2fb0e53ccf931": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "88be632353ac4aa9b4c7580f5c8f4d05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "89a8ed76a8df4e87a1659b6adfb0d458": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a442f35c4641456ba3b3b3c4fbee8995", "IPY_MODEL_a1604bdde1b34bbfb6ff7c12cb6c6e58", "IPY_MODEL_5d880920945845578a03b0216241ecb9"], "layout": "IPY_MODEL_4d3b101d7b1a492bb1f2e0c1f94f7674"}}, "8a55ef8aed5144dbb02dfc99e5bd43ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8a5cdb013ac046199d532fbaf286b577": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1de87285321f4f01bc8bf86e3b0f86a5", "max": 7512, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cc126f714dd0422fadec2262b9818560", "value": 7512}}, "8f5bd8b322054dddb9d394e452544762": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e3523f3c07f64fc88dacd72ac2ca1d3b", "placeholder": "​", "style": "IPY_MODEL_ed299eeae37b44afa7f2c605a93199e4", "value": " 3.09G/3.09G [00:22&lt;00:00, 396MB/s]"}}, "906d688903c7464ca09c6c61644a27a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "90dd6bb65f444300b184c720a6e1b339": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "926ae2d61c204b0c8b9b6e3a4a90926b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0e74b5680f5e4e5787bdb5e88984b74e", "placeholder": "​", "style": "IPY_MODEL_2a01d4ad5c8441baa8357117a9bada43", "value": "generation_config.json: 100%"}}, "92b35a13a55b4838bf1c728b8a74bab1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "92dc9145ab524f498bfc0c35f9fdb174": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7eb5ea11b3c84889995392a4a34e273b", "max": 765, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c11547368c9a4e81bf787800d355d7f4", "value": 765}}, "9841a7ca0ec74e61b6c9654e2774f955": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_32be1aba98784b208667179dd5862de2", "placeholder": "​", "style": "IPY_MODEL_e8ab843c82c34379b3f7c0edf8fa98ba", "value": "merges.txt: 100%"}}, "9f95b66bc04b45ac8c291e90c4101921": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7ce8c1e1642d4ff2b4e9fdd75487a7c1", "placeholder": "​", "style": "IPY_MODEL_6f45335cfbda48b4bc7de3824c8adda2", "value": " 7.51k/7.51k [00:00&lt;00:00, 365kB/s]"}}, "a1604bdde1b34bbfb6ff7c12cb6c6e58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_69bbbbee4ca24dfa98cb2fd7aaded0d4", "max": 7031863, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c02c11128bed4f3bb57f3d185df12ebf", "value": 7031863}}, "a3f10cdeab64466c86de3470279b5fbd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a442f35c4641456ba3b3b3c4fbee8995": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e545856974b842b3ad9d76508a9e9fcd", "placeholder": "​", "style": "IPY_MODEL_4a8f7ce47d0f4071831027affdb22bfa", "value": "tokenizer.json: 100%"}}, "a4451c36012745b9a12218af49e93956": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_73aa3cc053e44d1aa1c2c3930e07abce", "IPY_MODEL_cc2b6ccbb4044f499b3616dbb3704275", "IPY_MODEL_170c8e302fa5457a91c43e02b964ff4c"], "layout": "IPY_MODEL_557d12eabf05431980b26af12ca9e3b3"}}, "a5b61a1d115d4a9db489042bedbec8f9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a8f68b32207f43898a735122fb1e1026": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a9222c840014441d9199ddf2b7650d08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3321d3cd97f942b38125b98226a72899", "placeholder": "​", "style": "IPY_MODEL_e9b5c45236e94e3480dd6dbbb3d6fcf2", "value": " 632/632 [00:00&lt;00:00, 76.6kB/s]"}}, "a962f4949e1f42d8aa1791674e16da78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca9d082a491b499c93fc044ea69151e5", "placeholder": "​", "style": "IPY_MODEL_f1edfa6acde44acca81dc5cac5543ab8", "value": "tokenizer.json: 100%"}}, "ab92d2ba313e4e1097f6bc302fb98409": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_926ae2d61c204b0c8b9b6e3a4a90926b", "IPY_MODEL_cda22ce77643405596be676b2eb35c80", "IPY_MODEL_c9195c4996084510ac2baeffc3958673"], "layout": "IPY_MODEL_e718ccda074f49e1a488d71fd5c8f71c"}}, "b1f0cbd2181141d1a33948740a18a917": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79b9bffeb1fb44abae641493395ddfd1", "placeholder": "​", "style": "IPY_MODEL_0dd25d69dee74fd58dd979cca5d3e587", "value": "special_tokens_map.json: 100%"}}, "b3ba88110d5c4b1da54fa61657d3dae4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4aa6dc09c4147bab29f7ace05579fe4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_768be9bccefb4359bc21c0731e534399", "IPY_MODEL_fefd0ef6800c4a8aa57e22d3681fa324", "IPY_MODEL_754e6537ebc3499499a68fbb5b25df53"], "layout": "IPY_MODEL_2483117f317a44d0b4eecb4a4e4f62aa"}}, "b56b2851286c47e09cd032bad6f697cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8d3126b7d9d4a9a80c40789d9f68446": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a5b61a1d115d4a9db489042bedbec8f9", "placeholder": "​", "style": "IPY_MODEL_90dd6bb65f444300b184c720a6e1b339", "value": " 613/613 [00:00&lt;00:00, 51.4kB/s]"}}, "bb0039fe21594c27a09e91d58f0db14a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bd19725aa20d42d399f029caad53aa01": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9841a7ca0ec74e61b6c9654e2774f955", "IPY_MODEL_657fa8603e7e4e23b30ea3fbe3b66bcd", "IPY_MODEL_14cce34c29324f2abb209aa31251d414"], "layout": "IPY_MODEL_837b744b041b48949cf2fb0e53ccf931"}}, "be0a1fb6e452464fbca63cba9a005b2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4b481b20fe98408e97377eb6efea26f4", "max": 632, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f13f684bf6224980830986ed0a81e85a", "value": 632}}, "bedc55a741994190afdde78a0b6952ce": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c02c11128bed4f3bb57f3d185df12ebf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c0be1d43b3f446b98c32951e3d2bebe6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b56b2851286c47e09cd032bad6f697cf", "placeholder": "​", "style": "IPY_MODEL_cd5e8b1e84c74e0e959bffb5c75abf50", "value": "tokenizer_config.json: 100%"}}, "c109cfd6be8c428993f7089a6c2a94df": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b1f0cbd2181141d1a33948740a18a917", "IPY_MODEL_c2a1021f9a8e473ba170c943a003dc84", "IPY_MODEL_b8d3126b7d9d4a9a80c40789d9f68446"], "layout": "IPY_MODEL_3b9cbaf2c1044450b41dab928d7c27da"}}, "c11547368c9a4e81bf787800d355d7f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c1275a9bc9d949c8944870b1d0a58f0c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c18476863ce24354adbb343720fa1085": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c2a1021f9a8e473ba170c943a003dc84": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a3f10cdeab64466c86de3470279b5fbd", "max": 613, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d9288dbb67ca47d29fe3d188e11ca3d0", "value": 613}}, "c2e9d271af1449329bb7a3aeed9dec95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_06de6a033cd9416da2d32e343ffd6f82", "max": 7512, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_88be632353ac4aa9b4c7580f5c8f4d05", "value": 7512}}, "c2f5d7804bce46af81dad2129baf5a01": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2f9d9cbd9b648fdaa533005e0231538": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c895eb1145aa41e2b0dc4e7862c8e1ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c9195c4996084510ac2baeffc3958673": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bedc55a741994190afdde78a0b6952ce", "placeholder": "​", "style": "IPY_MODEL_790165ff4f0046448b17bd3505cbc416", "value": " 265/265 [00:00&lt;00:00, 26.3kB/s]"}}, "ca9d082a491b499c93fc044ea69151e5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cc126f714dd0422fadec2262b9818560": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cc2b6ccbb4044f499b3616dbb3704275": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_44aed22fadf340babe47e18cc3609603", "max": 2776833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c2f9d9cbd9b648fdaa533005e0231538", "value": 2776833}}, "cc5e0350a895469b8907801d2ddfc116": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd5e8b1e84c74e0e959bffb5c75abf50": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cda22ce77643405596be676b2eb35c80": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e72ddbfb5d9b4b86830a77498594da7c", "max": 265, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fad3f711763a45efa87750a5a978395f", "value": 265}}, "cfb8e59c6bdd4e84b24507aa86db8eed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d052810c41e843bb8e63ed650fc72dbb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_025ae279fcb74246b9bfb3bfd923904c", "max": 7031863, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0431e31bf3ea4e0d80eaef4db8311bd6", "value": 7031863}}, "d68c4628980a462c9b5dad58c54d9fbf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_52a12ec9d90444f182a329c34feb9e29", "max": 613, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f50500601bc94cdeb200a0d3b3c80a95", "value": 613}}, "d712e72eebf14a23a5c321795d0a4d19": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9288dbb67ca47d29fe3d188e11ca3d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "de7962e4c6a04f839a98fd8317ca26d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e1a8bf02391b48efa942016ec7bebc6d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_10a81cd2eab6441a872147db79f7ef1d", "max": 3087467144, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3671f2e22aac43edadd1240a1e1deeea", "value": 3087466850}}, "e3523f3c07f64fc88dacd72ac2ca1d3b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e3659031c23d4bcb8136899c476e1a6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6091d60224314261a301664d0362822a", "placeholder": "​", "style": "IPY_MODEL_5701898928264a458c89f2c8a080e83a", "value": "model.safetensors: 100%"}}, "e52f671db0794a548ae77ad06b5134b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e545856974b842b3ad9d76508a9e9fcd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5c600ca83f345b7bb6550bd6f25bc2c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_051169313068408a9aee6ae705be5d72", "IPY_MODEL_2ec34140efa84ca29135afa6b4aa475f", "IPY_MODEL_6ae2bcda9b1540bfae0b7c182e60b2c7"], "layout": "IPY_MODEL_0ad2ded850b542f5802ade7571301c3b"}}, "e640358764354406b64ea9d8cd62b97c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6ebb4fdfb2c43a5a3e7df423112cd46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e718ccda074f49e1a488d71fd5c8f71c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e72ddbfb5d9b4b86830a77498594da7c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e8ab843c82c34379b3f7c0edf8fa98ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e92bfb506b4744c08f40d33962e7c856": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e9b5c45236e94e3480dd6dbbb3d6fcf2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ea1e5a33362a43db8766cb0c08b578dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed299eeae37b44afa7f2c605a93199e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee6aa8e7f0ce4a31981d8e7f751a8c03": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_34852aab3a4441b88dd65be909a7673b", "IPY_MODEL_be0a1fb6e452464fbca63cba9a005b2b", "IPY_MODEL_a9222c840014441d9199ddf2b7650d08"], "layout": "IPY_MODEL_e6ebb4fdfb2c43a5a3e7df423112cd46"}}, "eff94aaebf5e45479fabd5b11c156291": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f13f684bf6224980830986ed0a81e85a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f1edfa6acde44acca81dc5cac5543ab8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f2ae3349b890487abee3a557b186294e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f50500601bc94cdeb200a0d3b3c80a95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f73f20e93f8a4763bd105d3dc8f61daf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fa6a56bbe82f412d8eff4968cddff88e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fad3f711763a45efa87750a5a978395f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fc9ad241d66b4cb380183a04fc2e6c2f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fefd0ef6800c4a8aa57e22d3681fa324": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_49eb1b29c8064d3baf42e78cd9305b73", "max": 2776833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f73f20e93f8a4763bd105d3dc8f61daf", "value": 2776833}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}