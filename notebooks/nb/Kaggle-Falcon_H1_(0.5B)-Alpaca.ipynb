{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Unsloth Training for Falcon H1\n", "\n", "This Notebook has been authored by TII Falcon Team.\n", "For more details on Falcon H1 series of models :\n", "1. [Official Page](https://tiiuae.github.io/Falcon-H1/)\n", "2. [Blogpost](https://falcon-lm.github.io/blog/falcon-h1/)\n", "3. [Official Gith<PERSON> Page ](https://github.com/tiiuae/Falcon-H1)\n", "4. [HF Collection](https://huggingface.co/collections/tiiuae/falcon-h1-6819f2795bc406da60fab8df)\n", "\n", "To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nos.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n\n!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth\n!pip install --upgrade transformers==4.53.2 \"huggingface_hub>=0.34.0\" \"datasets>=3.4.1,<4.0.0\"\n"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "# For faster training, we can use <PERSON><PERSON>'s CUDA function instead\n", "!pip install --no-deps causal-conv1d==1.5.0.post8\n", "!pip install --no-build-isolation mamba-ssm==2.2.4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "==((====))==  Unsloth 2024.8: Fast Gemma2 patching. Transformers = 4.43.3.\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.3.1+cu121. CUDA = 7.5. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.26.post1. FA2 = False]\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c97875e799204d779e18fba0b95fd5bf", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.22G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c72c0615369427e962f09cc45097193", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/168 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ab8732869fc3471ea8e0487873e48e15", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/46.3k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6057012b69af430f804e9ef0fcc85965", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.24M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a8e94a46887745b7b654aa12bc553ac1", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/555 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a391fc6789a74ff3871c9dd840a72369", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.5M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "max_seq_length = 2048  # Choose any! We auto support RoPE Scaling internally!\n", "dtype = (\n", "    None  # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", ")\n", "load_in_4bit = True  # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Meta-Llama-3.1-8B-bnb-4bit\",  # Llama-3.1 15 trillion tokens model 2x faster!\n", "    \"unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit\",\n", "    \"unsloth/Meta-Llama-3.1-70B-bnb-4bit\",\n", "    \"unsloth/Meta-Llama-3.1-405B-bnb-4bit\",  # We also uploaded 4bit for 405b!\n", "    \"unsloth/Mistral-Nemo-Base-2407-bnb-4bit\",  # New Mistral 12b 2x faster!\n", "    \"unsloth/Mistral-Nemo-Instruct-2407-bnb-4bit\",\n", "    \"unsloth/mistral-7b-v0.3-bnb-4bit\",  # Mistral v3 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/Phi-3-mini-4k-instruct\",  # Phi-3 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/gemma-2-9b-bnb-4bit\",\n", "    \"unsloth/gemma-2-27b-bnb-4bit\",  # <PERSON> 2x faster!\n", "    \"unsloth/gemma-2-2b-bnb-4bit\",  # New small Gemma model!\n", "]  # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Falcon-H1-0.5B-Instruct\", # Choose any model from https://huggingface.co/collections/tiiuae/falcon-h1-6819f2795bc406da60fab8df\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.8 patched 26 layers with 26 QKV layers, 26 O layers and 26 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules=[\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                    \"gate_proj\", \"up_proj\", \"down_proj\"], #<PERSON><PERSON> out_proj and conv1d layers should not be included here see https://github.com/huggingface/peft/pull/2562\n", "    lora_alpha = 16,\n", "    lora_dropout = 0.1,\n", "    use_gradient_checkpointing = False,\n", "    random_state = 3407,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the Alpaca dataset from [yahma](https://huggingface.co/datasets/yahma/alpaca-cleaned), which is a filtered version of 52K of the original [Alpaca dataset](https://crfm.stanford.edu/2023/03/13/alpaca.html). You can replace this code section with your own data prep.\n", "\n", "**[NOTE]** To train only on completions (ignoring the user's input) read TRL's docs [here](https://huggingface.co/docs/trl/sft_trainer#train-on-completions-only).\n", "\n", "**[NOTE]** Remember to add the **EOS_TOKEN** to the tokenized output!! Otherwise you'll get infinite generations!\n", "\n", "If you want to use the `llama-3` template for ShareGPT datasets, try our conversational [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Alpaca.ipynb)\n", "\n", "For text completions like novel writing, try this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_(7B)-Text_Completion.ipynb)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8b65ab1fa061457e89f295e8a81e635b", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/11.6k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b6fe8ee676c4dbeb48ee587c7be367a", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/44.3M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a3ac24b2fbf45349c1a08a167a4fc8d", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/51760 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "956ea575313b4306bed317ed3ea345eb", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/51760 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token # Must add EOS_TOKEN\n", "def formatting_prompts_func(examples):\n", "    instructions = examples[\"instruction\"]\n", "    inputs       = examples[\"input\"]\n", "    outputs      = examples[\"output\"]\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        # Must add EOS_TOKEN, otherwise your generation will go on forever!\n", "        text = alpaca_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"yahma/alpaca-cleaned\", split = \"train\")\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "92d9648dd6bb4086ad9ce52db641bf87", "version_major": 2, "version_minor": 0}, "text/plain": ["Map (num_proc=2):   0%|          | 0/51760 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    train_dataset=dataset,\n", "    dataset_text_field=\"text\",\n", "    max_seq_length=max_seq_length,\n", "    dataset_num_proc=2,\n", "    packing=False,  # Can make training 5x faster for short sequences.\n", "    args=SFTConfig(\n", "        per_device_train_batch_size=2,\n", "        gradient_accumulation_steps=8,\n", "        warmup_steps=5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps=60,\n", "        learning_rate=2e-4,\n", "        logging_steps=1,\n", "        optim=\"adamw_8bit\",\n", "        weight_decay=0.01,\n", "        lr_scheduler_type=\"linear\",\n", "        seed=3407,\n", "        output_dir=\"outputs\",\n", "        report_to=\"none\",  # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "2.697 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 51,760 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 60\n", " \"-____-\"     Number of trainable parameters = 20,766,720\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 03:16, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.854400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>2.406500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.755700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.990000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.632200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.651400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.220900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.359800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.127900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.266700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.028200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.021900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.990900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.156600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.968200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.961700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.075100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.302900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.028400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.925500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.960100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.975800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.900400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>1.033100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.105800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.113500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.090900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.939000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.883100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.941700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.914600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.920300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>1.024900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.868300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.964300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.908900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.908300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.806800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>1.139700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>1.216000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.963900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.984100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.945700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.923600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.974400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.971000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.925200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.234300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.932400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.085700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>1.062500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.971500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>1.244900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.856600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.071400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.925000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.845200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.895700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.958300</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["278.0636 seconds used for training.\n", "4.63 minutes used for training.\n", "Peak reserved memory = 7.684 GB.\n", "Peak reserved memory for training = 4.987 GB.\n", "Peak reserved memory % of max memory = 52.102 %.\n", "Peak reserved memory for training % of max memory = 33.815 %.\n"]}], "source": ["#@title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory         /max_memory*100, 3)\n", "lora_percentage = round(used_memory_for_lora/max_memory*100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<bos>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nContinue the fibonnaci sequence.\\n\\n### Input:\\n1, 1, 2, 3, 5, 8\\n\\n### Response:\\nThe fibonnaci sequence is a sequence of numbers where each number is the sum of the two preceding ones. The sequence is defined as follows:\\n\\n1, 1, 2, 3, 5, 8, 13, 21, 34, 55, ']"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"Continue the fi<PERSON><PERSON><PERSON> sequence.\", # instruction\n", "        \"1, 1, 2, 3, 5, 8\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**inputs, max_new_tokens = 64, use_cache = True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {}, "source": [" You can also use a `TextStreamer` for continuous inference - so you can see the generation token by token, instead of waiting the whole time!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "Continue the fi<PERSON><PERSON><PERSON> sequence.\n", "\n", "### Input:\n", "1, 1, 2, 3, 5, 8\n", "\n", "### Response:\n", "The <PERSON><PERSON><PERSON><PERSON> sequence is a sequence of numbers where each number is the sum of the two preceding ones. The sequence is defined as follows:\n", "\n", "1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765, 10946, 1771\n"]}], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"Continue the fi<PERSON><PERSON><PERSON> sequence.\", # instruction\n", "        \"1, 1, 2, 3, 5, 8\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.model',\n", " 'lora_model/added_tokens.json',\n", " 'lora_model/tokenizer.json')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bos>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "What is a famous tall tower in Paris?\n", "\n", "### Input:\n", "\n", "\n", "### Response:\n", "The Eiffel Tower is a famous tall tower in Paris, France. It is located in the 5th arrondissement of Paris and is one of the most recognizable landmarks in the world. The tower was built for the 1889 World's Fair and is 324 meters tall. It is made of iron and has 1,665 steps. The tower is a symbol of Paris and is a popular tourist attraction.<eos>\n"]}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "# alpaca_prompt = You MUST copy from above!\n", "\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"What is a famous tall tower in Paris?\", # instruction\n", "        \"\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "[**NEW**] To finetune and auto export to Ollama, try our [Ollama notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"kernelspec": {"display_name": "mlm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "accelerator": "GPU", "colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}}}}, "nbformat": 4, "nbformat_minor": 2}