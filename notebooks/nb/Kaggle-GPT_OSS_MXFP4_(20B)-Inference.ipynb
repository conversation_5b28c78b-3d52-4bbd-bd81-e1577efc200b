{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "CoVJPS_uUghm"}, "outputs": [], "source": "%%capture\n# We're installing the latest Torch, Triton, OpenAI's Triton kernels, Transformers and Unsloth!\n!pip install --upgrade -qqq uv\ntry: import numpy; install_numpy = f\"numpy=={numpy.__version__}\"\nexcept: install_numpy = \"numpy\"\n!uv pip install -qqq \\\n    \"torch>=2.8.0\" \"triton>=3.4.0\" {install_numpy} \\\n    \"unsloth_zoo[base] @ git+https://github.com/unslothai/unsloth-zoo\" \\\n    \"unsloth[base] @ git+https://github.com/unslothai/unsloth\" \\\n    torchvision bitsandbytes \\\n    git+https://github.com/huggingface/transformers \\\n    git+https://github.com/triton-lang/triton.git@main#subdirectory=python/triton_kernels\n"}, {"cell_type": "markdown", "metadata": {"id": "xRwOqyyOUghp"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "r2v_X2fA0Df5"}, "source": ["We're about to demonstrate the power of the new OpenAI GPT-OSS 20B model through an inference example. For our `bnb-4bit` version, use this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/GPT_OSS_BNB_(20B)-Inference.ipynb) instead.\n", "\n", "**We're using OpenAI's MXFP4 Triton kernels combined with Unsloth's kernels!**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 495, "referenced_widgets": ["46332ec475cc464d8af230667ae0390e", "8c0d9d7c3d744bd2adf238fa0fe82e2b", "a1f4207b466743dfb2124ba4cae93973", "d9f76eb3ebcb49e1ab9c5f842ac997ca", "961ef7f81ce848d1838a0db80c5eaae7", "f80261f1c7bd45dbbcb4ddbdc82b2457", "7dae163451094a349ff8871ca223a9ec", "b954ec926eb1455f955568c835b0d146", "beab54fd9fe8462dbc699ce223ab868a", "7a7f73ebfac84ad9aea5b9e67edcfeab", "ca66c1b98d4e4df589f5a2189d5b25fa", "331e05a218964657aec5fb809fc2cb97", "9a03c4cf2cd64577b758acca0e3f8451", "30dce22922b7445ea96006b6a4a5a6f1", "4b842088bf454066a80af148914cc193", "9dcb95a80f504f128c3308a303693595", "7696dab5612c43d5b9c88377afd86746", "502883196ab34781a4ebafe253a04b9c", "b21ed56558504f9794e882a9809ef92a", "2709ea7e528e4fccb1b3d91584127ef4", "50e35bf327bb46f4b4d5a62d3b376667", "cc6d565303fc406abdf9f057d130b7b3", "7e3177b1d6424df4bf8bb5039e22aeba", "7c234148b0ec4ed594b416c74eb7f277", "c249df6bc46b4b4f932e0de20f07571f", "e2f72e4eb915424cb657683efa77f6f9", "024e04f178ad4c46a294e00b0c9804d5", "86a59045323c49e5ae36df4187c980f8", "ed8cca379d1d42b397e25d4303576fd1", "dc33336f2691416ca6bbb997e8708515", "b3f720f4f63949d1ae67ec9933fcc554", "30fe3a15c55b4de0962e6d0d262f0bf7", "18c99ea233b04cd3af157d3f51622f4b", "c14b21107a514da7964bce32ff583867", "65f6acd17b9e4265a84b32d27098b6b3", "ef0b1598c3bb4967a105e8df4ba5a593", "0d8a6212ac4143148f0e10e5f6f34f39", "47abb02e2aa14d039444d47dfe5d3851", "aa5ad6fbcf904f2685f3f21461c97240", "4ac31f35a1884f10b35ac2de4cede4dc", "eca22aa57cb94488a9c7b0a88ee99afe", "1fedef1fa84549cb86a6ce0d6c4e8be4", "27657cc752874e298bd7811fd56dc487", "52e87a566c084e3fa94eb16929d4143a", "e07b827466eb44ad862e227ce01d3115", "d8b87a8fb9bc4970baa7cdd557d73d8c", "ea0defb653b040c6946c6a3fb1442127", "f21f7ba7c2dc4f419e1de8ae26b56cb8", "2867f9c09bd246b4a16df434e853112c", "bb08970edfcc4e099b9c25f1a74abf50", "4825ef359c2c400e987949e9f20f079b", "b2a58fbd03bd4022a6cfeb3c06eb4a83", "9a436449e45b40e6957178a8da1030af", "eb308cf282364d99bb9375940f066c24", "b00515649b0c419d8c93e9e2a1e49cd5", "4cd6a6dd3d224d9baf7a16d630d17c47", "394e2185bd7a4e9d8b0858f9972efab3", "d689e6f0d9f5481ba84c328dd13d1b89", "5d1374f6ef8049af9d6083c3be84f6bd", "14b87f1afe274422a8b681926dfa83dd", "5acef395976d4600afa3efe93d132e36", "31e466f2399746fe867a0174b4fc5f47", "cdc3d9982ff64f01b43b3ee435ea88fe", "74be0d4639b3498aa5474fa6c81e4c13", "5269421fa9404e178849ebe1045ff29a", "08a141f0691148fdafe8775c0736cee5", "f67449e444354285bbf61d68823e32e2", "1f64d696d8cc4b058120e0ea5e8df8bf", "ae61558a626447969853ecc58e89542a", "4ef4279a32b1478b9e7844a35adef6de", "40d970fff0e74519b3410d1bb9aefd4c", "5dde27e2ffd442759024e9b6f56fe48c", "f3c1c47e690e43678e61f889481bee35", "a44c1a0de37748b3a24206a0ac7deab3", "3ca2c854f5544debadb962c92a997d9d", "60a3b764e47a4950a2712c27245eefde", "98c50e1ed03e415791d868fea1005953", "bf094263d5a749778ae2278942829973", "549636c70763481abb740c03e47398b7", "b58c3304dfbf40b3ac2e65fc2376398d", "0ad489a82fde4c47b7284073b306b89b", "f357d0253fe74a6192fc478a8914d9ec", "01050a1514564565a06820e0319ad36d", "cc9b856db03e4ceb8a4f6fd9b144dc48", "2bbb6410ba16420e8a18e58452dde3b9", "2dc30da7b1264f97924fb2764afc23c6", "8a8122d168d149259056397b9cba3f92", "a4628b07bfe147a288acca66e0c390e4", "66b531b6f36742bc8754c76ab857b264", "feb753fa9c3e4fb386d605c77a4fbe7d", "308ff63515754b93b3ed2481e8394078", "4381a1bf596a4fceb6d36fe8d800d0de", "c0c1ce1743ee4bde96744a9e2d41a1cd", "2e169785e1464ad09d7b24e57df83832", "36f88d631ac8460797c91cfe89911b28", "5f00fb7cafb94e669d7c8fddc78ffd67", "dffd7dc7a1744494b72c78a663aeadc9", "ef7e2755b48741649922d7d1b028c456", "1fb60417dd5f4335957fa29aa6fa386e", "533266e3f34e41728a088bde9d13f792", "e95fbd123a2b4961b6f9e552536f37a3", "38867eef6d7c4476baa5940a78f889fd", "1ecd4377ab8e4d739f2f64a1954e54a2", "9f25b9c509a4499fbb69da0bcddba2d6", "ec7d1b8da3cd40c19aaf974cf37d6d9a", "f0fc507739d34afea82fb8fa32b1eaca", "9c6f82d565fd48de8d245e59152a6236", "5b42364ec24f4055828aa139afaa0b1a", "d2d1792f18fd4170a89169b4dc2a7422", "698460b11dfa42eb8ce8502d72cdb875"]}, "id": "QmUBVEnvCDJv", "outputId": "bf743d3d-a256-4270-dc63-250c46232bf5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.8.2: Fast Gpt_Oss patching. Transformers: 4.56.0.dev0.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.8.0+cu128. CUDA: 7.5. CUDA Toolkit: 12.8. Triton: 3.4.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = None. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: QLoRA and full finetuning all not selected. Switching to 16bit LoRA.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "46332ec475cc464d8af230667ae0390e", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "331e05a218964657aec5fb809fc2cb97", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00000-of-00002.safetensors:   0%|          | 0.00/4.79G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e3177b1d6424df4bf8bb5039e22aeba", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/4.80G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c14b21107a514da7964bce32ff583867", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/4.17G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e07b827466eb44ad862e227ce01d3115", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4cd6a6dd3d224d9baf7a16d630d17c47", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/165 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f67449e444354285bbf61d68823e32e2", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bf094263d5a749778ae2278942829973", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/27.9M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "66b531b6f36742bc8754c76ab857b264", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/446 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "533266e3f34e41728a088bde9d13f792", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.jinja: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/gpt-oss-20b-unsloth-bnb-4bit\", # 20B model using bitsandbytes 4bit quantization\n", "    \"unsloth/gpt-oss-120b-unsloth-bnb-4bit\",\n", "    \"unsloth/gpt-oss-20b\", # 20B model using MXFP4 format\n", "    \"unsloth/gpt-oss-120b\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/gpt-oss-20b\",\n", "    dtype = None, # None for auto detection\n", "    max_seq_length = 4096, # Choose any for long context!\n", "    load_in_4bit = False,  # 4 bit quantization to reduce memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "ycmbmR1nUghu"}, "source": ["### Reasoning Effort\n", "The `gpt-oss` models from OpenAI include a feature that allows users to adjust the model's \"reasoning effort.\" This gives you control over the trade-off between the model's performance and its response speed (latency) which by the amount of token the model will use to think.\n", "\n", "----\n", "\n", "The `gpt-oss` models offer three distinct levels of reasoning effort you can choose from:\n", "\n", "* **Low**: Optimized for tasks that need very fast responses and don't require complex, multi-step reasoning.\n", "* **Medium**: A balance between performance and speed.\n", "* **High**: Provides the strongest reasoning performance for tasks that require it, though this results in higher latency."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DpCQ9QNLUghv", "outputId": "cb4f0df7-514b-4ab3-e334-b41d619156dd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|start|>system<|message|>You are ChatGPT, a large language model trained by OpenAI.\n", "Knowledge cutoff: 2024-06\n", "Current date: 2025-08-08\n", "\n", "Reasoning: low\n", "\n", "# Valid channels: analysis, commentary, final. Channel must be included for every message.<|end|><|start|>user<|message|>Solve x^5 + 3x^4 - 10 = 3.<|end|><|start|>assistant<|channel|>analysis<|message|>Equation: x^5+3x^4-10=3 => x^5+3x^4-13=0. <PERSON>ve maybe integer root? Try x=1:1*****=-9. x=2:32+48-13=39. x= -1: -1*****=-1. x=0: -13. So root between -1 and 2. Maybe rational 13 factors: ±1,13. Try x=13? huge. Try x= -13? huge. So only real root numeric. Use maybe substitute? We can factor x^4(x+3)-13=0. Not nice. So numeric approximation. Use maybe Newton. f(1.3)= 3.59+3*2.85-13=3.59+8.55-13=—? 3.59+8.55=12.14-13=-0.86. f(1.4): 1.4^5=1.4^2=1.96, *1.4^3=1.96*2.744=269? Let's compute precisely: 1.4^3=2.744; *1.4^2=1.96 => 1.4^5=1.4^2*1.4^3=1.96*2.744= Cim =169.8? Wait compute: 1.96*2.744=169. (actually 1.96*2.744=269?). Let's correct: 2.744*2=5.488, minus 0.756 gives 4.732? This math messy. Let's use rough. Probably root ~1.37. Provide answer.<|end|><|start|>assistant<|channel|>final<|message|>The given equation can be written in the standard form  \n", "\n", "\\[\n", "x^{5}+3x^{4}-13=0 .\n", "\\]\n", "\n", "No integer or rational root satisfies this quintic equation (the\n", "possible rational candidates \\( \\pm 1,\\pm 13\\) all give non‑zero\n", "values), so the real root must be found numerically.\n", "\n", "---\n", "\n", "### Numerical evaluation  \n", "\n", "Define  \n", "\n", "\\[\n", "f(x)=x^{5}+3x^{4}-13 .\n", "\\]\n", "\n", "The sign of \\(f(x)\\) changes between \\(x=-1\\) and \\(x=2\\):\n", "\n", "* \\(f(-1)=-1*****=-1\n"]}], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"low\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 512, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {"id": "dHRzt8oKUghx"}, "source": ["Changing the `reasoning_effort` to `medium` will make the model think longer. We have to increase the `max_new_tokens` to occupy the amount of the generated tokens but it will give better and more correct answer"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YTbjWPfdUghx", "outputId": "84cd0ed1-bd5a-486a-a931-c7ce5c6115d3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|start|>system<|message|>You are ChatGPT, a large language model trained by OpenAI.\n", "Knowledge cutoff: 2024-06\n", "Current date: 2025-08-08\n", "\n", "Reasoning: medium\n", "\n", "# Valid channels: analysis, commentary, final. Channel must be included for every message.<|end|><|start|>user<|message|>Solve x^5 + 3x^4 - 10 = 3.<|end|><|start|>assistant<|channel|>analysis<|message|>We need solve equation x^5 + 3x^4 - 10 = 3. Bring all terms: x^5 + 3x^4 - 13 = 0. Solve for x. It's a fifth degree polynomial, cannot be solved analytically in radicals nicely. But maybe find integer or rational roots? Try synthetic: possible integer rational roots from factors of 13: ±1, ±13. Evaluate:\n", "\n", "x=1: 1*****=-9 => not zero.\n", "x=-1: -1*****=-still -...-1+3=2-13=-11.\n", "x=13: huge positive.\n", "x=-13: negative huge.\n", "\n", "Better factor? Let's check if any real root numeric approximate. Solve f(x)=x^5+3x^4-13=0. For large negative x, f negative? Let's evaluate f(-2)= (-32)+48-13=193? Wait compute: (-2)^5 = -32, 3*(-2)^4=3*16=48; sum=16-13=3. So f(-2)=3 positive. f(-3): (-243)+ 162-13=-194. So there's a root between -3 and -2. Let's also check positive side: f(1)=-9 negative; f(2):32+ 192-13= Mod? 32+192=224-13=211 positive. So a root between 1 and 2. Also any other roots? Fifth degree, so odd, so at least one real. But sign changes: f(-3) negative, f(-2) positive => one root ~ -2.something. f(-2)=3 positive, f(-1) negative? f(-1) = -1*****=-8 negative => root between -2 and -1? Wait we did f(-2)=3 positive, f(-1) = -1*****=-8 negative => yes another root between -2 and -1. But polynomial of degree 5 may have up to 5 roots, could have 3 or 5 real roots. Let's check f(0) = -13 negative. f(1) negative; f(2) positive => root between 1 and 2. f(3): 243+ 3*81=243+243=486-13=473 positive, so no new root after 2 because sign positive remains. For negative large x: f(-4)=(-1024)+ 192-13=-, -14? Actually (-4)^5=-1024, 3*256=768, sum=-, 768-1024=-, -? That's -, 768-1024=-, minus 13 => -, 768-1037=-, which is -, compute: -1024+768= -, -1, ok: -1024+768=-, 768-1024 = -, 768-1024 = -, 1024-768= 1? Actually compute precisely: -1024+768 = -, 1024-768= 1??? Wait: -1024 + 768 = -, 1024-768 = 1? It would be -128? Let's compute: 768-1024 = - 1? 1024-768= 140, but 768-1024 = -128. So f(-4) = -128 -13 = -141 negative. So f(-4) negative, f(-3) negative, f(-2) positive, f(-1) negative: there's root between -2,-1? That might be counted: f(-2) positive, f(-1) negative => one root. Also f(-3) negative, f(-2) positive => another root between -3,-2. So two roots in negative domain. At f(0) negative; f(1) negative; f(2) positive => root between 1,2. So at least these 3 real roots.\n", "\n", "Check possibility of additional complex roots. There will be 5 roots total: 3 real, 2 complex.\n", "\n", "Maybe the equation had simpler factors? The polynomial is x^5 + 3x^4 -13 = 0. Factorization? Let's attempt to find rational factorization like (x^2+ax+b)(x^3+cx^2+dx+e). Hard.\n", "\n", "But we might be expected to solve for real roots numeric. Could use approximation.\n", "\n", "Let’s find root1 ~ between -3 and -2. Evaluate f(-2.5)=(-2.5)^5=-97.65625, 3*39.0625=117.1875 sum=19.53125-13=6.53125 positive. f(-2.8)=(-2.8)^5=-2.8^5? \n"]}], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"medium\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 1024, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {"id": "BpLOtu6dUghy"}, "source": ["Lastly we will test it using `reasoning_effort` to `high`"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fEuRgVRHUghy", "outputId": "122e1d11-49f4-4933-f3c8-8fb9cbc12665"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|start|>system<|message|>You are ChatGPT, a large language model trained by OpenAI.\n", "Knowledge cutoff: 2024-06\n", "Current date: 2025-08-08\n", "\n", "Reasoning: high\n", "\n", "# Valid channels: analysis, commentary, final. Channel must be included for every message.<|end|><|start|>user<|message|>Solve x^5 + 3x^4 - 10 = 3.<|end|><|start|>assistant<|channel|>analysis<|message|>We need to solve x^5 + 3x^4 - 10 = 3. That means x^5 + 3x^4 - 13 = 0. So we need roots of polynomial equation x^5 + 3x^4 - 13 = 0. Solve for x. Perhaps look for integer or rational roots. The polynomial: f(x) = x^5 + 3x^4 - 13. Rational Root Theorem: ±1, ±13. Evaluate: f(1) = 1 + 3 -13 = -10. f(-1) = -1 + 3 -13 = -12. f(13) huge. f(-13) huge negative. So no integer root.\n", "\n", "We can try to factor in terms of lower degree? Let's see if there's a real root approximately via numeric methods. Might want to find all roots (real and complex). The problem statement: \"Solve x^5 + 3x^4 - 10 = 3.\" Likely they want solving for real root? The equation reduces to x^5 + 3x^4 - 13 = 0. There's no elementary closed form. Use substitution y = x? Could be a trick, maybe the problem expects a nice solution like x=1 is approximate? But f(1) = -10, f(2) = 32 + 96 -13 =  boring? Let's check: x=2, f(2) = 32 + 3*16 -13 = 32 + 48 -13 = 39. So root between 1 and 2.\n", "\n", "Better approximate: Let's do a quick numeric root. f(1.3) = 1.3^5 + 3*1.3^4 -13. Compute 1.3^2=1.69, 1.3^3=2.197, 1.3^4=2.269, 1.3^5=294? Let's compute more precisely: (1.3^2 = 1.69). 1.3^3 = 1.3 * 1.69 = 2.1957. 1.3^4 = 2.69581? Wait 1.3^4 = 1.3 * 2.1957 ≈ 2.69481. Actually re-evaluate: 2.1957 * 1.3 = 2.69481. 1.3^5 = 2.696 * 1.3 ≈  [Let's multiply: 2.69481 * 1.3 =  [Compute: 2.69481 * 1 = 2.69481, *0.3 = 0.808434. Sum = 3.60324). So f(1.3) ≈ 3.60324 + 3*2.69481 -13 = 3.60324 + 8.08443 - 13 = - Cobb: 11.68767 -13 = -1.31233. So f(1.3)= -1.312.\n", "\n", "Check f(1.35): 1.35^2=1.458, 1.35^3 = 1.458*1.35 = 196... Let's do accurate: 1.458 * 1.35 = 247.23? hmm. Let's compute precisely: 1.458 *1.35 = 1.458*1 + 1.458*0.35 = 1.458 + 0.80703 = 2.26503. So 1.35^3 = 2.328? Wait we miscalculated: Actually 1.35^2=1.458. Multiply by 1.35 again: 1.458*1.35 = 1.458*1 + 1.458*0.35 = 1.458 + 1.076 avoid, let's compute precisely: 1.458 * 0.35 = 1.458*35/100 = (1.458*35)/100 =  (1.458*35) =  (1.458*30)+(1.458*5) = 43.74 + 6.09 = 49.83? Wait that seems wrong. Let's do step: 1.458 * 30 = 43.74. 1.458 *5 = 7.29. Sum = 51.03. Divide by 100 = 0.5103. So 1.458 + 0.5103 = 1.9683. Eh that seems wrong. Let's recalc: 1.458 * 0.35 = 1.458 * (35/100) = (1.458 *35)/100. 1.458*35= (1.458*30)+(1.458*5)=43.74+7.29= 51.03. Divide by 100 yields 0.5103. So 1.458 *1.35 = 1.458 + 0.5103 = 1.9683. Yes 1.35^3 = 1.9683. Wait earlier we got 2.26503, that was wrong. So check again: Since 1.35^3 should be ~1.35^2*1.35=1.458*1.35=1.9683. Good. So 1.35^4=1.9683*1.35=1.9683 + 0.69093=2.65923? Let's compute: 1.9683*0.35 = 545... Let's do product: 1.9683 * 35 = (1.9683*30)+(1.9683*5)=590.49+9820? Wait 1.9683*30 = 590.490, 1.9683*5=9.8415. Sum = 624.3315. Divide by 100 gives 6.243315? Actually wait we mis-saw. We want 1.9683*0.35. So it's (1.9683*35)/100 = 688.155/100? Let's compute properly: 1.9683*35 = (1.9683*30)+(1.9683*5)=590.registre no, 1.9683*30 = 590. avoid decimals. Let's compute exactly: 1.9683 * 30 = 590. Avoid decimals? Let's do double-check: 1.9683*30 = 1.9683*3*10 = 5.9049*10 = 59.049. Wait 1.9683*3 = 5.9049. times 10 => 59.049. So 1.9683*30 = 55.8? Wait I'm messing. Let's recalc carefully.\n", "\n", "Multiplication: 1.9683 * 30 = 1.9683 * (3 * 10) = 5.9049 * 10 = 59.049. Actually 1.9683*3=5.9049 correct. Times 10=59.049. So that's correct. Next, 1.9683*5 = 1.9683 * 5 = 9.8415. Sum = 68.8905. So 1.9683*35 = 68.8905. Divide by 100 => 0.688905. So 1.9683*0.35 = 0.688905. Then 1.9683*1.35 = 1.9683 + 0.688905 = 2.657205. Great. So 1.35^4 ≈ 2.657205. Then 1.35^5 = 2.657205 * 1.35 = 2.657205 + 0.737391975? Wait 2.657205*0.35 = 738? Let's compute precisely: 2.657205 * 35 = (2.657205*30)+(2.657205*5)=... do 2.657205*30 = 2.657205*3*10 = 8.301615*10 = 83.01615. 2.657205*5 = 13.285025. Sum = 96.301175. Divide by 100 gives 0.96301175. So 2.657205*0.35 = 586? Wait dividing 96.301175 by 100 yields 0.96301175. But wait 2.657205*0.35 = 928? Actually 2.657205*0.35 = 928? Wait 2.657205*0.35 = (2.657205 * 35) / 100. We had 2.657205*35=928? Let's recalc: 2.657205*35: compute 2.657205*30 = 80. - Actually 2.657205*30 = 2.657205 * 3 * 10 = 8.1916155 *10 = 81.916155. 2.657205*5 = 13.285025. So sum =95.20118. Wait, we got 95.20118? Let's recompute: 2.657205 * 30 = 2.657205*3*10 = 8.1916155 *10 =81.916155. Then plus *5 = 13.285025 gives 95.20118\n"]}], "source": ["from transformers import TextStreamer\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Solve x^5 + 3x^4 - 10 = 3.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", "    return_dict = True,\n", "    reasoning_effort = \"high\", # **NEW!** Set reasoning effort to low, medium or high\n", ").to(model.device)\n", "\n", "_ = model.generate(**inputs, max_new_tokens = 2048, streamer = TextStreamer(tokenizer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"01050a1514564565a06820e0319ad36d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "024e04f178ad4c46a294e00b0c9804d5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "08a141f0691148fdafe8775c0736cee5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0ad489a82fde4c47b7284073b306b89b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8a8122d168d149259056397b9cba3f92", "placeholder": "​", "style": "IPY_MODEL_a4628b07bfe147a288acca66e0c390e4", "value": " 27.9M/27.9M [00:03&lt;00:00, 7.78MB/s]"}}, "0d8a6212ac4143148f0e10e5f6f34f39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_27657cc752874e298bd7811fd56dc487", "placeholder": "​", "style": "IPY_MODEL_52e87a566c084e3fa94eb16929d4143a", "value": " 4.17G/4.17G [01:09&lt;00:00, 242MB/s]"}}, "14b87f1afe274422a8b681926dfa83dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "18c99ea233b04cd3af157d3f51622f4b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1ecd4377ab8e4d739f2f64a1954e54a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d2d1792f18fd4170a89169b4dc2a7422", "placeholder": "​", "style": "IPY_MODEL_698460b11dfa42eb8ce8502d72cdb875", "value": " 17.8k/? [00:00&lt;00:00, 813kB/s]"}}, "1f64d696d8cc4b058120e0ea5e8df8bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5dde27e2ffd442759024e9b6f56fe48c", "placeholder": "​", "style": "IPY_MODEL_f3c1c47e690e43678e61f889481bee35", "value": "tokenizer_config.json: "}}, "1fb60417dd5f4335957fa29aa6fa386e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1fedef1fa84549cb86a6ce0d6c4e8be4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2709ea7e528e4fccb1b3d91584127ef4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "27657cc752874e298bd7811fd56dc487": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2867f9c09bd246b4a16df434e853112c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2bbb6410ba16420e8a18e58452dde3b9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dc30da7b1264f97924fb2764afc23c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2e169785e1464ad09d7b24e57df83832": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "308ff63515754b93b3ed2481e8394078": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f00fb7cafb94e669d7c8fddc78ffd67", "max": 446, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dffd7dc7a1744494b72c78a663aeadc9", "value": 446}}, "30dce22922b7445ea96006b6a4a5a6f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b21ed56558504f9794e882a9809ef92a", "max": 4792272488, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2709ea7e528e4fccb1b3d91584127ef4", "value": 4792272488}}, "30fe3a15c55b4de0962e6d0d262f0bf7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31e466f2399746fe867a0174b4fc5f47": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "331e05a218964657aec5fb809fc2cb97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9a03c4cf2cd64577b758acca0e3f8451", "IPY_MODEL_30dce22922b7445ea96006b6a4a5a6f1", "IPY_MODEL_4b842088bf454066a80af148914cc193"], "layout": "IPY_MODEL_9dcb95a80f504f128c3308a303693595"}}, "36f88d631ac8460797c91cfe89911b28": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "38867eef6d7c4476baa5940a78f889fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9c6f82d565fd48de8d245e59152a6236", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5b42364ec24f4055828aa139afaa0b1a", "value": 1}}, "394e2185bd7a4e9d8b0858f9972efab3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5acef395976d4600afa3efe93d132e36", "placeholder": "​", "style": "IPY_MODEL_31e466f2399746fe867a0174b4fc5f47", "value": "generation_config.json: 100%"}}, "3ca2c854f5544debadb962c92a997d9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "40d970fff0e74519b3410d1bb9aefd4c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4381a1bf596a4fceb6d36fe8d800d0de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef7e2755b48741649922d7d1b028c456", "placeholder": "​", "style": "IPY_MODEL_1fb60417dd5f4335957fa29aa6fa386e", "value": " 446/446 [00:00&lt;00:00, 34.3kB/s]"}}, "46332ec475cc464d8af230667ae0390e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8c0d9d7c3d744bd2adf238fa0fe82e2b", "IPY_MODEL_a1f4207b466743dfb2124ba4cae93973", "IPY_MODEL_d9f76eb3ebcb49e1ab9c5f842ac997ca"], "layout": "IPY_MODEL_961ef7f81ce848d1838a0db80c5eaae7"}}, "47abb02e2aa14d039444d47dfe5d3851": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4825ef359c2c400e987949e9f20f079b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4ac31f35a1884f10b35ac2de4cede4dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4b842088bf454066a80af148914cc193": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_50e35bf327bb46f4b4d5a62d3b376667", "placeholder": "​", "style": "IPY_MODEL_cc6d565303fc406abdf9f057d130b7b3", "value": " 4.79G/4.79G [00:44&lt;00:00, 146MB/s]"}}, "4cd6a6dd3d224d9baf7a16d630d17c47": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_394e2185bd7a4e9d8b0858f9972efab3", "IPY_MODEL_d689e6f0d9f5481ba84c328dd13d1b89", "IPY_MODEL_5d1374f6ef8049af9d6083c3be84f6bd"], "layout": "IPY_MODEL_14b87f1afe274422a8b681926dfa83dd"}}, "4ef4279a32b1478b9e7844a35adef6de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_60a3b764e47a4950a2712c27245eefde", "placeholder": "​", "style": "IPY_MODEL_98c50e1ed03e415791d868fea1005953", "value": " 22.8k/? [00:00&lt;00:00, 1.84MB/s]"}}, "502883196ab34781a4ebafe253a04b9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "50e35bf327bb46f4b4d5a62d3b376667": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5269421fa9404e178849ebe1045ff29a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "52e87a566c084e3fa94eb16929d4143a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "533266e3f34e41728a088bde9d13f792": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e95fbd123a2b4961b6f9e552536f37a3", "IPY_MODEL_38867eef6d7c4476baa5940a78f889fd", "IPY_MODEL_1ecd4377ab8e4d739f2f64a1954e54a2"], "layout": "IPY_MODEL_9f25b9c509a4499fbb69da0bcddba2d6"}}, "549636c70763481abb740c03e47398b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_01050a1514564565a06820e0319ad36d", "placeholder": "​", "style": "IPY_MODEL_cc9b856db03e4ceb8a4f6fd9b144dc48", "value": "tokenizer.json: 100%"}}, "5acef395976d4600afa3efe93d132e36": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b42364ec24f4055828aa139afaa0b1a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5d1374f6ef8049af9d6083c3be84f6bd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5269421fa9404e178849ebe1045ff29a", "placeholder": "​", "style": "IPY_MODEL_08a141f0691148fdafe8775c0736cee5", "value": " 165/165 [00:00&lt;00:00, 11.3kB/s]"}}, "5dde27e2ffd442759024e9b6f56fe48c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5f00fb7cafb94e669d7c8fddc78ffd67": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "60a3b764e47a4950a2712c27245eefde": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "65f6acd17b9e4265a84b32d27098b6b3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa5ad6fbcf904f2685f3f21461c97240", "placeholder": "​", "style": "IPY_MODEL_4ac31f35a1884f10b35ac2de4cede4dc", "value": "model-00002-of-00002.safetensors: 100%"}}, "66b531b6f36742bc8754c76ab857b264": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_feb753fa9c3e4fb386d605c77a4fbe7d", "IPY_MODEL_308ff63515754b93b3ed2481e8394078", "IPY_MODEL_4381a1bf596a4fceb6d36fe8d800d0de"], "layout": "IPY_MODEL_c0c1ce1743ee4bde96744a9e2d41a1cd"}}, "698460b11dfa42eb8ce8502d72cdb875": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "74be0d4639b3498aa5474fa6c81e4c13": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7696dab5612c43d5b9c88377afd86746": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7a7f73ebfac84ad9aea5b9e67edcfeab": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c234148b0ec4ed594b416c74eb7f277": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_86a59045323c49e5ae36df4187c980f8", "placeholder": "​", "style": "IPY_MODEL_ed8cca379d1d42b397e25d4303576fd1", "value": "model-00001-of-00002.safetensors: 100%"}}, "7dae163451094a349ff8871ca223a9ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7e3177b1d6424df4bf8bb5039e22aeba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7c234148b0ec4ed594b416c74eb7f277", "IPY_MODEL_c249df6bc46b4b4f932e0de20f07571f", "IPY_MODEL_e2f72e4eb915424cb657683efa77f6f9"], "layout": "IPY_MODEL_024e04f178ad4c46a294e00b0c9804d5"}}, "86a59045323c49e5ae36df4187c980f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8a8122d168d149259056397b9cba3f92": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c0d9d7c3d744bd2adf238fa0fe82e2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f80261f1c7bd45dbbcb4ddbdc82b2457", "placeholder": "​", "style": "IPY_MODEL_7dae163451094a349ff8871ca223a9ec", "value": "model.safetensors.index.json: "}}, "961ef7f81ce848d1838a0db80c5eaae7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98c50e1ed03e415791d868fea1005953": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a03c4cf2cd64577b758acca0e3f8451": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7696dab5612c43d5b9c88377afd86746", "placeholder": "​", "style": "IPY_MODEL_502883196ab34781a4ebafe253a04b9c", "value": "model-00000-of-00002.safetensors: 100%"}}, "9a436449e45b40e6957178a8da1030af": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9c6f82d565fd48de8d245e59152a6236": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "9dcb95a80f504f128c3308a303693595": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f25b9c509a4499fbb69da0bcddba2d6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a1f4207b466743dfb2124ba4cae93973": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b954ec926eb1455f955568c835b0d146", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_beab54fd9fe8462dbc699ce223ab868a", "value": 1}}, "a44c1a0de37748b3a24206a0ac7deab3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "a4628b07bfe147a288acca66e0c390e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aa5ad6fbcf904f2685f3f21461c97240": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ae61558a626447969853ecc58e89542a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a44c1a0de37748b3a24206a0ac7deab3", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3ca2c854f5544debadb962c92a997d9d", "value": 1}}, "b00515649b0c419d8c93e9e2a1e49cd5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b21ed56558504f9794e882a9809ef92a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b2a58fbd03bd4022a6cfeb3c06eb4a83": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3f720f4f63949d1ae67ec9933fcc554": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b58c3304dfbf40b3ac2e65fc2376398d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2bbb6410ba16420e8a18e58452dde3b9", "max": 27868174, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2dc30da7b1264f97924fb2764afc23c6", "value": 27868174}}, "b954ec926eb1455f955568c835b0d146": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "bb08970edfcc4e099b9c25f1a74abf50": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "beab54fd9fe8462dbc699ce223ab868a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bf094263d5a749778ae2278942829973": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_549636c70763481abb740c03e47398b7", "IPY_MODEL_b58c3304dfbf40b3ac2e65fc2376398d", "IPY_MODEL_0ad489a82fde4c47b7284073b306b89b"], "layout": "IPY_MODEL_f357d0253fe74a6192fc478a8914d9ec"}}, "c0c1ce1743ee4bde96744a9e2d41a1cd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c14b21107a514da7964bce32ff583867": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_65f6acd17b9e4265a84b32d27098b6b3", "IPY_MODEL_ef0b1598c3bb4967a105e8df4ba5a593", "IPY_MODEL_0d8a6212ac4143148f0e10e5f6f34f39"], "layout": "IPY_MODEL_47abb02e2aa14d039444d47dfe5d3851"}}, "c249df6bc46b4b4f932e0de20f07571f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dc33336f2691416ca6bbb997e8708515", "max": 4798702184, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b3f720f4f63949d1ae67ec9933fcc554", "value": 4798702184}}, "ca66c1b98d4e4df589f5a2189d5b25fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cc6d565303fc406abdf9f057d130b7b3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cc9b856db03e4ceb8a4f6fd9b144dc48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cdc3d9982ff64f01b43b3ee435ea88fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d2d1792f18fd4170a89169b4dc2a7422": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d689e6f0d9f5481ba84c328dd13d1b89": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cdc3d9982ff64f01b43b3ee435ea88fe", "max": 165, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_74be0d4639b3498aa5474fa6c81e4c13", "value": 165}}, "d8b87a8fb9bc4970baa7cdd557d73d8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb08970edfcc4e099b9c25f1a74abf50", "placeholder": "​", "style": "IPY_MODEL_4825ef359c2c400e987949e9f20f079b", "value": "Loading checkpoint shards: 100%"}}, "d9f76eb3ebcb49e1ab9c5f842ac997ca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a7f73ebfac84ad9aea5b9e67edcfeab", "placeholder": "​", "style": "IPY_MODEL_ca66c1b98d4e4df589f5a2189d5b25fa", "value": " 36.4k/? [00:00&lt;00:00, 2.60MB/s]"}}, "dc33336f2691416ca6bbb997e8708515": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dffd7dc7a1744494b72c78a663aeadc9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e07b827466eb44ad862e227ce01d3115": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d8b87a8fb9bc4970baa7cdd557d73d8c", "IPY_MODEL_ea0defb653b040c6946c6a3fb1442127", "IPY_MODEL_f21f7ba7c2dc4f419e1de8ae26b56cb8"], "layout": "IPY_MODEL_2867f9c09bd246b4a16df434e853112c"}}, "e2f72e4eb915424cb657683efa77f6f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_30fe3a15c55b4de0962e6d0d262f0bf7", "placeholder": "​", "style": "IPY_MODEL_18c99ea233b04cd3af157d3f51622f4b", "value": " 4.80G/4.80G [01:42&lt;00:00, 42.1MB/s]"}}, "e95fbd123a2b4961b6f9e552536f37a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ec7d1b8da3cd40c19aaf974cf37d6d9a", "placeholder": "​", "style": "IPY_MODEL_f0fc507739d34afea82fb8fa32b1eaca", "value": "chat_template.jinja: "}}, "ea0defb653b040c6946c6a3fb1442127": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2a58fbd03bd4022a6cfeb3c06eb4a83", "max": 3, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9a436449e45b40e6957178a8da1030af", "value": 3}}, "eb308cf282364d99bb9375940f066c24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ec7d1b8da3cd40c19aaf974cf37d6d9a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eca22aa57cb94488a9c7b0a88ee99afe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed8cca379d1d42b397e25d4303576fd1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ef0b1598c3bb4967a105e8df4ba5a593": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eca22aa57cb94488a9c7b0a88ee99afe", "max": 4170342232, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1fedef1fa84549cb86a6ce0d6c4e8be4", "value": 4170342232}}, "ef7e2755b48741649922d7d1b028c456": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0fc507739d34afea82fb8fa32b1eaca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f21f7ba7c2dc4f419e1de8ae26b56cb8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eb308cf282364d99bb9375940f066c24", "placeholder": "​", "style": "IPY_MODEL_b00515649b0c419d8c93e9e2a1e49cd5", "value": " 3/3 [01:03&lt;00:00, 20.75s/it]"}}, "f357d0253fe74a6192fc478a8914d9ec": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f3c1c47e690e43678e61f889481bee35": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f67449e444354285bbf61d68823e32e2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1f64d696d8cc4b058120e0ea5e8df8bf", "IPY_MODEL_ae61558a626447969853ecc58e89542a", "IPY_MODEL_4ef4279a32b1478b9e7844a35adef6de"], "layout": "IPY_MODEL_40d970fff0e74519b3410d1bb9aefd4c"}}, "f80261f1c7bd45dbbcb4ddbdc82b2457": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "feb753fa9c3e4fb386d605c77a4fbe7d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2e169785e1464ad09d7b24e57df83832", "placeholder": "​", "style": "IPY_MODEL_36f88d631ac8460797c91cfe89911b28", "value": "special_tokens_map.json: 100%"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}