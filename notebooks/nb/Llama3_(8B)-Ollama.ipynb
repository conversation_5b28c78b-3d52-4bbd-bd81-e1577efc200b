{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth\nelse:\n    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1,<4.0.0\" \"huggingface_hub>=0.34.0\" hf_transfer\n    !pip install --no-deps unsloth"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 300, "referenced_widgets": ["b03af9d3d3074c9fb43cc45d456b8e30", "4aab9fa7e38a49e59f87515e7bf415ee", "cba1bb5319c04c4c8adbc2304af6dde7", "af205ba151054c5fa8a5761b24dc8445", "45d8edc7203e4d2081170777d5eda422", "86f7a51468b248c8bb32c184f7cc4a69", "43efda3bc6a5496aab27c30c99577825", "0d2d931811914399b33af27d741cdbd2", "7190e73a16b34eaabf2e3fc00f17246e", "f9031d9400464ce4a500bd5b0b1b1fc7", "33f7cdb4b1484ff08d5882e38ed4ff33", "6f6d146e3dc1428682d6f9c193f8bf19", "6919b1082780411aa9310ceb152ea6e0", "8f9e359dcc0d4e4495eaa1f3920e803b", "8a41d23dbbdb44f4aab5f453c4be58a4", "f06db96ad2414b679cc0b55f25ff16db", "99e3b13809ca4873a44189f298084c59", "701f88ce88074492a3a8b3470e8b22e7", "b91f7a6e50b5463bb560e45a71ee1998", "f7ef65c1668c498bb72ba3411301e116", "3d8e6f188bf44dba981e445d8bea3402", "a615710d2f72406f94586cf44320d1d8", "5064b0b9c6d7462190bcd6059a6475f4", "19474c3fd67d4a0a9071442553eea424", "cdc0128cc0ea40bc949df6412d179b78", "b28fd98a29b04febac41b3f0169c72a1", "e0d4b6fedf2245e1b926c36bc7d792e6", "a51f8804e64e41f4b5f403bb48f97739", "7d182abc791a4529a1d022c73f147743", "b13a48803a164e7caecfb06e2bde1556", "9bc07321be954d109b8f27d4b8f181dd", "6c96874fffb648708eba436836dec3c7", "8c321f9d450c45259553f244cea495ce", "665de9c511234f50b95f019e30553647", "7833e5ff39994bd8af56ab74ca5a6282", "05e9011d5fd44a219ef86ce93b400cdb", "9009e3ae003b4189bbfc8f6d20e425db", "feddd2b94ff640019168f085886042ca", "66d602f02ee94a9fb9bb3ef6ebb157a2", "bad159b09d454000bba39dba607b8e60", "e16a04efe0564f13bda5d36487b79053", "9b918d8d5f674be88e05c0f3fc365b77", "1c514cd5db254fd887496cfcea29b203", "4f78fb604b944c8da3f8f94a84779533", "571fbb27971e4af0b958591a502e1249", "d792fa2e810a4aec841b96aed03f22e5", "5a927d80c3d841768f8a32abc2b3da4e", "60a2e5edb2424b91b551abaf8607d225", "aa461878d707464ea5728e857199fc1b", "c2ec9b62f02b4813ab4d7a7c4609ec57", "7949c2ae13f44c609de332549305efd4", "3f30c80db3c54588b79e9352f57e783d", "6e059c44581046c6b7ac232fac94c6a4", "6b2f0019c68b4ca385fc9e9641e98006", "3bfbb892f9fe4c7baec8518c5fc27fd3"]}, "id": "QmUBVEnvCDJv", "outputId": "c36582a5-ee6c-483c-8a5b-9ec43430d2af"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "==((====))==  Unsloth 2024.9.post4: Fast Llama patching. Transformers = 4.44.2.\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.4.1+cu121. CUDA = 7.5. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.28.post1. FA2 = False]\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b03af9d3d3074c9fb43cc45d456b8e30", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/5.70G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6f6d146e3dc1428682d6f9c193f8bf19", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/198 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5064b0b9c6d7462190bcd6059a6475f4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/50.6k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "665de9c511234f50b95f019e30553647", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/9.09M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "571fbb27971e4af0b958591a502e1249", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-v0.3-bnb-4bit\",      # New Mistral v3 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/llama-3-8b-bnb-4bit\",           # Llama-3 15 trillion tokens model 2x faster!\n", "    \"unsloth/llama-3-8b-Instruct-bnb-4bit\",\n", "    \"unsloth/llama-3-70b-bnb-4bit\",\n", "    \"unsloth/Phi-3-mini-4k-instruct\",        # Phi-3 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\",             # Gemma 2.2x faster!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/llama-3-8b-bnb-4bit\",\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "b2a0373b-a721-4170-d7d3-8e6f6a33b0d5"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.9.post4 patched 32 layers with 32 QKV layers, 32 O layers and 32 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the Alpaca dataset from [vicgalle](https://huggingface.co/datasets/vicgalle/alpaca-gpt4), which is a version of 52K of the original [Alpaca dataset](https://crfm.stanford.edu/2023/03/13/alpaca.html) generated from GPT4. You can replace this code section with your own data prep."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 159, "referenced_widgets": ["e7c4177a28d34f6da992178c8cb9313c", "ffe387f8ca604781a7cd92a44c6dd508", "a4df6bc79d93441283021cad1e4535e3", "c4df80b7320a4022aa91a8b3f0c49827", "dd0bd629b2384bd4aa4da791c3d6d254", "db6e9ea5d4ac4ceeb6216973ab04fff2", "cedf19b766684a1f8b2e40d1f97e89a4", "b522d2d378de44329f3224533318eb25", "a10a1f9bf41540ddb33c2b8dc181947e", "d760752518e841a2a03f68914e21602f", "90323c950496418d8540e5e9dfc7db31", "2a27e2ac34184a1e88652c377c516d07", "848f2ab429a2449daa889bc5daf315d4", "8f2008bd1c9e4979a69151c7056dbd68", "6b01f9604205455f8d887d5f216d845d", "830101e5c8f7425886318bf7bf368128", "a63b105990504c248cf4eebc42faaba8", "2375ae0eb6c541b086ce027a7368e86a", "09d4965cff9a457e91bc2ed788b37d98", "b5e79d185ecf496890db8818b0b60306", "2f8928b10d9a44e6b2c03fee74973d61", "5e8a3854fa4445b3b3a09a4db9a59de4", "19f7599c3e474401aa66b205209c5b05", "88f835bb9ef046ccbf32c41db3d752fc", "c22e35a67d6546868f601f486faaa09f", "ce52ffbce8164fd3b6f05f8e2f157bf8", "9f1f657d2e3c47ffabe49528cb850edc", "2ef094bd791b45169fb3de9eea3858f7", "29b8a584ae494edbaa5d1573bd2cc405", "63b2683594c44b858c937349019c6d0d", "cead1d73fc484c12ab7b7e4dcbfed58b", "40ec2d13fcc14995b86d2f28a7ae9f37", "25c75bff6c7d40148ef6311860013ae8"]}, "id": "HvOPfPnet76H", "outputId": "8f60bbe3-d43c-441f-99b3-3bb658aadb8e"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e7c4177a28d34f6da992178c8cb9313c", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/3.38k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a27e2ac34184a1e88652c377c516d07", "version_major": 2, "version_minor": 0}, "text/plain": ["(…)-00000-of-00001-6ef3991c06080e14.parquet:   0%|          | 0.00/48.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "19f7599c3e474401aa66b205209c5b05", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["['instruction', 'input', 'output', 'text']\n"]}], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"vicgalle/alpaca-gpt4\", split=\"train\")\n", "print(dataset.column_names)"]}, {"cell_type": "markdown", "metadata": {"id": "xg4_dG-m0Cz4"}, "source": ["One issue is this dataset has multiple columns. For `Ollama` and `llama.cpp` to function like a custom `ChatGPT` Chatbot, we must only have 2 columns - an `instruction` and an `output` column."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DTQR4jrDMcJf", "outputId": "d8b592db-cdf2-49e6-990d-3a493b0770ce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['instruction', 'input', 'output', 'text']\n"]}], "source": ["print(dataset.column_names)"]}, {"cell_type": "markdown", "metadata": {"id": "MwEbRFl0Mf3E"}, "source": ["To solve this, we shall do the following:\n", "* Merge all columns into 1 instruction prompt.\n", "* Remember LLMs are text predictors, so we can customize the instruction to anything we like!\n", "* Use the `to_sharegpt` function to do this column merging process!\n", "\n", "For example below in our [Titanic CSV finetuning notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb), we merged multiple columns in 1 prompt:\n", "\n", "<img src=\"https://raw.githubusercontent.com/unslothai/unsloth/nightly/images/Merge.png\" height=\"100\">"]}, {"cell_type": "markdown", "metadata": {"id": "w61VJ7rQM8jT"}, "source": ["To merge multiple columns into 1, use `merged_prompt`.\n", "* Enclose all columns in curly braces `{}`.\n", "* Optional text must be enclused in `[[]]`. For example if the column \"Pclass\" is empty, the merging function will not show the text and skp this. This is useful for datasets with missing values.\n", "* You can select every column, or a few!\n", "* Select the output or target / prediction column in `output_column_name`. For the Alpaca dataset, this will be `output`.\n", "\n", "To make the finetune handle multiple turns (like in ChatGPT), we have to create a \"fake\" dataset with multiple turns - we use `conversation_extension` to randomnly select some conversations from the dataset, and pack them together into 1 conversation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 293, "referenced_widgets": ["4dac566fd3ee404a8308026caf52e165", "4ebfd9be5b684da9a98c007084813595", "1f8684abca0c45b4a8c36f854fc1e15b", "be28376dcf0a4540872cabee59e87a64", "50ec7aa2658e4cdf878dbd4c98660072", "4f808dffe6c24927b1b87b1319ccb311", "948f186726e340d18abc9ac134630fcb", "a843939563ee45299fdd3f32986462c5", "8d96f84cdd14438baf1a5c5e4badd284", "e829599ab414438f9b65c3dbe151ab2f", "520dd5d7080d46e29213ab6958131a04", "4a7639181756463fa0d2bdcddb0ee460", "2bba477ee49f425aac6be9a9e788c28b", "057825aa7df44d8eb9657aa8244c6a56", "bc1a5a549e4845b2a549c4a5a077b100", "22edcfad17e9407d84b0d169818bd86f", "b398a7d22a424d7d9ae67afdd95dcdcd", "bc84a9ff36f143248ea6c2181f68ae02", "1a501100f02d48bf8e6746ef3cd4047e", "b77913d6312d4608bdfa405fcaf16022", "5ad5ec145642450bb2b3c3b2e0dada54", "c5d04e56aaef4276b8da7b1d47eda54c", "886ccb525cb149dfb49be917882f4ae3", "43f6c10a2c3c478fba1a3784f75c6b84", "69c3b6e19c4d4abbb1e4a42a5be79e62", "d3e8cc7d6db94605ab85f3a41c03a046", "0bdef7784bb9486091ef72fa30911f17", "7474eb738ef64c8aa24c7dadcc296309", "3c186c44416447daaa58e288416b69a4", "18c1a70d11484b20b0c129afda607eec", "f90e7b0952a04982acc7b4e4128aa822", "4e76d402b4e4416e9b7e437af31ad66e", "12e4386de36246c28a70205ad43dc5d3", "4ef743732002497395f3c2215713eb3e", "44e1233f423e4fb7bc8e94c98449f524", "69cee937c2bb46528db052e775896eaa", "f9c775663b86483abcedd9beba15662b", "9a301b9b504543008aa9cdb0ec09cdc2", "97f798f38c9c4456ab6c5235d8af9e7d", "33a01d898ffc4a1a865b3c899df46981", "6ff4bb32f392421097f322b035208e6b", "e815c4a6522142068f09ed0abe3d5761", "45165ac4e08f4103815205cc37690726", "b1e8d77d5bc34e99824c873e55cc49a2", "5edc00f7efce452e82b713120068d5ce", "9a05e6b44b2147c99a54764f01910b30", "916ab8f1faf14632bdb3dead624eb21f", "de5c470b9d654008bb0032b5aa4329ce", "602377d8635c49118036fbb6817b3cee", "e660f7079e4346e499da1ac196c96980", "fa70fc9faa8a4580bf44e07f65e4d092", "3bd2b3b05e214b8b918cb37bf8b87c4b", "b23419db6d7b41af9769c4c7535d5145", "96aad118d29c4178b429612b2276ddfd", "8061d66b27d548ea86d7ef10d2d784e8", "18b7f26e9cc74aa7a3f01b7af539c4ba", "25397a2b86ef492c95c76b595f288e0d", "a79fd1eef2c543f9bef6ff4c78b7b9ac", "231d1e98fa704113bb70a4aef84ac30f", "e70183b7122c440eb0ad820a34ecd0b1", "36412dbe8646468dbc51dbac72ac305c", "70bb803085a2457092d4642a3baf8f54", "14c60f02caa441b7a5ce09ffd770f5a9", "ea6a29aacd804453863970f0e4bfb4ba", "1e6693fba36444749c5190ae984b7761", "5e1f0774a45141149ba3a9a62e2d50f1"]}, "id": "jZxeGSeX0CR8", "outputId": "6486785e-69cc-4434-bef0-f5360ab2ac1a"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4dac566fd3ee404a8308026caf52e165", "version_major": 2, "version_minor": 0}, "text/plain": ["Merging columns:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a7639181756463fa0d2bdcddb0ee460", "version_major": 2, "version_minor": 0}, "text/plain": ["Converting to ShareGPT:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "886ccb525cb149dfb49be917882f4ae3", "version_major": 2, "version_minor": 0}, "text/plain": ["Flattening the indices:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ef743732002497395f3c2215713eb3e", "version_major": 2, "version_minor": 0}, "text/plain": ["Flattening the indices:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5edc00f7efce452e82b713120068d5ce", "version_major": 2, "version_minor": 0}, "text/plain": ["Flattening the indices:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "18b7f26e9cc74aa7a3f01b7af539c4ba", "version_major": 2, "version_minor": 0}, "text/plain": ["Extending conversations:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import to_sharegpt\n", "\n", "dataset = to_sharegpt(\n", "    dataset,\n", "    merged_prompt=\"{instruction}[[\\nYour input is:\\n{input}]]\",\n", "    output_column_name=\"output\",\n", "    conversation_extension=3,  # Select more to handle longer conversations\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "1Kh90vpD1jYJ"}, "source": ["Finally use `standardize_sharegpt` to fix up the dataset!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["d08ffe495f9649cd99fd13e38fa09aa5", "e0a12ecf2f7b4af59ad6b9c6806cffd2", "2748bc11780947038af15b94cdc0565a", "39ac8df581254571bf15e52778564ad6", "32f125cf43cd40a4b232d80a4cd0594f", "0a3dd767c8284aa899195fb798a5da74", "05e65597d7804e8eaa92d0df28a03b95", "91b8e492c8e14eba9e9cd3c857e464ac", "2342b7593577476ebc15b5b0b7c93d37", "890767e37b054db5b6b2df4bb2ad351d", "e9c0e9a6545c49b9895f751b6c738974"]}, "id": "ZPwDXBvP1g8S", "outputId": "c400321c-0a47-47c3-d84c-e9ba524740a6"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d08ffe495f9649cd99fd13e38fa09aa5", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import standardize_sharegpt\n", "\n", "dataset = standardize_sharegpt(dataset)"]}, {"cell_type": "markdown", "metadata": {"id": "GThrcKACxTe2"}, "source": ["### Customizable Chat Templates\n", "\n", "You also need to specify a chat template. Previously, you could use the Alpaca format as shown below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MVBanRIJRAcQ"}, "outputs": [], "source": ["alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\""]}, {"cell_type": "markdown", "metadata": {"id": "VTzZ5oZrxkFz"}, "source": ["Now, you have to use `{INPUT}` for the instruction and `{OUTPUT}` for the response.\n", "\n", "We also allow you to use an optional `{SYSTEM}` field. This is useful for Ollama when you want to use a custom system prompt (also like in ChatGPT).\n", "\n", "You can also not put a `{SYSTEM}` field, and just put plain text.\n", "\n", "```python\n", "chat_template = \"\"\"{SYSTEM}\n", "USER: {INPUT}\n", "ASSISTANT: {OUTPUT}\"\"\"\n", "```\n", "\n", "Use below if you want to use the Llama-3 prompt format. You must use the `instruct` and not the `base` model if you use this!\n", "```python\n", "chat_template = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "{SYSTEM}<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{INPUT}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "{OUTPUT}<|eot_id|>\"\"\"\n", "```\n", "\n", "For the ChatML format:\n", "```python\n", "chat_template = \"\"\"<|im_start|>system\n", "{SYSTEM}<|im_end|>\n", "<|im_start|>user\n", "{INPUT}<|im_end|>\n", "<|im_start|>assistant\n", "{OUTPUT}<|im_end|>\"\"\"\n", "```"]}, {"cell_type": "markdown", "metadata": {"id": "EK-_ncj-RCNy"}, "source": ["The issue is the Alpaca format has 3 fields, whilst OpenAI style chatbots must only use 2 fields (instruction and response). That's why we used the `to_sharegpt` function to merge these columns into 1."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 67, "referenced_widgets": ["a1d70bbc33a64c23a63f773abc5e22e6", "84f9e22160d74ec498f99e9c93b0c8bc", "cb89bc91831b431bba8b1f1a14ac05ec", "669649c93c60494fac2bc7dfb4b43ac1", "c821f5cc39924fde9fc9d381747bc5d3", "1ce9fb4fb360443fbab1bc426b84e51e", "61e2f95ccaf84962a319224b652af287", "cbf4c274668c4feeb407501754eb7bf1", "9249cef6e8824525a1e183498d9d41ac", "c37f06b87a9b41ba957ac760aad0ea48", "9c9436e28d9c4cfdb9a251ce1ee4cb39"]}, "id": "JOGaZf1sdLlr", "outputId": "ca525f00-aa3c-4563-af31-420f90e62acd"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: We automatically added an EOS token to stop endless generations.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a1d70bbc33a64c23a63f773abc5e22e6", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["chat_template = \"\"\"Below are some instructions that describe some tasks. Write responses that appropriately complete each request.\n", "\n", "### Instruction:\n", "{INPUT}\n", "\n", "### Response:\n", "{OUTPUT}\"\"\"\n", "\n", "from unsloth import apply_chat_template\n", "\n", "dataset = apply_chat_template(\n", "    dataset,\n", "    tokenizer=tokenizer,\n", "    chat_template=chat_template,\n", "    # default_system_message = \"You are a helpful assistant\", << [OPTIONAL]\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 67, "referenced_widgets": ["dee51ee264e24b809be18f6d286ae874", "a9a39684291d4eb1b7312640feae1462", "d7435502d95f4355b37d2f3332b057f1", "2d15261428824594908bd697644d61ed", "b3a8a71922f341758e1adf0f951208bb", "7f107e2a1d274a2f9bec4acce979d2da", "0d5da7e017424aa0b7ff0d40abad1ecc", "4c4bc99385eb49dd95ad4b740b949e55", "f62d1a6521574459ae6fb1ebd71ad631", "62191c10c14c4728bf85a293d29d37ce", "ec56936c53984c85ade94df6ce443ad5"]}, "id": "95_Nn-89DhsL", "outputId": "27f7cc9d-8fd9-4111-ee2d-a5a434b30282"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dee51ee264e24b809be18f6d286ae874", "version_major": 2, "version_minor": 0}, "text/plain": ["Map (num_proc=2):   0%|          | 0/52002 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    packing = False, # Can make training 5x faster for short sequences.\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        max_steps = 60,\n", "        # num_train_epochs = 1, # For longer training runs!\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "623e558b-a1c9-425b-feff-4dcdbc381d0c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "5.613 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "fd7d79c2-68a7-4e3a-e567-adecc3b1be87"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 52,002 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 60\n", " \"-____-\"     Number of trainable parameters = 41,943,040\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 14:46, <PERSON>po<PERSON> 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.530300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.521200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.419800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.496600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.618100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.255100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.290100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.277400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.323600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.166000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.204200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.189000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.049100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.115200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.150700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.051400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.051000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.173100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.129600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>1.104200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>1.033500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.111600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.966300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>1.042500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.017800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.071600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.092100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.074600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.077500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.122200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>1.066400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>1.154400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.980800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>1.074700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>1.155100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>1.142200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>1.047400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>1.024000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.990800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>1.018900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>1.025600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>1.039400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>1.166000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>1.086500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>1.001300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>1.040000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>1.076200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.120800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>1.020300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.238000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>1.094100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>1.112800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>1.098000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.985200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>1.009800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.034200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>1.018500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>1.043500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.986100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>1.032200</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "38fed629-e855-4386-c150-8578e0a73a15"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["910.2379 seconds used for training.\n", "15.17 minutes used for training.\n", "Peak reserved memory = 8.361 GB.\n", "Peak reserved memory for training = 2.748 GB.\n", "Peak reserved memory % of max memory = 56.692 %.\n", "Peak reserved memory for training % of max memory = 18.633 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! <PERSON><PERSON><PERSON><PERSON> makes inference natively 2x faster as well! You should use prompts which are similar to the ones you had finetuned on, otherwise you might get bad results!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "40b5e499-1ad5-4907-a284-9416a36d010c"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The next number in the <PERSON><PERSON><PERSON><PERSON> sequence is 13.<|end_of_text|>\n"]}], "source": ["FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "messages = [                    # Change below!\n", "    {\"role\": \"user\", \"content\": \"Continue the <PERSON><PERSON><PERSON><PERSON> sequence! Your input is 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids, streamer = text_streamer, max_new_tokens = 128, pad_token_id = tokenizer.eos_token_id)"]}, {"cell_type": "markdown", "metadata": {"id": "CrSvZObor0lY"}, "source": ["Since we created an actual chatbot, you can also do longer conversations by manually adding alternating conversations between the user and assistant!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "JcbFUWEyQVaE", "outputId": "517ed3fe-009e-4ebf-d233-2883e943de82"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["France's tallest tower is called the Eiffel Tower.<|end_of_text|>\n"]}], "source": ["FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "messages = [                         # Change below!\n", "    {\"role\": \"user\",      \"content\": \"Continue the <PERSON><PERSON><PERSON><PERSON> sequence! Your input is 1, 1, 2, 3, 5, 8\"},\n", "    {\"role\": \"assistant\", \"content\": \"The <PERSON><PERSON><PERSON><PERSON> sequence continues as 13, 21, 34, 55 and 89.\"},\n", "    {\"role\": \"user\",      \"content\": \"What is France's tallest tower called?\"},\n", "]\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids, streamer = text_streamer, max_new_tokens = 128, pad_token_id = tokenizer.eos_token_id)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "587e0389-0044-47a2-f691-581d262ea95c"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKX_XKs_BNZR", "outputId": "98ec2273-2e01-4062-c576-1ffb7b3afdb0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The sequence 1, 1, 2, 3, 5, 8 is a special sequence known as the <PERSON><PERSON><PERSON><PERSON> sequence. The <PERSON><PERSON><PERSON><PERSON> sequence is a series of numbers where each number is the sum of the two previous numbers, starting with 0 and 1. In this case, the sequence is 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, and so on. The <PERSON><PERSON><PERSON><PERSON> sequence has many interesting properties and is widely studied in mathematics and computer science.<|end_of_text|>\n"]}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "pass\n", "\n", "messages = [                    # Change below!\n", "    {\"role\": \"user\", \"content\": \"Describe anything special about a sequence. Your input is 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "input_ids = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids, streamer = text_streamer, max_new_tokens = 128, pad_token_id = tokenizer.eos_token_id)"]}, {"cell_type": "markdown", "metadata": {"id": "QQMjaNrjsU5_"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yFfaXG0WsQuE"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "XOFzC441vCtq"}, "source": ["<a name=\"<PERSON><PERSON><PERSON>\"></a>\n", "### Ollama Support\n", "\n", "[Unsloth](https://github.com/unslothai/unsloth) now allows you to automatically finetune and create a [Modelfile](https://github.com/ollama/ollama/blob/main/docs/modelfile.md), and export to [Ollama](https://ollama.com/)! This makes finetuning much easier and provides a seamless workflow from `Unsloth` to `Ollama`!\n", "\n", "Let's first install `Ollama`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NUxcyP_UfeLl", "outputId": "69972ce0-9caf-41fd-b19a-fa058521990b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>> Installing ollama to /usr/local\n", ">>> Downloading Linux amd64 bundle\n", "############################################################################################# 100.0%\n", ">>> Creating ollama user...\n", ">>> Adding ollama user to video group...\n", ">>> Adding current user to ollama group...\n", ">>> Creating ollama systemd service...\n", "WARNING: Unable to detect NVIDIA/AMD GPU. Install lspci or lshw to automatically detect and install GPU dependencies.\n", ">>> The Ollama API is now available at 127.0.0.1:11434.\n", ">>> Install complete. Run \"ollama\" from the command line.\n"]}], "source": ["!curl -fsSL https://ollama.com/install.sh | sh"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["Next, we shall save the model to GGUF / llama.cpp\n", "\n", "We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "We also support saving to multiple GGUF options in a list fashion! This can speed things up by 10 minutes or more if you want multiple export formats!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FqfebeAdT073", "outputId": "9d2292eb-1e31-4c88-9b44-5371e4104abf"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: ##### The current model auto adds a BOS token.\n", "Unsloth: ##### Your chat template has a BOS token. We shall remove it temporarily.\n", "Unsloth: You have 1 CPUs. Using `safe_serialization` is 10x slower.\n", "We shall switch to Pytorch saving, which will take 3 minutes and not 30 minutes.\n", "To force `safe_serialization`, set it to `None` instead.\n", "Unsloth: <PERSON><PERSON>/Colab has limited disk space. We need to delete the downloaded\n", "model which will save 4-16GB of disk space, allowing you to save on Kaggle/Colab.\n", "Unsloth: Will remove a cached repo with size 5.7G\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Merging 4bit and LoRA weights to 16bit...\n", "Unsloth: Will use up to 5.49 out of 12.67 RAM for saving.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 47%|████▋     | 15/32 [00:01<00:01,  9.79it/s]We will save to Disk and not RAM now.\n", "100%|██████████| 32/32 [01:41<00:00,  3.18s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Saving tokenizer... Done.\n", "Unsloth: Saving model... This might take 5 minutes for Llama-7b...\n", "Unsloth: Saving model/pytorch_model-00001-of-00004.bin...\n", "Unsloth: Saving model/pytorch_model-00002-of-00004.bin...\n", "Unsloth: Saving model/pytorch_model-00003-of-00004.bin...\n", "Unsloth: Saving model/pytorch_model-00004-of-00004.bin...\n", "Done.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unsloth: Converting llama model. Can use fast conversion = False.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth: Conversion from QLoRA to GGUF information\n", "   \\\\   /|    [0] Installing llama.cpp will take 3 minutes.\n", "O^O/ \\_/ \\    [1] Converting HF to GGUF 16bits will take 3 minutes.\n", "\\        /    [2] Converting GGUF 16bits to ['q8_0'] will take 10 minutes each.\n", " \"-____-\"     In total, you will have to wait at least 16 minutes.\n", "\n", "Unsloth: [0] Installing llama.cpp. This will take 3 minutes...\n", "Unsloth: [1] Converting model at model into q8_0 GGUF format.\n", "The output location will be ./model/unsloth.Q8_0.gguf\n", "This will take 3 minutes...\n", "INFO:hf-to-gguf:Loading model: model\n", "INFO:gguf.gguf_writer:gguf: This GGUF file is for Little Endian only\n", "INFO:hf-to-gguf:Exporting model...\n", "INFO:hf-to-gguf:gguf: loading model weight map from 'pytorch_model.bin.index.json'\n", "INFO:hf-to-gguf:gguf: loading model part 'pytorch_model-00001-of-00004.bin'\n", "INFO:hf-to-gguf:token_embd.weight,           torch.float16 --> Q8_0, shape = {4096, 128256}\n", "INFO:hf-to-gguf:blk.0.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.0.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.0.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.0.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.0.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.0.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.0.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.0.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.0.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.1.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.1.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.1.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.1.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.1.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.1.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.1.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.1.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.1.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.2.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.2.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.2.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.2.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.2.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.2.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.2.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.2.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.2.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.3.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.3.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.3.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.3.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.3.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.3.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.3.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.3.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.3.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.4.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.4.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.4.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.4.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.4.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.4.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.4.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.4.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.4.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.5.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.5.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.5.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.5.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.5.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.5.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.5.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.5.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.5.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.6.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.6.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.6.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.6.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.6.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.6.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.6.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.6.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.6.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.7.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.7.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.7.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.7.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.7.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.7.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.7.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.7.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.7.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.8.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.8.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.8.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.8.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.8.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.8.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.8.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.8.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.8.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:gguf: loading model part 'pytorch_model-00002-of-00004.bin'\n", "INFO:hf-to-gguf:blk.9.attn_q.weight,         torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.9.attn_k.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.9.attn_v.weight,         torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.9.attn_output.weight,    torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.9.ffn_gate.weight,       torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.9.ffn_up.weight,         torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.9.ffn_down.weight,       torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.9.attn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.9.ffn_norm.weight,       torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.10.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.10.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.10.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.10.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.10.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.10.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.10.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.10.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.10.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.11.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.11.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.11.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.11.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.11.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.11.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.11.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.11.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.11.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.12.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.12.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.12.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.12.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.12.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.12.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.12.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.12.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.12.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.13.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.13.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.13.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.13.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.13.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.13.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.13.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.13.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.13.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.14.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.14.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.14.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.14.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.14.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.14.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.14.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.14.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.14.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.15.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.15.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.15.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.15.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.15.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.15.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.15.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.15.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.15.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.16.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.16.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.16.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.16.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.16.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.16.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.16.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.16.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.16.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.17.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.17.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.17.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.17.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.17.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.17.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.17.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.17.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.17.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.18.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.18.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.18.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.18.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.18.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.18.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.18.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.18.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.18.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.19.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.19.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.19.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.19.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.19.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.19.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.19.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.19.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.19.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.20.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.20.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.20.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.20.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.20.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:gguf: loading model part 'pytorch_model-00003-of-00004.bin'\n", "INFO:hf-to-gguf:blk.20.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.20.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.20.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.20.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.21.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.21.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.21.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.21.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.21.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.21.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.21.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.21.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.21.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.22.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.22.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.22.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.22.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.22.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.22.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.22.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.22.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.22.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.23.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.23.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.23.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.23.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.23.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.23.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.23.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.23.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.23.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.24.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.24.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.24.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.24.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.24.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.24.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.24.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.24.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.24.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.25.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.25.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.25.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.25.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.25.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.25.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.25.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.25.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.25.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.26.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.26.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.26.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.26.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.26.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.26.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.26.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.26.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.26.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.27.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.27.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.27.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.27.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.27.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.27.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.27.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.27.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.27.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.28.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.28.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.28.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.28.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.28.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.28.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.28.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.28.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.28.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.29.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.29.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.29.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.29.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.29.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.29.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.29.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.29.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.29.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.30.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.30.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.30.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.30.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.30.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.30.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.30.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.30.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.30.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.31.attn_q.weight,        torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.31.attn_k.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.31.attn_v.weight,        torch.float16 --> Q8_0, shape = {4096, 1024}\n", "INFO:hf-to-gguf:blk.31.attn_output.weight,   torch.float16 --> Q8_0, shape = {4096, 4096}\n", "INFO:hf-to-gguf:blk.31.ffn_gate.weight,      torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:blk.31.ffn_up.weight,        torch.float16 --> Q8_0, shape = {4096, 14336}\n", "INFO:hf-to-gguf:gguf: loading model part 'pytorch_model-00004-of-00004.bin'\n", "INFO:hf-to-gguf:blk.31.ffn_down.weight,      torch.float16 --> Q8_0, shape = {14336, 4096}\n", "INFO:hf-to-gguf:blk.31.attn_norm.weight,     torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:blk.31.ffn_norm.weight,      torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:output_norm.weight,          torch.float16 --> F32, shape = {4096}\n", "INFO:hf-to-gguf:output.weight,               torch.float16 --> Q8_0, shape = {4096, 128256}\n", "INFO:hf-to-gguf:Set meta model\n", "INFO:hf-to-gguf:Set model parameters\n", "INFO:hf-to-gguf:gguf: context length = 8192\n", "INFO:hf-to-gguf:gguf: embedding length = 4096\n", "INFO:hf-to-gguf:gguf: feed forward length = 14336\n", "INFO:hf-to-gguf:gguf: head count = 32\n", "INFO:hf-to-gguf:gguf: key-value head count = 8\n", "INFO:hf-to-gguf:gguf: rope theta = 500000.0\n", "INFO:hf-to-gguf:gguf: rms norm epsilon = 1e-05\n", "INFO:hf-to-gguf:gguf: file type = 7\n", "INFO:hf-to-gguf:Set model tokenizer\n", "INFO:gguf.vocab:Adding 280147 merge(s).\n", "INFO:gguf.vocab:Setting special token type bos to 128000\n", "INFO:gguf.vocab:Setting special token type eos to 128001\n", "INFO:gguf.vocab:Setting special token type pad to 128255\n", "INFO:gguf.vocab:Setting chat_template to {{ 'Below are some instructions that describe some tasks. Write responses that appropriately complete each request.' }}{% for message in messages %}{% if message['role'] == 'user' %}{{ '\n", "\n", "### Instruction:\n", "' + message['content'] }}{% elif message['role'] == 'assistant' %}{{ '\n", "\n", "### Response:\n", "' + message['content'] + '<|end_of_text|>' }}{% else %}{{ raise_exception('Only user and assistant roles are supported!') }}{% endif %}{% endfor %}{% if add_generation_prompt %}{{ '\n", "\n", "### Response:\n", "' }}{% endif %}\n", "INFO:hf-to-gguf:Set model quantization version\n", "INFO:gguf.gguf_writer:Writing the following files:\n", "INFO:gguf.gguf_writer:model/unsloth.Q8_0.gguf: n_tensors = 291, total_size = 8.5G\n", "Writing: 100%|██████████| 8.53G/8.53G [03:05<00:00, 46.1Mbyte/s]\n", "INFO:hf-to-gguf:Model successfully exported to model/unsloth.Q8_0.gguf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unsloth: ##### The current model auto adds a BOS token.\n", "Unsloth: ##### We removed it in GGUF's chat template for you.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Conversion completed! Output location: ./model/unsloth.Q8_0.gguf\n", "Unsloth: Saved <PERSON><PERSON><PERSON> to model/Modelfile\n"]}], "source": ["# Save to 8bit Q8_0\n", "if True: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "J7lk6l0CuPXS"}, "source": ["We use `subprocess` to start `Ollama` up in a non blocking fashion! In your own desktop, you can simply open up a new `terminal` and type `ollama serve`, but in Colab, we have to use this hack!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mcP9omF_tN7Q"}, "outputs": [], "source": ["import subprocess\n", "\n", "subprocess.Popen([\"ollama\", \"serve\"])\n", "import time\n", "\n", "time.sleep(3)  # Wait for a few seconds for <PERSON><PERSON><PERSON> to load!"]}, {"cell_type": "markdown", "metadata": {"id": "md3PExRLRhOc"}, "source": ["`Ollama` needs a `Modelfile`, which specifies the model's prompt format. Let's print <PERSON><PERSON><PERSON><PERSON>'s auto generated one:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h82vfNigRhiz", "outputId": "bcd91437-d4cf-47de-8905-475e3fc4deec"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FROM {__FILE_LOCATION__}\n", "\n", "TEMPLATE \"\"\"Below are some instructions that describe some tasks. Write responses that appropriately complete each request.{{ if .Prompt }}\n", "\n", "### Instruction:\n", "{{ .Prompt }}{{ end }}\n", "\n", "### Response:\n", "{{ .Response }}<|end_of_text|>\"\"\"\n", "\n", "PARAMETER stop \"<|eot_id|>\"\n", "PARAMETER stop \"<|start_header_id|>\"\n", "PARAMETER stop \"<|end_header_id|>\"\n", "PARAMETER stop \"<|end_of_text|>\"\n", "PARAMETER stop \"<|reserved_special_token_\"\n", "PARAMETER temperature 1.5\n", "PARAMETER min_p 0.1\n"]}], "source": ["print(tokenizer._ollama_modelfile)"]}, {"cell_type": "markdown", "metadata": {"id": "j6cipBJBudxv"}, "source": ["We now will create an `Ollama` model called `unsloth_model` using the `Modelfile` which we auto generated!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SDTUJv_QiaVh", "outputId": "66fcae42-3792-4b52-eb42-d867d9f83d69"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25ltransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠦ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠏ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠼ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠴ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠧ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠇ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠋ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠙ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠹ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data ⠸ \u001b[?25h\u001b[?25l\u001b[2K\u001b[1Gtransferring model data \n", "creating new layer sha256:94728011329d3d304c40e235f81f1b75580e163036c07d98382dc5548d555a34 \n", "creating new layer sha256:95b5361453780fb5797ce5abfe9a330f5d33fdec13d2232ef1443ee0c3a86ecc \n", "creating new layer sha256:57675488fe3dd2a75da06ae97984c4ce6f382208e9d989c584b22ee395bab0d8 \n", "creating new layer sha256:e706dd26476841ded603017f70f5b99b5be356caa859878787bfc3898d547f08 \n", "writing manifest \n", "success \u001b[?25h\n"]}], "source": ["!ollama create unsloth_model -f ./model/Modelfile"]}, {"cell_type": "markdown", "metadata": {"id": "-KSoKTKQukba"}, "source": ["And now we can do inference on it via `Ollama`!\n", "\n", "You can also upload to `Ollama` and try the `Ollama` Desktop app by heading to https://www.ollama.com/"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rkp0uMrNpYaW", "outputId": "38bb3bd7-4a29-4c81-e319-388dcd96a449"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:04.241326628Z\",\"message\":{\"role\":\"assistant\",\"content\":\"The\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:04.465575479Z\",\"message\":{\"role\":\"assistant\",\"content\":\" next\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:04.760101468Z\",\"message\":{\"role\":\"assistant\",\"content\":\" number\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.051240606Z\",\"message\":{\"role\":\"assistant\",\"content\":\" in\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.376545126Z\",\"message\":{\"role\":\"assistant\",\"content\":\" the\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.515751946Z\",\"message\":{\"role\":\"assistant\",\"content\":\" <PERSON><PERSON><PERSON><PERSON>\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.658721744Z\",\"message\":{\"role\":\"assistant\",\"content\":\" sequence\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.795226527Z\",\"message\":{\"role\":\"assistant\",\"content\":\" after\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:05.923676364Z\",\"message\":{\"role\":\"assistant\",\"content\":\" \"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.053599585Z\",\"message\":{\"role\":\"assistant\",\"content\":\"8\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.187220374Z\",\"message\":{\"role\":\"assistant\",\"content\":\" is\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.316237671Z\",\"message\":{\"role\":\"assistant\",\"content\":\" \"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.448901764Z\",\"message\":{\"role\":\"assistant\",\"content\":\"13\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.585864644Z\",\"message\":{\"role\":\"assistant\",\"content\":\" (\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.712030586Z\",\"message\":{\"role\":\"assistant\",\"content\":\"the\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.835728964Z\",\"message\":{\"role\":\"assistant\",\"content\":\" sum\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:06.962898827Z\",\"message\":{\"role\":\"assistant\",\"content\":\" of\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.088064406Z\",\"message\":{\"role\":\"assistant\",\"content\":\" the\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.212942126Z\",\"message\":{\"role\":\"assistant\",\"content\":\" previous\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.336569966Z\",\"message\":{\"role\":\"assistant\",\"content\":\" two\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.46094096Z\",\"message\":{\"role\":\"assistant\",\"content\":\" numbers\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.593857726Z\",\"message\":{\"role\":\"assistant\",\"content\":\").\"},\"done\":false}\n", "{\"model\":\"unsloth_model\",\"created_at\":\"2024-10-01T06:47:07.741203726Z\",\"message\":{\"role\":\"assistant\",\"content\":\"\"},\"done_reason\":\"stop\",\"done\":true,\"total_duration\":3741960321,\"load_duration\":48967410,\"prompt_eval_count\":47,\"prompt_eval_duration\":150430000,\"eval_count\":23,\"eval_duration\":3499634000}\n"]}], "source": ["!curl http://localhost:11434/api/chat -d '{ \\\n", "    \"model\": \"unsloth_model\", \\\n", "    \"messages\": [ \\\n", "        { \"role\": \"user\", \"content\": \"Continue the <PERSON><PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\" } \\\n", "    ] \\\n", "    }'"]}, {"cell_type": "markdown", "metadata": {"id": "XnMbhp7KsKhr"}, "source": ["# ChatGPT interactive mode\n", "\n", "### ⭐ To run the finetuned model like in a ChatGPT style interface, first click the **| >_ |** button.\n", "![](https://raw.githubusercontent.com/unslothai/unsloth/nightly/images/Where_Terminal.png)\n", "\n", "---\n", "---\n", "---\n", "\n", "### ⭐ Then, type `ollama run unsloth_model`\n", "\n", "![](https://raw.githubusercontent.com/unslothai/unsloth/nightly/images/Terminal_Type.png)\n", "\n", "---\n", "---\n", "---\n", "### ⭐ And you have a ChatGPT style assistant!\n", "\n", "### Type any question you like and press `ENTER`. If you want to exit, hit `CTRL + D`\n", "![](https://raw.githubusercontent.com/unslothai/unsloth/nightly/images/Assistant.png)", "You can also use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"057825aa7df44d8eb9657aa8244c6a56": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1a501100f02d48bf8e6746ef3cd4047e", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b77913d6312d4608bdfa405fcaf16022", "value": 52002}}, "05e65597d7804e8eaa92d0df28a03b95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "05e9011d5fd44a219ef86ce93b400cdb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e16a04efe0564f13bda5d36487b79053", "max": 9085698, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9b918d8d5f674be88e05c0f3fc365b77", "value": 9085698}}, "09d4965cff9a457e91bc2ed788b37d98": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0a3dd767c8284aa899195fb798a5da74": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bdef7784bb9486091ef72fa30911f17": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d2d931811914399b33af27d741cdbd2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d5da7e017424aa0b7ff0d40abad1ecc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "12e4386de36246c28a70205ad43dc5d3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "14c60f02caa441b7a5ce09ffd770f5a9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "18b7f26e9cc74aa7a3f01b7af539c4ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_25397a2b86ef492c95c76b595f288e0d", "IPY_MODEL_a79fd1eef2c543f9bef6ff4c78b7b9ac", "IPY_MODEL_231d1e98fa704113bb70a4aef84ac30f"], "layout": "IPY_MODEL_e70183b7122c440eb0ad820a34ecd0b1"}}, "18c1a70d11484b20b0c129afda607eec": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19474c3fd67d4a0a9071442553eea424": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a51f8804e64e41f4b5f403bb48f97739", "placeholder": "​", "style": "IPY_MODEL_7d182abc791a4529a1d022c73f147743", "value": "tokenizer_config.json: 100%"}}, "19f7599c3e474401aa66b205209c5b05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_88f835bb9ef046ccbf32c41db3d752fc", "IPY_MODEL_c22e35a67d6546868f601f486faaa09f", "IPY_MODEL_ce52ffbce8164fd3b6f05f8e2f157bf8"], "layout": "IPY_MODEL_9f1f657d2e3c47ffabe49528cb850edc"}}, "1a501100f02d48bf8e6746ef3cd4047e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1c514cd5db254fd887496cfcea29b203": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1ce9fb4fb360443fbab1bc426b84e51e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e6693fba36444749c5190ae984b7761": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f8684abca0c45b4a8c36f854fc1e15b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a843939563ee45299fdd3f32986462c5", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8d96f84cdd14438baf1a5c5e4badd284", "value": 52002}}, "22edcfad17e9407d84b0d169818bd86f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "231d1e98fa704113bb70a4aef84ac30f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1e6693fba36444749c5190ae984b7761", "placeholder": "​", "style": "IPY_MODEL_5e1f0774a45141149ba3a9a62e2d50f1", "value": " 52002/52002 [00:03&lt;00:00, 17065.32 examples/s]"}}, "2342b7593577476ebc15b5b0b7c93d37": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2375ae0eb6c541b086ce027a7368e86a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25397a2b86ef492c95c76b595f288e0d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_36412dbe8646468dbc51dbac72ac305c", "placeholder": "​", "style": "IPY_MODEL_70bb803085a2457092d4642a3baf8f54", "value": "Extending conversations: 100%"}}, "25c75bff6c7d40148ef6311860013ae8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2748bc11780947038af15b94cdc0565a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_91b8e492c8e14eba9e9cd3c857e464ac", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2342b7593577476ebc15b5b0b7c93d37", "value": 52002}}, "29b8a584ae494edbaa5d1573bd2cc405": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2a27e2ac34184a1e88652c377c516d07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_848f2ab429a2449daa889bc5daf315d4", "IPY_MODEL_8f2008bd1c9e4979a69151c7056dbd68", "IPY_MODEL_6b01f9604205455f8d887d5f216d845d"], "layout": "IPY_MODEL_830101e5c8f7425886318bf7bf368128"}}, "2bba477ee49f425aac6be9a9e788c28b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b398a7d22a424d7d9ae67afdd95dcdcd", "placeholder": "​", "style": "IPY_MODEL_bc84a9ff36f143248ea6c2181f68ae02", "value": "Converting to ShareGPT: 100%"}}, "2d15261428824594908bd697644d61ed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_62191c10c14c4728bf85a293d29d37ce", "placeholder": "​", "style": "IPY_MODEL_ec56936c53984c85ade94df6ce443ad5", "value": " 52002/52002 [01:18&lt;00:00, 804.54 examples/s]"}}, "2ef094bd791b45169fb3de9eea3858f7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f8928b10d9a44e6b2c03fee74973d61": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32f125cf43cd40a4b232d80a4cd0594f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33a01d898ffc4a1a865b3c899df46981": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33f7cdb4b1484ff08d5882e38ed4ff33": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "36412dbe8646468dbc51dbac72ac305c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "39ac8df581254571bf15e52778564ad6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_890767e37b054db5b6b2df4bb2ad351d", "placeholder": "​", "style": "IPY_MODEL_e9c0e9a6545c49b9895f751b6c738974", "value": " 52002/52002 [00:02&lt;00:00, 19246.38 examples/s]"}}, "3bd2b3b05e214b8b918cb37bf8b87c4b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3bfbb892f9fe4c7baec8518c5fc27fd3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3c186c44416447daaa58e288416b69a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d8e6f188bf44dba981e445d8bea3402": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f30c80db3c54588b79e9352f57e783d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40ec2d13fcc14995b86d2f28a7ae9f37": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "43efda3bc6a5496aab27c30c99577825": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "43f6c10a2c3c478fba1a3784f75c6b84": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7474eb738ef64c8aa24c7dadcc296309", "placeholder": "​", "style": "IPY_MODEL_3c186c44416447daaa58e288416b69a4", "value": "Flattening the indices: 100%"}}, "44e1233f423e4fb7bc8e94c98449f524": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_97f798f38c9c4456ab6c5235d8af9e7d", "placeholder": "​", "style": "IPY_MODEL_33a01d898ffc4a1a865b3c899df46981", "value": "Flattening the indices: 100%"}}, "45165ac4e08f4103815205cc37690726": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45d8edc7203e4d2081170777d5eda422": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a7639181756463fa0d2bdcddb0ee460": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2bba477ee49f425aac6be9a9e788c28b", "IPY_MODEL_057825aa7df44d8eb9657aa8244c6a56", "IPY_MODEL_bc1a5a549e4845b2a549c4a5a077b100"], "layout": "IPY_MODEL_22edcfad17e9407d84b0d169818bd86f"}}, "4aab9fa7e38a49e59f87515e7bf415ee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_86f7a51468b248c8bb32c184f7cc4a69", "placeholder": "​", "style": "IPY_MODEL_43efda3bc6a5496aab27c30c99577825", "value": "model.safetensors: 100%"}}, "4c4bc99385eb49dd95ad4b740b949e55": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4dac566fd3ee404a8308026caf52e165": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4ebfd9be5b684da9a98c007084813595", "IPY_MODEL_1f8684abca0c45b4a8c36f854fc1e15b", "IPY_MODEL_be28376dcf0a4540872cabee59e87a64"], "layout": "IPY_MODEL_50ec7aa2658e4cdf878dbd4c98660072"}}, "4e76d402b4e4416e9b7e437af31ad66e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4ebfd9be5b684da9a98c007084813595": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4f808dffe6c24927b1b87b1319ccb311", "placeholder": "​", "style": "IPY_MODEL_948f186726e340d18abc9ac134630fcb", "value": "Merging columns: 100%"}}, "4ef743732002497395f3c2215713eb3e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_44e1233f423e4fb7bc8e94c98449f524", "IPY_MODEL_69cee937c2bb46528db052e775896eaa", "IPY_MODEL_f9c775663b86483abcedd9beba15662b"], "layout": "IPY_MODEL_9a301b9b504543008aa9cdb0ec09cdc2"}}, "4f78fb604b944c8da3f8f94a84779533": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4f808dffe6c24927b1b87b1319ccb311": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5064b0b9c6d7462190bcd6059a6475f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_19474c3fd67d4a0a9071442553eea424", "IPY_MODEL_cdc0128cc0ea40bc949df6412d179b78", "IPY_MODEL_b28fd98a29b04febac41b3f0169c72a1"], "layout": "IPY_MODEL_e0d4b6fedf2245e1b926c36bc7d792e6"}}, "50ec7aa2658e4cdf878dbd4c98660072": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "520dd5d7080d46e29213ab6958131a04": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "571fbb27971e4af0b958591a502e1249": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d792fa2e810a4aec841b96aed03f22e5", "IPY_MODEL_5a927d80c3d841768f8a32abc2b3da4e", "IPY_MODEL_60a2e5edb2424b91b551abaf8607d225"], "layout": "IPY_MODEL_aa461878d707464ea5728e857199fc1b"}}, "5a927d80c3d841768f8a32abc2b3da4e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f30c80db3c54588b79e9352f57e783d", "max": 350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6e059c44581046c6b7ac232fac94c6a4", "value": 350}}, "5ad5ec145642450bb2b3c3b2e0dada54": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e1f0774a45141149ba3a9a62e2d50f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5e8a3854fa4445b3b3a09a4db9a59de4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5edc00f7efce452e82b713120068d5ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9a05e6b44b2147c99a54764f01910b30", "IPY_MODEL_916ab8f1faf14632bdb3dead624eb21f", "IPY_MODEL_de5c470b9d654008bb0032b5aa4329ce"], "layout": "IPY_MODEL_602377d8635c49118036fbb6817b3cee"}}, "602377d8635c49118036fbb6817b3cee": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "60a2e5edb2424b91b551abaf8607d225": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b2f0019c68b4ca385fc9e9641e98006", "placeholder": "​", "style": "IPY_MODEL_3bfbb892f9fe4c7baec8518c5fc27fd3", "value": " 350/350 [00:00&lt;00:00, 21.6kB/s]"}}, "61e2f95ccaf84962a319224b652af287": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "62191c10c14c4728bf85a293d29d37ce": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63b2683594c44b858c937349019c6d0d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "665de9c511234f50b95f019e30553647": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7833e5ff39994bd8af56ab74ca5a6282", "IPY_MODEL_05e9011d5fd44a219ef86ce93b400cdb", "IPY_MODEL_9009e3ae003b4189bbfc8f6d20e425db"], "layout": "IPY_MODEL_feddd2b94ff640019168f085886042ca"}}, "669649c93c60494fac2bc7dfb4b43ac1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c37f06b87a9b41ba957ac760aad0ea48", "placeholder": "​", "style": "IPY_MODEL_9c9436e28d9c4cfdb9a251ce1ee4cb39", "value": " 52002/52002 [00:05&lt;00:00, 7224.96 examples/s]"}}, "66d602f02ee94a9fb9bb3ef6ebb157a2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6919b1082780411aa9310ceb152ea6e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_99e3b13809ca4873a44189f298084c59", "placeholder": "​", "style": "IPY_MODEL_701f88ce88074492a3a8b3470e8b22e7", "value": "generation_config.json: 100%"}}, "69c3b6e19c4d4abbb1e4a42a5be79e62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_18c1a70d11484b20b0c129afda607eec", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f90e7b0952a04982acc7b4e4128aa822", "value": 52002}}, "69cee937c2bb46528db052e775896eaa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ff4bb32f392421097f322b035208e6b", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e815c4a6522142068f09ed0abe3d5761", "value": 52002}}, "6b01f9604205455f8d887d5f216d845d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f8928b10d9a44e6b2c03fee74973d61", "placeholder": "​", "style": "IPY_MODEL_5e8a3854fa4445b3b3a09a4db9a59de4", "value": " 48.4M/48.4M [00:00&lt;00:00, 147MB/s]"}}, "6b2f0019c68b4ca385fc9e9641e98006": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6c96874fffb648708eba436836dec3c7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6e059c44581046c6b7ac232fac94c6a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6f6d146e3dc1428682d6f9c193f8bf19": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6919b1082780411aa9310ceb152ea6e0", "IPY_MODEL_8f9e359dcc0d4e4495eaa1f3920e803b", "IPY_MODEL_8a41d23dbbdb44f4aab5f453c4be58a4"], "layout": "IPY_MODEL_f06db96ad2414b679cc0b55f25ff16db"}}, "6ff4bb32f392421097f322b035208e6b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "701f88ce88074492a3a8b3470e8b22e7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "70bb803085a2457092d4642a3baf8f54": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7190e73a16b34eaabf2e3fc00f17246e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7474eb738ef64c8aa24c7dadcc296309": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7833e5ff39994bd8af56ab74ca5a6282": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_66d602f02ee94a9fb9bb3ef6ebb157a2", "placeholder": "​", "style": "IPY_MODEL_bad159b09d454000bba39dba607b8e60", "value": "tokenizer.json: 100%"}}, "7949c2ae13f44c609de332549305efd4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7d182abc791a4529a1d022c73f147743": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7f107e2a1d274a2f9bec4acce979d2da": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8061d66b27d548ea86d7ef10d2d784e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "830101e5c8f7425886318bf7bf368128": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "848f2ab429a2449daa889bc5daf315d4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a63b105990504c248cf4eebc42faaba8", "placeholder": "​", "style": "IPY_MODEL_2375ae0eb6c541b086ce027a7368e86a", "value": "(…)-00000-of-00001-6ef3991c06080e14.parquet: 100%"}}, "84f9e22160d74ec498f99e9c93b0c8bc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ce9fb4fb360443fbab1bc426b84e51e", "placeholder": "​", "style": "IPY_MODEL_61e2f95ccaf84962a319224b652af287", "value": "Map: 100%"}}, "86f7a51468b248c8bb32c184f7cc4a69": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "886ccb525cb149dfb49be917882f4ae3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_43f6c10a2c3c478fba1a3784f75c6b84", "IPY_MODEL_69c3b6e19c4d4abbb1e4a42a5be79e62", "IPY_MODEL_d3e8cc7d6db94605ab85f3a41c03a046"], "layout": "IPY_MODEL_0bdef7784bb9486091ef72fa30911f17"}}, "88f835bb9ef046ccbf32c41db3d752fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ef094bd791b45169fb3de9eea3858f7", "placeholder": "​", "style": "IPY_MODEL_29b8a584ae494edbaa5d1573bd2cc405", "value": "Generating train split: 100%"}}, "890767e37b054db5b6b2df4bb2ad351d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8a41d23dbbdb44f4aab5f453c4be58a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d8e6f188bf44dba981e445d8bea3402", "placeholder": "​", "style": "IPY_MODEL_a615710d2f72406f94586cf44320d1d8", "value": " 198/198 [00:00&lt;00:00, 14.4kB/s]"}}, "8c321f9d450c45259553f244cea495ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d96f84cdd14438baf1a5c5e4badd284": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8f2008bd1c9e4979a69151c7056dbd68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_09d4965cff9a457e91bc2ed788b37d98", "max": 48393562, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b5e79d185ecf496890db8818b0b60306", "value": 48393562}}, "8f9e359dcc0d4e4495eaa1f3920e803b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b91f7a6e50b5463bb560e45a71ee1998", "max": 198, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f7ef65c1668c498bb72ba3411301e116", "value": 198}}, "9009e3ae003b4189bbfc8f6d20e425db": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1c514cd5db254fd887496cfcea29b203", "placeholder": "​", "style": "IPY_MODEL_4f78fb604b944c8da3f8f94a84779533", "value": " 9.09M/9.09M [00:00&lt;00:00, 33.7MB/s]"}}, "90323c950496418d8540e5e9dfc7db31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "916ab8f1faf14632bdb3dead624eb21f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3bd2b3b05e214b8b918cb37bf8b87c4b", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b23419db6d7b41af9769c4c7535d5145", "value": 52002}}, "91b8e492c8e14eba9e9cd3c857e464ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9249cef6e8824525a1e183498d9d41ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "948f186726e340d18abc9ac134630fcb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "96aad118d29c4178b429612b2276ddfd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "97f798f38c9c4456ab6c5235d8af9e7d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "99e3b13809ca4873a44189f298084c59": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9a05e6b44b2147c99a54764f01910b30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e660f7079e4346e499da1ac196c96980", "placeholder": "​", "style": "IPY_MODEL_fa70fc9faa8a4580bf44e07f65e4d092", "value": "Flattening the indices: 100%"}}, "9a301b9b504543008aa9cdb0ec09cdc2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9b918d8d5f674be88e05c0f3fc365b77": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9bc07321be954d109b8f27d4b8f181dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9c9436e28d9c4cfdb9a251ce1ee4cb39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9f1f657d2e3c47ffabe49528cb850edc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a10a1f9bf41540ddb33c2b8dc181947e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a1d70bbc33a64c23a63f773abc5e22e6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_84f9e22160d74ec498f99e9c93b0c8bc", "IPY_MODEL_cb89bc91831b431bba8b1f1a14ac05ec", "IPY_MODEL_669649c93c60494fac2bc7dfb4b43ac1"], "layout": "IPY_MODEL_c821f5cc39924fde9fc9d381747bc5d3"}}, "a4df6bc79d93441283021cad1e4535e3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b522d2d378de44329f3224533318eb25", "max": 3385, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a10a1f9bf41540ddb33c2b8dc181947e", "value": 3385}}, "a51f8804e64e41f4b5f403bb48f97739": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a615710d2f72406f94586cf44320d1d8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a63b105990504c248cf4eebc42faaba8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a79fd1eef2c543f9bef6ff4c78b7b9ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_14c60f02caa441b7a5ce09ffd770f5a9", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ea6a29aacd804453863970f0e4bfb4ba", "value": 52002}}, "a843939563ee45299fdd3f32986462c5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a9a39684291d4eb1b7312640feae1462": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f107e2a1d274a2f9bec4acce979d2da", "placeholder": "​", "style": "IPY_MODEL_0d5da7e017424aa0b7ff0d40abad1ecc", "value": "Map (num_proc=2): 100%"}}, "aa461878d707464ea5728e857199fc1b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "af205ba151054c5fa8a5761b24dc8445": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9031d9400464ce4a500bd5b0b1b1fc7", "placeholder": "​", "style": "IPY_MODEL_33f7cdb4b1484ff08d5882e38ed4ff33", "value": " 5.70G/5.70G [00:43&lt;00:00, 300MB/s]"}}, "b03af9d3d3074c9fb43cc45d456b8e30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4aab9fa7e38a49e59f87515e7bf415ee", "IPY_MODEL_cba1bb5319c04c4c8adbc2304af6dde7", "IPY_MODEL_af205ba151054c5fa8a5761b24dc8445"], "layout": "IPY_MODEL_45d8edc7203e4d2081170777d5eda422"}}, "b13a48803a164e7caecfb06e2bde1556": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1e8d77d5bc34e99824c873e55cc49a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b23419db6d7b41af9769c4c7535d5145": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b28fd98a29b04febac41b3f0169c72a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c96874fffb648708eba436836dec3c7", "placeholder": "​", "style": "IPY_MODEL_8c321f9d450c45259553f244cea495ce", "value": " 50.6k/50.6k [00:00&lt;00:00, 2.69MB/s]"}}, "b398a7d22a424d7d9ae67afdd95dcdcd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3a8a71922f341758e1adf0f951208bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b522d2d378de44329f3224533318eb25": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5e79d185ecf496890db8818b0b60306": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b77913d6312d4608bdfa405fcaf16022": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b91f7a6e50b5463bb560e45a71ee1998": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bad159b09d454000bba39dba607b8e60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bc1a5a549e4845b2a549c4a5a077b100": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5ad5ec145642450bb2b3c3b2e0dada54", "placeholder": "​", "style": "IPY_MODEL_c5d04e56aaef4276b8da7b1d47eda54c", "value": " 52002/52002 [00:01&lt;00:00, 40478.66 examples/s]"}}, "bc84a9ff36f143248ea6c2181f68ae02": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "be28376dcf0a4540872cabee59e87a64": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e829599ab414438f9b65c3dbe151ab2f", "placeholder": "​", "style": "IPY_MODEL_520dd5d7080d46e29213ab6958131a04", "value": " 52002/52002 [00:01&lt;00:00, 60921.19 examples/s]"}}, "c22e35a67d6546868f601f486faaa09f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_63b2683594c44b858c937349019c6d0d", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cead1d73fc484c12ab7b7e4dcbfed58b", "value": 52002}}, "c2ec9b62f02b4813ab4d7a7c4609ec57": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c37f06b87a9b41ba957ac760aad0ea48": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c4df80b7320a4022aa91a8b3f0c49827": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d760752518e841a2a03f68914e21602f", "placeholder": "​", "style": "IPY_MODEL_90323c950496418d8540e5e9dfc7db31", "value": " 3.38k/3.38k [00:00&lt;00:00, 54.4kB/s]"}}, "c5d04e56aaef4276b8da7b1d47eda54c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c821f5cc39924fde9fc9d381747bc5d3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cb89bc91831b431bba8b1f1a14ac05ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbf4c274668c4feeb407501754eb7bf1", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9249cef6e8824525a1e183498d9d41ac", "value": 52002}}, "cba1bb5319c04c4c8adbc2304af6dde7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d2d931811914399b33af27d741cdbd2", "max": 5702746405, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7190e73a16b34eaabf2e3fc00f17246e", "value": 5702745862}}, "cbf4c274668c4feeb407501754eb7bf1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cdc0128cc0ea40bc949df6412d179b78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b13a48803a164e7caecfb06e2bde1556", "max": 50641, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9bc07321be954d109b8f27d4b8f181dd", "value": 50641}}, "ce52ffbce8164fd3b6f05f8e2f157bf8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40ec2d13fcc14995b86d2f28a7ae9f37", "placeholder": "​", "style": "IPY_MODEL_25c75bff6c7d40148ef6311860013ae8", "value": " 52002/52002 [00:01&lt;00:00, 51272.54 examples/s]"}}, "cead1d73fc484c12ab7b7e4dcbfed58b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cedf19b766684a1f8b2e40d1f97e89a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d08ffe495f9649cd99fd13e38fa09aa5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e0a12ecf2f7b4af59ad6b9c6806cffd2", "IPY_MODEL_2748bc11780947038af15b94cdc0565a", "IPY_MODEL_39ac8df581254571bf15e52778564ad6"], "layout": "IPY_MODEL_32f125cf43cd40a4b232d80a4cd0594f"}}, "d3e8cc7d6db94605ab85f3a41c03a046": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e76d402b4e4416e9b7e437af31ad66e", "placeholder": "​", "style": "IPY_MODEL_12e4386de36246c28a70205ad43dc5d3", "value": " 52002/52002 [00:00&lt;00:00, 160344.47 examples/s]"}}, "d7435502d95f4355b37d2f3332b057f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4c4bc99385eb49dd95ad4b740b949e55", "max": 52002, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f62d1a6521574459ae6fb1ebd71ad631", "value": 52002}}, "d760752518e841a2a03f68914e21602f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d792fa2e810a4aec841b96aed03f22e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2ec9b62f02b4813ab4d7a7c4609ec57", "placeholder": "​", "style": "IPY_MODEL_7949c2ae13f44c609de332549305efd4", "value": "special_tokens_map.json: 100%"}}, "db6e9ea5d4ac4ceeb6216973ab04fff2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dd0bd629b2384bd4aa4da791c3d6d254": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de5c470b9d654008bb0032b5aa4329ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_96aad118d29c4178b429612b2276ddfd", "placeholder": "​", "style": "IPY_MODEL_8061d66b27d548ea86d7ef10d2d784e8", "value": " 52002/52002 [00:11&lt;00:00, 4398.24 examples/s]"}}, "dee51ee264e24b809be18f6d286ae874": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a9a39684291d4eb1b7312640feae1462", "IPY_MODEL_d7435502d95f4355b37d2f3332b057f1", "IPY_MODEL_2d15261428824594908bd697644d61ed"], "layout": "IPY_MODEL_b3a8a71922f341758e1adf0f951208bb"}}, "e0a12ecf2f7b4af59ad6b9c6806cffd2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a3dd767c8284aa899195fb798a5da74", "placeholder": "​", "style": "IPY_MODEL_05e65597d7804e8eaa92d0df28a03b95", "value": "Standardizing format: 100%"}}, "e0d4b6fedf2245e1b926c36bc7d792e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e16a04efe0564f13bda5d36487b79053": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e660f7079e4346e499da1ac196c96980": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e70183b7122c440eb0ad820a34ecd0b1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7c4177a28d34f6da992178c8cb9313c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ffe387f8ca604781a7cd92a44c6dd508", "IPY_MODEL_a4df6bc79d93441283021cad1e4535e3", "IPY_MODEL_c4df80b7320a4022aa91a8b3f0c49827"], "layout": "IPY_MODEL_dd0bd629b2384bd4aa4da791c3d6d254"}}, "e815c4a6522142068f09ed0abe3d5761": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e829599ab414438f9b65c3dbe151ab2f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e9c0e9a6545c49b9895f751b6c738974": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ea6a29aacd804453863970f0e4bfb4ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ec56936c53984c85ade94df6ce443ad5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f06db96ad2414b679cc0b55f25ff16db": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f62d1a6521574459ae6fb1ebd71ad631": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f7ef65c1668c498bb72ba3411301e116": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f9031d9400464ce4a500bd5b0b1b1fc7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f90e7b0952a04982acc7b4e4128aa822": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f9c775663b86483abcedd9beba15662b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45165ac4e08f4103815205cc37690726", "placeholder": "​", "style": "IPY_MODEL_b1e8d77d5bc34e99824c873e55cc49a2", "value": " 52002/52002 [00:17&lt;00:00, 2977.05 examples/s]"}}, "fa70fc9faa8a4580bf44e07f65e4d092": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "feddd2b94ff640019168f085886042ca": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ffe387f8ca604781a7cd92a44c6dd508": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_db6e9ea5d4ac4ceeb6216973ab04fff2", "placeholder": "​", "style": "IPY_MODEL_cedf19b766684a1f8b2e40d1f97e89a4", "value": "README.md: 100%"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}