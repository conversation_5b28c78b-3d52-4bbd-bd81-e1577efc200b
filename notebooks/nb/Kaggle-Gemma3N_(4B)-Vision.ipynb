{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**NEW** Unsloth now supports training the new **gpt-oss** model from OpenAI! You can start finetune gpt-oss for free with our **[Colab notebook](https://x.com/UnslothAI/status/1953896997867729075)**!\n", "\n", "Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Gemma 3N Guide](https://docs.unsloth.ai/basics/gemma-3n-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nos.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n\n!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth\n!pip install --upgrade transformers==4.53.2 \"huggingface_hub>=0.34.0\" \"datasets>=3.4.1,<4.0.0\"\n"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "!pip install --no-deps --upgrade timm # Only for Gemma 3N"]}, {"cell_type": "markdown", "metadata": {"id": "GFOEZbP7ONMs"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 564, "referenced_widgets": ["2a5367c2e1de4294abe9427b53f99e8d", "d59736c1c0a5402582a58a839fa91b87", "f52d049ec6684bbd87ebd3999a0b48e1", "3aafc982e9f443e0b80f2537a602bedd", "39a70e2dca734c3b8a027d0cef56cb92", "f31b674a40f04b4b86be75ac61623baa", "af7ca6d7450e48f892c4fb2bee7f289a", "a0384f6e86e74d84aeddf381ded870ee", "baa0bcca526c4755a45ffd6883e0ab8a", "0f9280c0b0c44cf280ddf69a2b3e8316", "0b0a8cf628344a3cbe6c60464e9fe629", "263a7b18f3fe47bba64aeca0c7cab64c", "1c9a380cae0c4b8a92ca36334d47fb06", "40a6fcb1083644f1bfc07f3f88265e97", "302a9873d6a941c6ba9d6f16c410d822", "e35528d267bf45dbb677ebccaa403db8", "ba20039749864a5c8b15e941d106acea", "ae7e9987115846fc81a81b527f620eb1", "faf91bd96bef4b25909413a067462641", "acb5fdc26acc4de4a4b95f19ea9ecf6c", "ebf91030d01a4a519d8620bc68572a46", "aef4298577fd4c668bb9534adc9e6497", "7014fcc35ec048f5ae749835d66dae3b", "76efee305ed946488b3b7a5600215f68", "24db90ffb60a491b9f8d403564763fd2", "b3185444c6ed4f998e83f45661093832", "80f5961ac6fb4aedb02126f01339d5dd", "0acb52c1500b44c19724675f130a9498", "cd269df7e56a4ef292966349dff84adb", "b151a72e1d664c6fac753d6d02b7f480", "761994d216c64546bfe872225de97739", "8c74c2fd4c4a49fca99e998c488c9dcd", "a98ba46370c04cc4ac270191c2bfe659", "0e01bba2b2e7482b861945dc1445b80d", "bc48c0fba7224ef4a3edb402a2d3ef96", "6e4c15ecb43a4d73ac47e591454979c1", "44880b52a83d443f83e0a710a3f56d31", "3d7c5a6c8b954d418a6920ba3509b004", "41a6b61140854eefb11311475f76539e", "5b7df0e9308e4c6297c745ac47c4168f", "4aea6d898b354bbdbf64779488a62ddc", "91d007741b5942d9b4316dbf3fbc9ca7", "7431bad8395c4b7bb7a6df2506f6a50a", "4a2284240ded4df2b4a09bf5265e8249", "15f292e473da408ebf2c2a8d01c72ced", "3cd7c0686c844a34923b2d1ec394323e", "0de19aa760bb48fc90317148d788f1db", "8ca333f750204bd4adf3472e5c7d15cd", "dcc60498ee594a778b5362ecfce470bd", "8af9266d08b44ca8bfc1f140c72cda15", "c0b7a2ca6ccd46ceb801fbaa1fcd8285", "4949ae7012e441398a83fdaf7d35e0f1", "f03390aaff3d49fab85624c74a0f96da", "4f1baa363f0d461dbd68911920e434a0", "6aca132cfab4485d9798b727b8bcaa7f", "40251f2e13a9488d8b6ca9b951f470a6", "694e8b599e42401baba6647293ff95c8", "dc3b490e4d444b25bbd5fd40070195c9", "13daf9d0e0424201b087deb6e7159408", "8d9fd160902f46419385098151e8ce1c", "617fa5ab98594871bf60ad2f2a700049", "c761a2261fc14a308452fa6ab64a41c2", "01ba58393fac4597b42e9da1dfbcdd43", "24f6bcf39f3b4e789990887a88918e11", "76d0535dbff0416fa53bd6c81d19e9f2", "670d9c6a0506481b9df720c82f0e513f", "032eb4f3a29d46cb8e530fdb9172086c", "419585ce186a4ff78b8e3c2aeab750bc", "dd6ff6cc6fc14effb4217bd4b1cc7c37", "76562ec6f379483ea4ead56abda42999", "664793e3dc454e73b3243d17ce382c00", "be745bd4e323491fb5e9e4288ec655cd", "558b5631257a44c9b184c8d83fada92e", "dca6ddb029f9419794764159cb8cb185", "17125f492d7c45d281b947f7c0ba6910", "fd63d0938d0b44668bbb8ac4d21161b9", "a2aff28efd82496c9e632d422e0281b0", "a2cf66116c84481e90913327932cd0d0", "71c1d1d65fcd4662aabba5aa2dd7c7ad", "938e87216c2647f68818cbe3dd010621", "73fbc0af7d3f4975b9fc4d763e36ca0e", "77b5ece143b34095a417f39f329fbf6e", "391b7079441949729c1e5e30b7d49af4", "ad2be424a8c7420f961d0f77fa2b84ef", "395274f8f45b4ddc965bdf525b9e440a", "e43751895462487ba614486e0d706fb1", "5e611852de2b48fcab75cc2f65fdcbc1", "2c14d68bc5b444ceaae8189b831a04e5", "ec4933913c5e4e1ebdf838a8a867d604", "1c1340a6aebd4b098aeb5f525574761a", "7920eac5707c4458a9aa14857393bb82", "bcad85fa8e8d467b893e15a971ec22a6", "4dd03008135e40349de9d731b904a42e", "13baf175df014ec69656c32973bd271e", "6f5b149902c1421a8aa66054577ba1b1", "fdf03700578a4968a301547dbc8ce028", "2b965603be7046cbba7ee475fa4698fd", "fd0a23305f324d03a43750dc4504dd1f", "38b815b85b994c49bdb31b22367d0b44", "21e6ab84f8184bf4834d7e10bbbc447c", "f960554d76de4e61b81f2c595a6ca1c6", "4478c9661ebc4d169880c6c39a3dd68a", "61e781702d784300bb65f22b5461c20f", "7ab8146a24a64dec8d75f9b08c1e5492", "2f3197aafd8e4b489fcc5e49e469c7bd", "f66c6d99b37d4e739d8a7611b4b3ad5d", "6ad99ac3f02b4fda9180c4d98d647c0b", "c96cd07cfc19411eb50ca793e6ddd5e1", "423e1c16eb4b42829f56c940d175f3dd", "e5bb033de9c74b19bdec93efa99d6d0f", "37231f9ba24f40ad89a555ba8b1fcb24", "4be0579a244f431690cb73331d809764", "41cf9ff9d78445d9832703283966b2ce", "09d6f53f62374e8481bb8fb347f91fcd", "3d178102dd654db387a4b1e6d12067bd", "3754902f40964d1495c4b1e17ff5c9c8", "1fa532ec0a0742c2a0d300a8a162caf0", "ef830029c44144a0a9e43a6f862b02b6", "92b67b05b3a2488ba072b7e732a180be", "4ab652d3b1034a118dfc302c2fd5f75a", "a76990b601824790b72f5356d8250f7a", "d10b81910b754229bf0c9339c36ca7c3", "6ed85751db894b4c82504a3a1b8b7fe6", "58cc9cf92ce4424e880d25bac65e96e5", "fa6a8bbb6beb4dc2b0baeb4431b323ad", "e9d801c0f62d416abf651b6df25c64f0", "d76d4ae6a2ae484a9f81afef4b018488", "647a745e145547b7b6662c856cee3db9", "caf046bd0a4546f0af8a307c6e4f0dda", "05ce88f447bf4f8bacaf04a465247abe", "fdea634b45b14ce2b508d151749631d4", "d847fc71f3ab4de1aded0585a0b75267"]}, "id": "QmUBVEnvCDJv", "outputId": "a43ac7e4-d175-422a-e15e-3096f27edeee"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.7.11: Fast Gemma3N patching. Transformers: 4.53.3.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: Gemma3N does not support SDPA - switching to eager!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a5367c2e1de4294abe9427b53f99e8d", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "263a7b18f3fe47bba64aeca0c7cab64c", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00003.safetensors:   0%|          | 0.00/3.72G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7014fcc35ec048f5ae749835d66dae3b", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00003.safetensors:   0%|          | 0.00/4.99G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e01bba2b2e7482b861945dc1445b80d", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00003.safetensors:   0%|          | 0.00/1.15G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15f292e473da408ebf2c2a8d01c72ced", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "40251f2e13a9488d8b6ca9b951f470a6", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/191 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "032eb4f3a29d46cb8e530fdb9172086c", "version_major": 2, "version_minor": 0}, "text/plain": ["processor_config.json:   0%|          | 0.00/98.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a2cf66116c84481e90913327932cd0d0", "version_major": 2, "version_minor": 0}, "text/plain": ["preprocessor_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ec4933913c5e4e1ebdf838a8a867d604", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "21e6ab84f8184bf4834d7e10bbbc447c", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.70M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "37231f9ba24f40ad89a555ba8b1fcb24", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/33.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d10b81910b754229bf0c9339c36ca7c3", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/769 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastVisionModel # FastLanguageModel for LLMs\n", "import torch\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Llama-3.2-11B-Vision-Instruct-bnb-4bit\", # Llama 3.2 vision support\n", "    \"unsloth/Llama-3.2-11B-Vision-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-90B-Vision-Instruct-bnb-4bit\", # Can fit in a 80GB card!\n", "    \"unsloth/Llama-3.2-90B-Vision-bnb-4bit\",\n", "\n", "    \"unsloth/Pixtral-12B-2409-bnb-4bit\",              # Pixtral fits in 16GB!\n", "    \"unsloth/Pixtral-12B-Base-2409-bnb-4bit\",         # Pixtral base model\n", "\n", "    \"unsloth/Qwen2-VL-2B-Instruct-bnb-4bit\",          # Qwen2 VL support\n", "    \"unsloth/Qwen2-VL-7B-Instruct-bnb-4bit\",\n", "    \"unsloth/Qwen2-VL-72B-Instruct-bnb-4bit\",\n", "\n", "    \"unsloth/llava-v1.6-mistral-7b-hf-bnb-4bit\",      # Any Llava variant works!\n", "    \"unsloth/llava-1.5-7b-hf-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, processor = FastVisionModel.from_pretrained(\n", "    \"unsloth/gemma-3n-E4B\",\n", "    load_in_4bit = True, # Use 4bit to reduce memory use. False for 16bit LoRA.\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for long context\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters for parameter efficient fine-tuning, allowing us to train only 1% of all model parameters efficiently.\n", "\n", "**[NEW]** We also support fine-tuning only the vision component, only the language component, or both. Additionally, you can choose to fine-tune the attention modules, the MLP layers, or both!"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "80fb3df7-ae19-4257-fc04-7a232fe8bec5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Making `model.base_model.model.model.language_model` require gradients\n"]}], "source": ["model = FastVisionModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers     = True, # False if not finetuning vision layers\n", "    finetune_language_layers   = True, # False if not finetuning language layers\n", "    finetune_attention_modules = True, # False if not finetuning attention layers\n", "    finetune_mlp_modules       = True, # False if not finetuning MLP layers\n", "\n", "    r = 32,                           # The larger, the higher the accuracy, but might overfit\n", "    lora_alpha = 32,                  # Recommended alpha == r at least\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", "    use_rslora = False,               # We support rank stabilized LoRA\n", "    loftq_config = None,               # And LoftQ\n", "    target_modules = \"all-linear\",    # Optional now! Can specify a list if needed\n", "    modules_to_save=[\n", "        \"lm_head\",\n", "        \"embed_tokens\",\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We'll use a sampled dataset of handwritten math formulas. The objective is to convert these images into a computer-readable format—specifically LaTeX—so they can be rendered. This is particularly useful for complex expressions.\n", "\n", "You can access the dataset [here](https://huggingface.co/datasets/unsloth/LaTeX_OCR). The full dataset is [here](https://huggingface.co/datasets/linxy/LaTeX_OCR)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "LjY75GoYUCB8"}, "outputs": [], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"unsloth/LaTeX_OCR\", split = \"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "W1W2Qhsz6rUT"}, "source": ["Let's take an overview of the dataset. We'll examine the second image and its corresponding caption."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bfcSGwIb6p_R", "outputId": "53b12377-d2ad-47ff-fdfc-7453d5a651cc"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['image', 'text'],\n", "    num_rows: 68686\n", "})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 67}, "id": "uOLWY2936t1n", "outputId": "3a1e7a27-9c6d-4fe4-a9ca-ed2a446cd8be"}, "outputs": [{"data": {"image/jpeg": "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", "image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGB size=320x50>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[2][\"image\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "lXjfJr4W6z8P", "outputId": "a61261e8-0056-4ac2-c2c8-02df3797f678"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'H ^ { \\\\prime } = \\\\beta N \\\\int d \\\\lambda \\\\biggl \\\\{ \\\\frac { 1 } { 2 \\\\beta ^ { 2 } N ^ { 2 } } \\\\partial _ { \\\\lambda } \\\\zeta ^ { \\\\dagger } \\\\partial _ { \\\\lambda } \\\\zeta + V ( \\\\lambda ) \\\\zeta ^ { \\\\dagger } \\\\zeta \\\\biggr \\\\} \\\\ .'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[2][\"text\"]"]}, {"cell_type": "markdown", "metadata": {"id": "rKHxfZua1CrS"}, "source": ["We can also render LaTeX directly in the browser!"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "nPopsxAC1CrS", "outputId": "c8998182-7549-496f-960b-b105923740a6"}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\sigma ^ { \\mu } \\frac { \\lambda ^ { a } } { 2 } A _ { \\mu } ^ { a } .$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "\n", "latex = dataset[3][\"text\"]\n", "display(Math(latex))"]}, {"cell_type": "markdown", "metadata": {"id": "K9CBpiISFa6C"}, "source": ["To format the dataset, all vision fine-tuning tasks should follow this format:\n", "\n", "```python\n", "[\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"type\": \"text\", \"text\": instruction},\n", "            {\"type\": \"image\", \"image\": sample[\"image\"]},\n", "        ],\n", "    },\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\"type\": \"text\", \"text\": instruction},\n", "            {\"type\": \"image\", \"image\": sample[\"image\"]},\n", "        ],\n", "    },\n", "]\n", "```"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "oPXzJZzHEgXe"}, "outputs": [], "source": ["instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "def convert_to_conversation(sample):\n", "    conversation = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\"type\": \"text\", \"text\": instruction},\n", "                {\"type\": \"image\", \"image\": sample[\"image\"]},\n", "            ],\n", "        },\n", "        {\"role\": \"assistant\", \"content\": [{\"type\": \"text\", \"text\": sample[\"text\"]}]},\n", "    ]\n", "    return {\"messages\": conversation}\n", "pass"]}, {"cell_type": "markdown", "metadata": {"id": "FY-9u-OD6_gE"}, "source": ["Let's convert the dataset into the \"correct\" format for finetuning:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "gFW2qXIr7Ezy"}, "outputs": [], "source": ["converted_dataset = [convert_to_conversation(sample) for sample in dataset]"]}, {"cell_type": "markdown", "metadata": {"id": "ndDUB23CGAC5"}, "source": ["The first example is now structured like below:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gGFzmplrEy9I", "outputId": "7186328e-49e4-4deb-e927-3cbd9661f231"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'role': 'user',\n", "   'content': [{'type': 'text',\n", "     'text': 'Write the LaTeX representation for this image.'},\n", "    {'type': 'image',\n", "     'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=160x40>}]},\n", "  {'role': 'assistant',\n", "   'content': [{'type': 'text',\n", "     'text': '{ \\\\frac { N } { M } } \\\\in { \\\\bf Z } , { \\\\frac { M } { P } } \\\\in { \\\\bf Z } , { \\\\frac { P } { Q } } \\\\in { \\\\bf Z }'}]}]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["converted_dataset[0]"]}, {"cell_type": "markdown", "metadata": {"id": "MsRPBIb0JJ6c"}, "source": ["Lets take the Gemma 3n instruction chat template and use it in our base model"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "exoDVEvmJN-6"}, "outputs": [], "source": ["from unsloth import get_chat_template\n", "\n", "processor = get_chat_template(\n", "    processor,\n", "    \"gemma-3n\"\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "FecKS-dA82f5"}, "source": ["Before fine-tuning, let us evaluate the base model's performance. We do not expect strong results, as it has not encountered this chat template before."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vcat4UxA81vr", "outputId": "3d4e3034-44c2-456d-f3d5-84f5665e302c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  number=\"1\" \n", "  count_sym=1 \n", "  count_sym_arg=1 \n", "  count_sym_arg_arg=1 \n", "  count_sym_arg_arg_arg=1 \n", "  count_sym_arg_arg_arg_arg=1 \n", "  count_sym_arg_arg_arg_arg_arg=1 \n", "  count_sym_arg_arg_arg_arg_arg_arg=1 \n", "  count_sym_arg_arg_arg_arg_arg_arg_arg=1 \n", "  \n"]}], "source": ["FastVisionModel.for_inference(model)  # Enable for inference!\n", "\n", "image = dataset[2][\"image\"]\n", "instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [{\"type\": \"image\"}, {\"type\": \"text\", \"text\": instruction}],\n", "    }\n", "]\n", "input_text = processor.apply_chat_template(messages, add_generation_prompt=True)\n", "inputs = processor(\n", "    image,\n", "    input_text,\n", "    add_special_tokens=False,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(processor, skip_prompt=True)\n", "result = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                        use_cache=True, temperature = 1.0, top_p = 0.95, top_k = 64)"]}, {"cell_type": "markdown", "metadata": {"id": "FeAiMlQ71CrS"}, "source": ["You can see it's absolutely terrible! It doesn't follow instructions at all"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!\n", "\n", "We use our new `UnslothVisionDataCollator` which will help in our vision finetuning setup."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "95_Nn-89DhsL", "outputId": "4e82a708-86e0-404e-8860-6da40669642d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Model does not have a default image size - using 512\n"]}], "source": ["from unsloth.trainer import UnslothVisionDataCollator\n", "from trl import SFTTrainer, SFTConfig\n", "\n", "FastVisionModel.for_training(model) # Enable for training!\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model=model,\n", "    train_dataset=converted_dataset,\n", "    processing_class=processor.tokenizer,\n", "    data_collator=UnslothVisionDataCollator(model, processor),\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 1,\n", "        gradient_accumulation_steps = 4,\n", "        gradient_checkpointing = True,\n", "\n", "        # use reentrant checkpointing\n", "        gradient_checkpointing_kwargs = {\"use_reentrant\": False},\n", "        max_grad_norm = 0.3,              # max gradient norm based on QLoRA paper\n", "        warmup_ratio = 0.03,\n", "        max_steps = 60,\n", "        #num_train_epochs = 2,          # Set this instead of max_steps for full training runs\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        save_strategy=\"steps\",\n", "        optim = \"adamw_torch_fused\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"cosine\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\",             # For Weights and Biases\n", "\n", "        # You MUST put the below items for vision finetuning:\n", "        remove_unused_columns = False,\n", "        dataset_text_field = \"\",\n", "        dataset_kwargs = {\"skip_prepare_dataset\": True},\n", "        max_length = 2048,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "e9119560-5845-42ac-a05e-d5dfd067472b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.741 GB.\n", "5.416 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "210e8e04-0413-4856-f53e-12d9f0ecd727"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 68,686 | Num Epochs = 1 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 1 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (1 x 4 x 1) = 4\n", " \"-____-\"     Trainable parameters = 76,840,960 of 7,926,819,152 (0.97% trained)\n", "`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 10:44, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>6.085000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>6.677200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>5.742100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>6.613500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>6.242100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>6.310800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>5.654200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>5.624900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>6.585500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>3.044600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>2.539600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>2.751500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.639900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.910100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.739000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.867800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.474500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.567700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.457700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.367700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.409500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.381000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.438900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.301100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.244200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.387800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.198300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.259800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.409400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.259000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.254100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.934800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.350800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.341600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.238300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.194400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.159600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.250200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.414000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.501100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.209200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.078600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.311200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.281100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.148000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.282300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.285300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.197100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.278600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.274700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.208400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.247100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.177200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.206700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.294000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.654300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.152100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.212800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.397300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.195600</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "c07d7c52-f5cd-4f9d-fb43-96c20bca110d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["760.6053 seconds used for training.\n", "12.68 minutes used for training.\n", "Peak reserved memory = 6.061 GB.\n", "Peak reserved memory for training = 0.645 GB.\n", "Peak reserved memory % of max memory = 41.117 %.\n", "Peak reserved memory for training % of max memory = 4.376 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can modify the instruction and input—just leave the output blank.\n", "\n", "We'll use the best hyperparameters for inference on Gemma: `top_p=0.95`, `top_k=64`, and `temperature=1.0`."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "e37b8941-8343-43da-ee96-5805472f636a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ [ B _ { n } ^ { + } , b _ { 2 } ^ { + } ] = n B _ { n } ^ { + } , \\quad [ [ B _ { n } ^ { - } , b _ { 2 } ^ { + } ] , b _ { 2 } ^ { - } ] = n B _ { n } ^ { - } .\n", "<eos>\n"]}], "source": ["FastVisionModel.for_inference(model)  # Enable for inference!\n", "\n", "image = dataset[10][\"image\"]\n", "instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [{\"type\": \"image\"}, {\"type\": \"text\", \"text\": instruction}],\n", "    }\n", "]\n", "\n", "input_text = processor.apply_chat_template(messages, add_generation_prompt=True)\n", "\n", "inputs = processor(\n", "    image,\n", "    input_text,\n", "    add_special_tokens=False,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(processor, skip_prompt=True)\n", "result = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                        use_cache=True, temperature = 1.0, top_p = 0.95, top_k = 64)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, use Hugging Face’s `push_to_hub` for online saving, or `save_pretrained` for local storage.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "b7d7e525-c8a0-4269-80de-95f7dec559a4"}, "outputs": [{"data": {"text/plain": ["['lora_model/processor_config.json']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "processor.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# processor.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MKX_XKs_BNZR"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastVisionModel\n", "\n", "    model, processor = FastVisionModel.from_pretrained(\n", "        model_name=\"lora_model\",  # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit=True,  # Set to False for 16bit LoRA\n", "    )\n", "    FastVisionModel.for_inference(model)  # Enable for inference!\n", "\n", "FastVisionModel.for_inference(model)  # Enable for inference!\n", "\n", "sample = dataset[1]\n", "image = sample[\"image\"].convert(\"RGB\")\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": sample[\"text\"],\n", "            },\n", "            {\n", "                \"type\": \"image\",\n", "            },\n", "        ],\n", "    },\n", "]\n", "input_text = processor.apply_chat_template(messages, add_generation_prompt=True)\n", "inputs = processor(\n", "    image,\n", "    input_text,\n", "    add_special_tokens=False,\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(processor.tokenizer, skip_prompt=True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache=True, temperature = 1.0, top_p = 0.95, top_k = 64)"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Select ONLY 1 to save! (Both not needed!)\n", "\n", "# Save locally to 16bit\n", "if False: model.save_pretrained_merged(\"unsloth_finetune\", processor,)\n", "\n", "# To export and save to your Hugging Face account\n", "if False: model.push_to_hub_merged(\"YOUR_USERNAME/unsloth_finetune\", processor, token = \"PUT_HERE\")"]}, {"cell_type": "markdown", "metadata": {"id": "TSjNVDCYv-yr"}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"01ba58393fac4597b42e9da1dfbcdd43": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "032eb4f3a29d46cb8e530fdb9172086c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_419585ce186a4ff78b8e3c2aeab750bc", "IPY_MODEL_dd6ff6cc6fc14effb4217bd4b1cc7c37", "IPY_MODEL_76562ec6f379483ea4ead56abda42999"], "layout": "IPY_MODEL_664793e3dc454e73b3243d17ce382c00"}}, "05ce88f447bf4f8bacaf04a465247abe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "09d6f53f62374e8481bb8fb347f91fcd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4ab652d3b1034a118dfc302c2fd5f75a", "placeholder": "​", "style": "IPY_MODEL_a76990b601824790b72f5356d8250f7a", "value": " 33.4M/33.4M [00:00&lt;00:00, 2.18MB/s]"}}, "0acb52c1500b44c19724675f130a9498": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b0a8cf628344a3cbe6c60464e9fe629": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0de19aa760bb48fc90317148d788f1db": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4949ae7012e441398a83fdaf7d35e0f1", "max": 3, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f03390aaff3d49fab85624c74a0f96da", "value": 3}}, "0e01bba2b2e7482b861945dc1445b80d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bc48c0fba7224ef4a3edb402a2d3ef96", "IPY_MODEL_6e4c15ecb43a4d73ac47e591454979c1", "IPY_MODEL_44880b52a83d443f83e0a710a3f56d31"], "layout": "IPY_MODEL_3d7c5a6c8b954d418a6920ba3509b004"}}, "0f9280c0b0c44cf280ddf69a2b3e8316": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13baf175df014ec69656c32973bd271e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13daf9d0e0424201b087deb6e7159408": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_76d0535dbff0416fa53bd6c81d19e9f2", "placeholder": "​", "style": "IPY_MODEL_670d9c6a0506481b9df720c82f0e513f", "value": " 191/191 [00:00&lt;00:00, 18.3kB/s]"}}, "15f292e473da408ebf2c2a8d01c72ced": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3cd7c0686c844a34923b2d1ec394323e", "IPY_MODEL_0de19aa760bb48fc90317148d788f1db", "IPY_MODEL_8ca333f750204bd4adf3472e5c7d15cd"], "layout": "IPY_MODEL_dcc60498ee594a778b5362ecfce470bd"}}, "17125f492d7c45d281b947f7c0ba6910": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1c1340a6aebd4b098aeb5f525574761a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_13baf175df014ec69656c32973bd271e", "placeholder": "​", "style": "IPY_MODEL_6f5b149902c1421a8aa66054577ba1b1", "value": "tokenizer_config.json: "}}, "1c9a380cae0c4b8a92ca36334d47fb06": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba20039749864a5c8b15e941d106acea", "placeholder": "​", "style": "IPY_MODEL_ae7e9987115846fc81a81b527f620eb1", "value": "model-00001-of-00003.safetensors: 100%"}}, "1fa532ec0a0742c2a0d300a8a162caf0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "21e6ab84f8184bf4834d7e10bbbc447c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f960554d76de4e61b81f2c595a6ca1c6", "IPY_MODEL_4478c9661ebc4d169880c6c39a3dd68a", "IPY_MODEL_61e781702d784300bb65f22b5461c20f"], "layout": "IPY_MODEL_7ab8146a24a64dec8d75f9b08c1e5492"}}, "24db90ffb60a491b9f8d403564763fd2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b151a72e1d664c6fac753d6d02b7f480", "max": 4987233092, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_761994d216c64546bfe872225de97739", "value": 4987233092}}, "24f6bcf39f3b4e789990887a88918e11": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "263a7b18f3fe47bba64aeca0c7cab64c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1c9a380cae0c4b8a92ca36334d47fb06", "IPY_MODEL_40a6fcb1083644f1bfc07f3f88265e97", "IPY_MODEL_302a9873d6a941c6ba9d6f16c410d822"], "layout": "IPY_MODEL_e35528d267bf45dbb677ebccaa403db8"}}, "2a5367c2e1de4294abe9427b53f99e8d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d59736c1c0a5402582a58a839fa91b87", "IPY_MODEL_f52d049ec6684bbd87ebd3999a0b48e1", "IPY_MODEL_3aafc982e9f443e0b80f2537a602bedd"], "layout": "IPY_MODEL_39a70e2dca734c3b8a027d0cef56cb92"}}, "2b965603be7046cbba7ee475fa4698fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2c14d68bc5b444ceaae8189b831a04e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2f3197aafd8e4b489fcc5e49e469c7bd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "302a9873d6a941c6ba9d6f16c410d822": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ebf91030d01a4a519d8620bc68572a46", "placeholder": "​", "style": "IPY_MODEL_aef4298577fd4c668bb9534adc9e6497", "value": " 3.72G/3.72G [00:50&lt;00:00, 213MB/s]"}}, "37231f9ba24f40ad89a555ba8b1fcb24": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4be0579a244f431690cb73331d809764", "IPY_MODEL_41cf9ff9d78445d9832703283966b2ce", "IPY_MODEL_09d6f53f62374e8481bb8fb347f91fcd"], "layout": "IPY_MODEL_3d178102dd654db387a4b1e6d12067bd"}}, "3754902f40964d1495c4b1e17ff5c9c8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38b815b85b994c49bdb31b22367d0b44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "391b7079441949729c1e5e30b7d49af4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "395274f8f45b4ddc965bdf525b9e440a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "39a70e2dca734c3b8a027d0cef56cb92": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3aafc982e9f443e0b80f2537a602bedd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f9280c0b0c44cf280ddf69a2b3e8316", "placeholder": "​", "style": "IPY_MODEL_0b0a8cf628344a3cbe6c60464e9fe629", "value": " 370k/? [00:00&lt;00:00, 32.4MB/s]"}}, "3cd7c0686c844a34923b2d1ec394323e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8af9266d08b44ca8bfc1f140c72cda15", "placeholder": "​", "style": "IPY_MODEL_c0b7a2ca6ccd46ceb801fbaa1fcd8285", "value": "Loading checkpoint shards: 100%"}}, "3d178102dd654db387a4b1e6d12067bd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d7c5a6c8b954d418a6920ba3509b004": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40251f2e13a9488d8b6ca9b951f470a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_694e8b599e42401baba6647293ff95c8", "IPY_MODEL_dc3b490e4d444b25bbd5fd40070195c9", "IPY_MODEL_13daf9d0e0424201b087deb6e7159408"], "layout": "IPY_MODEL_8d9fd160902f46419385098151e8ce1c"}}, "40a6fcb1083644f1bfc07f3f88265e97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_faf91bd96bef4b25909413a067462641", "max": 3723418087, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_acb5fdc26acc4de4a4b95f19ea9ecf6c", "value": 3723418087}}, "419585ce186a4ff78b8e3c2aeab750bc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_be745bd4e323491fb5e9e4288ec655cd", "placeholder": "​", "style": "IPY_MODEL_558b5631257a44c9b184c8d83fada92e", "value": "processor_config.json: 100%"}}, "41a6b61140854eefb11311475f76539e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "41cf9ff9d78445d9832703283966b2ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef830029c44144a0a9e43a6f862b02b6", "max": 33442553, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_92b67b05b3a2488ba072b7e732a180be", "value": 33442553}}, "423e1c16eb4b42829f56c940d175f3dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4478c9661ebc4d169880c6c39a3dd68a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6ad99ac3f02b4fda9180c4d98d647c0b", "max": 4696020, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c96cd07cfc19411eb50ca793e6ddd5e1", "value": 4696020}}, "44880b52a83d443f83e0a710a3f56d31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7431bad8395c4b7bb7a6df2506f6a50a", "placeholder": "​", "style": "IPY_MODEL_4a2284240ded4df2b4a09bf5265e8249", "value": " 1.15G/1.15G [00:08&lt;00:00, 150MB/s]"}}, "4949ae7012e441398a83fdaf7d35e0f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a2284240ded4df2b4a09bf5265e8249": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4ab652d3b1034a118dfc302c2fd5f75a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4aea6d898b354bbdbf64779488a62ddc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4be0579a244f431690cb73331d809764": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3754902f40964d1495c4b1e17ff5c9c8", "placeholder": "​", "style": "IPY_MODEL_1fa532ec0a0742c2a0d300a8a162caf0", "value": "tokenizer.json: 100%"}}, "4dd03008135e40349de9d731b904a42e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f1baa363f0d461dbd68911920e434a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "558b5631257a44c9b184c8d83fada92e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "58cc9cf92ce4424e880d25bac65e96e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_caf046bd0a4546f0af8a307c6e4f0dda", "max": 769, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_05ce88f447bf4f8bacaf04a465247abe", "value": 769}}, "5b7df0e9308e4c6297c745ac47c4168f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5e611852de2b48fcab75cc2f65fdcbc1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "617fa5ab98594871bf60ad2f2a700049": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61e781702d784300bb65f22b5461c20f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_423e1c16eb4b42829f56c940d175f3dd", "placeholder": "​", "style": "IPY_MODEL_e5bb033de9c74b19bdec93efa99d6d0f", "value": " 4.70M/4.70M [00:00&lt;00:00, 279kB/s]"}}, "647a745e145547b7b6662c856cee3db9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "664793e3dc454e73b3243d17ce382c00": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "670d9c6a0506481b9df720c82f0e513f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "694e8b599e42401baba6647293ff95c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_617fa5ab98594871bf60ad2f2a700049", "placeholder": "​", "style": "IPY_MODEL_c761a2261fc14a308452fa6ab64a41c2", "value": "generation_config.json: 100%"}}, "6aca132cfab4485d9798b727b8bcaa7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6ad99ac3f02b4fda9180c4d98d647c0b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6e4c15ecb43a4d73ac47e591454979c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4aea6d898b354bbdbf64779488a62ddc", "max": 1148535480, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_91d007741b5942d9b4316dbf3fbc9ca7", "value": 1148535480}}, "6ed85751db894b4c82504a3a1b8b7fe6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d76d4ae6a2ae484a9f81afef4b018488", "placeholder": "​", "style": "IPY_MODEL_647a745e145547b7b6662c856cee3db9", "value": "special_tokens_map.json: 100%"}}, "6f5b149902c1421a8aa66054577ba1b1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7014fcc35ec048f5ae749835d66dae3b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_76efee305ed946488b3b7a5600215f68", "IPY_MODEL_24db90ffb60a491b9f8d403564763fd2", "IPY_MODEL_b3185444c6ed4f998e83f45661093832"], "layout": "IPY_MODEL_80f5961ac6fb4aedb02126f01339d5dd"}}, "71c1d1d65fcd4662aabba5aa2dd7c7ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_391b7079441949729c1e5e30b7d49af4", "placeholder": "​", "style": "IPY_MODEL_ad2be424a8c7420f961d0f77fa2b84ef", "value": "preprocessor_config.json: "}}, "73fbc0af7d3f4975b9fc4d763e36ca0e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e611852de2b48fcab75cc2f65fdcbc1", "placeholder": "​", "style": "IPY_MODEL_2c14d68bc5b444ceaae8189b831a04e5", "value": " 1.09k/? [00:00&lt;00:00, 67.8kB/s]"}}, "7431bad8395c4b7bb7a6df2506f6a50a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "761994d216c64546bfe872225de97739": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "76562ec6f379483ea4ead56abda42999": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fd63d0938d0b44668bbb8ac4d21161b9", "placeholder": "​", "style": "IPY_MODEL_a2aff28efd82496c9e632d422e0281b0", "value": " 98.0/98.0 [00:00&lt;00:00, 5.49kB/s]"}}, "76d0535dbff0416fa53bd6c81d19e9f2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "76efee305ed946488b3b7a5600215f68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0acb52c1500b44c19724675f130a9498", "placeholder": "​", "style": "IPY_MODEL_cd269df7e56a4ef292966349dff84adb", "value": "model-00002-of-00003.safetensors: 100%"}}, "77b5ece143b34095a417f39f329fbf6e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7920eac5707c4458a9aa14857393bb82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fdf03700578a4968a301547dbc8ce028", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2b965603be7046cbba7ee475fa4698fd", "value": 1}}, "7ab8146a24a64dec8d75f9b08c1e5492": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80f5961ac6fb4aedb02126f01339d5dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8af9266d08b44ca8bfc1f140c72cda15": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c74c2fd4c4a49fca99e998c488c9dcd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ca333f750204bd4adf3472e5c7d15cd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4f1baa363f0d461dbd68911920e434a0", "placeholder": "​", "style": "IPY_MODEL_6aca132cfab4485d9798b727b8bcaa7f", "value": " 3/3 [00:44&lt;00:00, 13.10s/it]"}}, "8d9fd160902f46419385098151e8ce1c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91d007741b5942d9b4316dbf3fbc9ca7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "92b67b05b3a2488ba072b7e732a180be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "938e87216c2647f68818cbe3dd010621": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_395274f8f45b4ddc965bdf525b9e440a", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e43751895462487ba614486e0d706fb1", "value": 1}}, "a0384f6e86e74d84aeddf381ded870ee": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "a2aff28efd82496c9e632d422e0281b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a2cf66116c84481e90913327932cd0d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_71c1d1d65fcd4662aabba5aa2dd7c7ad", "IPY_MODEL_938e87216c2647f68818cbe3dd010621", "IPY_MODEL_73fbc0af7d3f4975b9fc4d763e36ca0e"], "layout": "IPY_MODEL_77b5ece143b34095a417f39f329fbf6e"}}, "a76990b601824790b72f5356d8250f7a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a98ba46370c04cc4ac270191c2bfe659": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "acb5fdc26acc4de4a4b95f19ea9ecf6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ad2be424a8c7420f961d0f77fa2b84ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ae7e9987115846fc81a81b527f620eb1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aef4298577fd4c668bb9534adc9e6497": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "af7ca6d7450e48f892c4fb2bee7f289a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b151a72e1d664c6fac753d6d02b7f480": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3185444c6ed4f998e83f45661093832": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8c74c2fd4c4a49fca99e998c488c9dcd", "placeholder": "​", "style": "IPY_MODEL_a98ba46370c04cc4ac270191c2bfe659", "value": " 4.99G/4.99G [01:32&lt;00:00, 39.9MB/s]"}}, "ba20039749864a5c8b15e941d106acea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "baa0bcca526c4755a45ffd6883e0ab8a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bc48c0fba7224ef4a3edb402a2d3ef96": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_41a6b61140854eefb11311475f76539e", "placeholder": "​", "style": "IPY_MODEL_5b7df0e9308e4c6297c745ac47c4168f", "value": "model-00003-of-00003.safetensors: 100%"}}, "bcad85fa8e8d467b893e15a971ec22a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fd0a23305f324d03a43750dc4504dd1f", "placeholder": "​", "style": "IPY_MODEL_38b815b85b994c49bdb31b22367d0b44", "value": " 1.20M/? [00:00&lt;00:00, 23.4MB/s]"}}, "be745bd4e323491fb5e9e4288ec655cd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c0b7a2ca6ccd46ceb801fbaa1fcd8285": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c761a2261fc14a308452fa6ab64a41c2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c96cd07cfc19411eb50ca793e6ddd5e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "caf046bd0a4546f0af8a307c6e4f0dda": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd269df7e56a4ef292966349dff84adb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d10b81910b754229bf0c9339c36ca7c3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6ed85751db894b4c82504a3a1b8b7fe6", "IPY_MODEL_58cc9cf92ce4424e880d25bac65e96e5", "IPY_MODEL_fa6a8bbb6beb4dc2b0baeb4431b323ad"], "layout": "IPY_MODEL_e9d801c0f62d416abf651b6df25c64f0"}}, "d59736c1c0a5402582a58a839fa91b87": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f31b674a40f04b4b86be75ac61623baa", "placeholder": "​", "style": "IPY_MODEL_af7ca6d7450e48f892c4fb2bee7f289a", "value": "model.safetensors.index.json: "}}, "d76d4ae6a2ae484a9f81afef4b018488": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d847fc71f3ab4de1aded0585a0b75267": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dc3b490e4d444b25bbd5fd40070195c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_01ba58393fac4597b42e9da1dfbcdd43", "max": 191, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_24f6bcf39f3b4e789990887a88918e11", "value": 191}}, "dca6ddb029f9419794764159cb8cb185": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dcc60498ee594a778b5362ecfce470bd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dd6ff6cc6fc14effb4217bd4b1cc7c37": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dca6ddb029f9419794764159cb8cb185", "max": 98, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_17125f492d7c45d281b947f7c0ba6910", "value": 98}}, "e35528d267bf45dbb677ebccaa403db8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e43751895462487ba614486e0d706fb1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e5bb033de9c74b19bdec93efa99d6d0f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e9d801c0f62d416abf651b6df25c64f0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ebf91030d01a4a519d8620bc68572a46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ec4933913c5e4e1ebdf838a8a867d604": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1c1340a6aebd4b098aeb5f525574761a", "IPY_MODEL_7920eac5707c4458a9aa14857393bb82", "IPY_MODEL_bcad85fa8e8d467b893e15a971ec22a6"], "layout": "IPY_MODEL_4dd03008135e40349de9d731b904a42e"}}, "ef830029c44144a0a9e43a6f862b02b6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f03390aaff3d49fab85624c74a0f96da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f31b674a40f04b4b86be75ac61623baa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f52d049ec6684bbd87ebd3999a0b48e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a0384f6e86e74d84aeddf381ded870ee", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_baa0bcca526c4755a45ffd6883e0ab8a", "value": 1}}, "f66c6d99b37d4e739d8a7611b4b3ad5d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f960554d76de4e61b81f2c595a6ca1c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f3197aafd8e4b489fcc5e49e469c7bd", "placeholder": "​", "style": "IPY_MODEL_f66c6d99b37d4e739d8a7611b4b3ad5d", "value": "tokenizer.model: 100%"}}, "fa6a8bbb6beb4dc2b0baeb4431b323ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fdea634b45b14ce2b508d151749631d4", "placeholder": "​", "style": "IPY_MODEL_d847fc71f3ab4de1aded0585a0b75267", "value": " 769/769 [00:00&lt;00:00, 55.2kB/s]"}}, "faf91bd96bef4b25909413a067462641": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd0a23305f324d03a43750dc4504dd1f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd63d0938d0b44668bbb8ac4d21161b9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fdea634b45b14ce2b508d151749631d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fdf03700578a4968a301547dbc8ce028": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}