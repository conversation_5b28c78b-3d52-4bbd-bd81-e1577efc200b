{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Unsloth Training for Falcon H1\n", "\n", "This Notebook has been authored by TII Falcon Team.\n", "For more details on Falcon H1 series of models :\n", "1. [Official Page](https://tiiuae.github.io/Falcon-H1/)\n", "2. [blogpost](https://falcon-lm.github.io/blog/falcon-h1/)\n", "3. [Official github page ](https://github.com/tiiuae/Falcon-H1)\n", "4. [hf collection](https://huggingface.co/collections/tiiuae/falcon-h1-6819f2795bc406da60fab8df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "# Installs Unsloth, Xformers (Flash Attention) and all other packages!\n", "!pip install unsloth\n", "# Get latest Unsloth\n", "!pip uninstall unsloth -y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install \"unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git@main\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install vllm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install --force-reinstall git+https://github.com/huggingface/transformers.git "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install --no-build-isolation git+https://github.com/Dao-AILab/causal-conv1d.git@main\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install --no-build-isolation git+https://github.com/state-spaces/mamba.git@main"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install git+https://github.com/unslothai/unsloth-zoo.git"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import unsloth\n", "from unsloth import FastLanguageModel\n", "import torch\n", "import os\n", "os.environ['TRITON_JIT_DISABLE_OPT'] = '1' # Likely the most critical change\n", "\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"tiiuae/Falcon-H1-0.5B-Instruct\", # Choose any model from https://huggingface.co/collections/tiiuae/falcon-h1-6819f2795bc406da60fab8df\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure PEFT model\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r=64,\n", "    target_modules=[\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                    \"gate_proj\", \"up_proj\", \"down_proj\"], #<PERSON><PERSON> out_proj and conv1d layers should not be included here see https://github.com/huggingface/peft/pull/2562\n", "    lora_alpha= 32,\n", "    lora_dropout= 0.1,\n", "    use_gradient_checkpointing=False,\n", "    random_state=3407,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token\n", "def formatting_prompts_func(examples):\n", "    instructions = examples[\"instruction\"]\n", "    inputs       = examples[\"input\"]\n", "    outputs      = examples[\"output\"]\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        # Must add EOS_TOKEN, otherwise your generation will go on forever!\n", "        text = alpaca_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"yahma/alpaca-cleaned\", split = \"train\")\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from trl import SFTTrainer\n", "from transformers import TrainingArguments\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    packing = False, # Can make training 5x faster for short sequences.\n", "    args = TrainingArguments(\n", "        per_device_train_batch_size = 1,\n", "        gradient_accumulation_steps = 8,\n", "        warmup_steps = 5,\n", "        max_steps = 60,\n", "        learning_rate = 2e-4,\n", "        fp16 = not torch.cuda.is_bf16_supported(),\n", "        bf16 = torch.cuda.is_bf16_supported(),\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Show current memory stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"TRITON_DISABLE_LINE_INFO\"] = \"1\"\n", "trainer_stats = trainer.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Show final memory and time stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "mlm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}