{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%env UNSLOTH_RETURN_LOGITS=1 # Run this to disable CCE since it is not supported for CPT"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QmUBVEnvCDJv"}, "outputs": [], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-v0.3-bnb-4bit\",      # New Mistral v3 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/llama-3-8b-bnb-4bit\",           # Llama-3 15 trillion tokens model 2x faster!\n", "    \"unsloth/llama-3-8b-Instruct-bnb-4bit\",\n", "    \"unsloth/llama-3-70b-bnb-4bit\",\n", "    \"unsloth/Phi-3-mini-4k-instruct\",        # Phi-3 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\",             # Gemma 2.2x faster!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/mistral-7b-v0.3\", # Choose ANY! eg teknium/OpenHermes-2.5-Mistral-7B\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!\n", "\n", "We also add `embed_tokens` and `lm_head` to allow the model to learn out of distribution data."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6bZsfBuZDeCL"}, "outputs": [], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 128, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",\n", "\n", "                      \"embed_tokens\", \"lm_head\",], # Add for continual pretraining\n", "    lora_alpha = 32,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = True,   # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the Korean subset of the [Wikipedia dataset](https://huggingface.co/datasets/wikimedia/wikipedia) to first continually pretrain the model. You can use **any language** you like! Go to [Wikipedia's List of Languages](https://en.wikipedia.org/wiki/List_of_Wikipedias) to find your own language!\n", "\n", "**[NOTE]** To train only on completions (ignoring the user's input) read TRL's docs [here](https://huggingface.co/docs/trl/sft_trainer#train-on-completions-only).\n", "\n", "**[NOTE]** Remember to add the **EOS_TOKEN** to the tokenized output!! Otherwise you'll get infinite generations!\n", "\n", "If you want to use the `llama-3` template for ShareGPT datasets, try our conversational [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_v0.3_(7B)-Conversational.ipynb)\n", "\n", "For text completions like novel writing, try this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_(7B)-Text_Completion.ipynb).\n", "\n", "**[NOTE]** Use https://translate.google.com to translate from English to Korean!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LjY75GoYUCB8"}, "outputs": [], "source": ["# Wikipedia provides a title and an article text.\n", "# Use https://translate.google.com!\n", "_wikipedia_prompt = \"\"\"Wikipedia Article\n", "### Title: {}\n", "\n", "### Article:\n", "{}\"\"\"\n", "# becomes:\n", "wikipedia_prompt = \"\"\"위키피디아 기사\n", "### 제목: {}\n", "\n", "### 기사:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token # Must add EOS_TOKEN\n", "def formatting_prompts_func(examples):\n", "    titles = examples[\"title\"]\n", "    texts  = examples[\"text\"]\n", "    outputs = []\n", "    for title, text in zip(titles, texts):\n", "        # Must add EOS_TOKEN, otherwise your generation will go on forever!\n", "        text = wikipedia_prompt.format(title, text) + EOS_TOKEN\n", "        outputs.append(text)\n", "    return { \"text\" : outputs, }\n", "pass"]}, {"cell_type": "markdown", "metadata": {"id": "rw0tuebw4Ppe"}, "source": ["We only use 1% of the dataset to speed things up! Use more for longer runs!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EsKrpkza3VB3"}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"wikimedia/wikipedia\", \"20231101.ko\", split = \"train\",)\n", "\n", "# We select 1% of the data to make training faster!\n", "dataset = dataset.train_test_split(train_size = 0.01)[\"train\"]\n", "\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Continued Pretraining\n", "Now let's use Unsloth's `UnslothTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 20 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`.\n", "\n", "Also set `embedding_learning_rate` to be a learning rate at least 2x or 10x smaller than `learning_rate` to make continual pretraining work!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "95_Nn-89DhsL"}, "outputs": [], "source": ["from transformers import TrainingArguments\n", "from unsloth import UnslothTrainer, UnslothTrainingArguments\n", "\n", "trainer = <PERSON><PERSON><PERSON>hTrainer(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 4,\n", "\n", "    args = UnslothTrainingArguments(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 8,\n", "\n", "        # Use warmup_ratio and num_train_epochs for longer runs!\n", "        max_steps = 120,\n", "        warmup_steps = 10,\n", "        # warmup_ratio = 0.1,\n", "        # num_train_epochs = 1,\n", "\n", "        # Select a 2 to 10x smaller learning rate for the embedding matrices!\n", "        learning_rate = 5e-5,\n", "        embedding_learning_rate = 1e-5,\n", "\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "2ejIt2xSNKKp"}, "outputs": [], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yqxqAZ7KJ4oL"}, "outputs": [], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "markdown", "metadata": {"id": "R9dRBJZulavZ"}, "source": ["### Instruction Finetuning\n", "\n", "We now use the [Alpaca in GPT4 Dataset](https://huggingface.co/datasets/FreedomIntelligence/alpaca-gpt4-korean) but translated in Korean!\n", "\n", "Go to [vicgalle/alpaca-gpt4](https://huggingface.co/datasets/vicgalle/alpaca-gpt4) for the original GPT4 dataset for Alpaca or [MultilingualSIFT project](https://github.com/FreedomIntelligence/MultilingualSIFT) for other translations of the Alpaca dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1oNjUwxOyG8C"}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "alpaca_dataset = load_dataset(\"FreedomIntelligence/alpaca-gpt4-korean\", split=\"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "kmFG41nFytgi"}, "source": ["We print 1 example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QvTfIKaUQxQ5"}, "outputs": [], "source": ["print(alpaca_dataset[0])"]}, {"cell_type": "markdown", "metadata": {"id": "OO8UY34ql2vJ"}, "source": ["We again use https://translate.google.com/ to translate the Alpaca format into Korean"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B2tv8AEhPTu6"}, "outputs": [], "source": ["_alpaca_prompt = \"\"\"Below is an instruction that describes a task. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "# Becomes:\n", "alpaca_prompt = \"\"\"다음은 작업을 설명하는 명령입니다. 요청을 적절하게 완료하는 응답을 작성하세요.\n", "\n", "### 지침:\n", "{}\n", "\n", "### 응답:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token # Must add EOS_TOKEN\n", "def formatting_prompts_func(conversations):\n", "    texts = []\n", "    conversations = conversations[\"conversations\"]\n", "    for convo in conversations:\n", "        # Must add EOS_TOKEN, otherwise your generation will go on forever!\n", "        text = alpaca_prompt.format(convo[0][\"value\"], convo[1][\"value\"]) + EOS_TOKEN\n", "        texts.append(text)\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "alpaca_dataset = alpaca_dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "mxWWh8xsl9XT"}, "source": ["We again employ `Unsloth<PERSON>rainer` and do instruction finetuning!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1Zul21NSRRLP"}, "outputs": [], "source": ["from transformers import TrainingArguments\n", "from unsloth import UnslothTrainer, UnslothTrainingArguments\n", "\n", "trainer = <PERSON><PERSON><PERSON>hTrainer(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = alpaca_dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 8,\n", "\n", "    args = UnslothTrainingArguments(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 8,\n", "\n", "        # Use num_train_epochs and warmup_ratio for longer runs!\n", "        max_steps = 120,\n", "        warmup_steps = 10,\n", "        # warmup_ratio = 0.1,\n", "        # num_train_epochs = 1,\n", "\n", "        # Select a 2 to 10x smaller learning rate for the embedding matrices!\n", "        learning_rate = 5e-5,\n", "        embedding_learning_rate = 1e-5,\n", "\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.00,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DIO7c1FoRe-X"}, "outputs": [], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "pCqnaKmlO1U9"}, "outputs": [], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!\n", "\n", "Remember to use https://translate.google.com/!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B_GRpqmUiFRj"}, "outputs": [], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        # \"Continue the <PERSON><PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\", # instruction\n", "        \"피보나치 수열을 계속하세요: 1, 1, 2, 3, 5, 8,\", # instruction\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**inputs, max_new_tokens = 64, use_cache = True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "CrSvZObor0lY"}, "source": [" You can also use a `TextStreamer` for continuous inference - so you can see the generation token by token, instead of waiting the whole time!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "e2pEuRb1r2Vg"}, "outputs": [], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        # \"What is Korean music like?\"\n", "        \"한국음악은 어떤가요?\", # instruction\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128)"]}, {"cell_type": "markdown", "metadata": {"id": "acKgU7NMauCp"}, "source": ["By using https://translate.google.com/ we get\n", "```\n", "Korean music is classified into many types of music genres.\n", "\n", "This genre is classified into different music genres such as pop songs,\n", "\n", "rock songs, classical songs and pop songs, music groups consisting of drums, fans, instruments and singers\n", "```"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upcOlWe7A1vc"}, "outputs": [], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MKX_XKs_BNZR"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name=\"lora_model\",  # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length=max_seq_length,\n", "        dtype=dtype,\n", "        load_in_4bit=load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model)  # Enable native 2x faster inference\n", "\n", "# alpaca_prompt = You MUST copy from above!\n", "\n", "inputs = tokenizer(\n", "    [\n", "        alpaca_prompt.format(\n", "            # \"Describe the planet Earth extensively.\", # instruction\n", "            \"지구를 광범위하게 설명하세요.\",\n", "            \"\",  # output - leave this blank for generation!\n", "        ),\n", "    ],\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(\n", "    **inputs, streamer=text_streamer, max_new_tokens=128, repetition_penalty=0.1\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "twNf4NXhmLqj"}, "source": ["By using https://translate.google.com/ we get\n", "```\n", "Earth refers to all things including natural disasters such as local derailment\n", "\n", "and local depletion that occur in one space along with the suppression of water, gases, and living things.\n", "\n", "Most of the Earth's water comes from oceans, atmospheric water, underground water layers, and rivers and rivers.\n", "```\n", "\n", "Yikes the language model is a bit whacky! Change the temperature and using sampling will definitely make the output much better!"]}, {"cell_type": "markdown", "metadata": {"id": "QQMjaNrjsU5_"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yFfaXG0WsQuE"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\",  # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit=load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FqfebeAdT073"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q5_k_m\", token = \"\")"]}], "metadata": {"accelerator": "GPU", "colab": {"cell_execution_strategy": "setup", "gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}